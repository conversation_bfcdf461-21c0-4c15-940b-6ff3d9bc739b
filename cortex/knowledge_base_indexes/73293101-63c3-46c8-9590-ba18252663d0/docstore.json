{"docstore/metadata": {"d404b096-8b45-4f72-b98c-151fec5f211f": {"doc_hash": "fed439483c0cc24384562934cb28505168d786fa1acbc5769afd1c0e12835bcd"}, "39ebc5fe-82b5-435b-9c22-142e60944d16": {"doc_hash": "8a5494c441ece48633a53b75daf0b05311652c44f689c4ba9733268764f59dcc"}, "500036aa-42fd-454f-9b65-90018f5e7ede": {"doc_hash": "91080773e4183c3fb155cf64a6c0162acb64dec6fe7df206a999bf678614c36b"}, "df8d0780-ab14-456e-a12e-41875863474f": {"doc_hash": "0793cfd15db579cb4e293e94f2bc8560749057301bd65ac3e7e32502a4da3921"}, "e465d2f7-7e58-4ea3-8948-5f4686cea1fe": {"doc_hash": "b0b36b00f4924589f7716b0e660006c6f5a576940cfc53283c232b07727b2660"}, "5e1ce493-cd43-426d-af1d-b8ae56047e14": {"doc_hash": "d44a3ea2fc59d7eb8f9fa036a2871a8178290980950207be3df0ad42ab132548"}, "6cbcf6b1-0b15-4a06-8e25-c56a2bf61040": {"doc_hash": "210ad9ca2605aa5359af49ece2a2e89183f9592a31acbc1859cc1afd5e5f586a"}, "98596c66-a30a-43c0-8186-76ac785b6b08": {"doc_hash": "d1fa6f7372ce8fb79760860e0cf454b837d8276b706a6b2863f4fcef925eb41b"}, "e6af940b-30fb-417b-9913-b32daca16472": {"doc_hash": "84765eaa2bf28ee0ed61f27d804943888b25195481617e0254c86ecfe75183c0"}, "0eb9625f-9d71-41d6-a808-5cc3d8891fd5": {"doc_hash": "05c0e76ba0732aee02b15c93ae3d99c72afd7dd07a2c8d70c517fb0175155440"}, "54ed81a0-7609-4bb8-939a-b4914ad29835": {"doc_hash": "e88d3cf79db8d71ddbc0b7a5f1839132417ed1aca35073617876a51a268b3875"}, "57a7f47f-6482-4bf5-b62b-6e4212dacde0": {"doc_hash": "3d8f0fd8446ed2b6f59eff6c9b4f3847538ba4acd8b12c0c51c06a25d48d2b6f"}, "47e674fe-7439-4a81-ab33-f2a9cea97f44": {"doc_hash": "fec774e6ae9f4cad6c7b360c60b8ee472c47b05b05ef6d18d252c6f0d7d5fca4"}, "594cb50a-1069-4750-a6a7-08e430a89010": {"doc_hash": "f12aafbb8e54fcc0beddaed8f2b9df8697209e867fc57335f0f221d18cab303b"}, "53039e48-3295-4975-9f7e-e28de1b0fa53": {"doc_hash": "6c76d34dee9106588b8dc4b749349b2f78ba5f4d0ac2b0182e43460e770fd781"}, "26482d70-26f4-4978-bce2-c1c949b96a5e": {"doc_hash": "30cb98dcd2f7ccab9fbdf422e0feecc77838a1086720d0616d62027ec29d053e"}, "ea2dc893-e788-449d-9a1a-7fc775bfec51": {"doc_hash": "7534a49773494e782ca6357ae21128bd9b4102502f93741a331d577977fd468d"}, "ecf4b284-422f-4385-838e-cd7835f291c9": {"doc_hash": "afe85a7a80d5774b143cde13a1056a6d342d34b9e83b6e2fdeb8e11c4bcd772c"}, "377c1932-804e-45bb-b3d8-2d6c4c0cbe55": {"doc_hash": "ae255fc4ac34ded08f4fcb88ccc05c646f71a6c9f2c0b22f425a3bbb50004229"}, "147be61a-4fa8-4e02-871a-1bd66c172cf7": {"doc_hash": "c221f98bf900ef40f44012a543dc67506611ee2b91ecc14f516565cd873755d4"}, "1d9b1636-0629-49e8-90b0-3aa88885c38a": {"doc_hash": "a801616038c20676ecb574b3c77a62bd6ce1344d400591f5a8fb74224b1f0ecc"}, "a0c386e8-2aa7-49f9-89e1-c32b25aeb6b2": {"doc_hash": "e58004a99ad879347f48597e71c8d5d9f61d03ff57f368a4b28cbba77b0dbbd7"}, "9dee0eb8-7436-4d54-b1ef-dbd3536deaf9": {"doc_hash": "4d704bf37710ec4f67abbbba948db3b1c24999dc03f01e29bac8eb3778d468ac"}, "c4487546-8f44-4009-ab58-b15294ecfe72": {"doc_hash": "f1313277034e8b093b35b3352fa6f57c45a1829a90009999587d67527e82c0ae"}, "f78ac40c-9c24-45a3-b17e-afa77a639a35": {"doc_hash": "94daa94105e4847e0d84debabad77a2032e2b63fe25745cd0ec4a35de5556ae9"}, "c040e8c8-0269-424d-ba9a-429256d5a847": {"doc_hash": "db7d581f477a2bc6df40c80c33308ce401eebefc4e70ceee2abb42579aa5261e"}, "4b4c57df-0b82-40ae-8f54-e575232634fb": {"doc_hash": "f75323da85e9730f572c96f760cd00697eaa56b143fbbf748f3b7d2d0f3c474b"}, "842f2795-7ce2-49c1-8063-657a49e9b2fb": {"doc_hash": "240f1af6e250c9e0bdb7ce4f5f25d1f9d2c285f51bb74b95a6075e8d85e2ca1e"}, "16e50e50-74ff-46dd-ac1f-7e955e21ce01": {"doc_hash": "ed3f62cbe935a18eef58b651a4711eb818d9dcae7596c9f67608233353d93e3b"}, "b10a44b3-8574-4a27-a6e2-75a63fa0b6af": {"doc_hash": "4fbdd043bcdf1692d04d1e24c64b3b4fad1e25532021280d87e6778f44a21c12"}, "52c71446-e7f2-4e4e-acc2-39a3323bbfc1": {"doc_hash": "ffa5c5d8d8cec1e1f4458b09a64779bd868870ac763e29aaadc2796130dfbd8d"}, "46299bad-ab24-4ec2-953c-4c09592c2c0a": {"doc_hash": "7494f46e4dc66339eca63b3f344828bb960c709fab60fee43a26b072773fb5ea"}, "c7fbcac9-44af-4ea3-8dc8-a50cabd6e60e": {"doc_hash": "7de16eccb9a8b2cbc6f52743973366a2c2a4750f7755c5bafe60a7c0a0ef9174"}, "9ddd335b-3bc1-4ca7-9e1d-d682961ea00d": {"doc_hash": "f346b26dd871456feb7dba1fa4d576fdb2bbcff9fed3ac65eb84b798f05cb4ff"}, "59d9fe73-9dd4-40f8-a07f-9db41c495a0a": {"doc_hash": "98a366847844471630118aeeff9aa1dbc676574bd387c0c200fcd0ed8f82bff2"}, "d21b137b-0be5-4bfe-8bef-9d9af5bcb48d": {"doc_hash": "9f3ae6b5402b9047fc4eac154bf9ebf7ed5fa61b2281644279125ccbdb614b98"}, "b29706a1-1982-4a7b-a5c9-9f77d57d6f0e": {"doc_hash": "0d697121bf222c9c77a13ee04bcab9162c1f7dd09448d1f088f753a2639ae0ba"}, "b4dbda57-cb02-4fc7-9dc0-7f63a83b994a": {"doc_hash": "c7c848b616e153c8035355ea56b0a60c223b05bdeccd47754d93f9bd2a2ca173"}, "762b15c7-5013-4e3d-9e51-6b9adfc79649": {"doc_hash": "46a86abed7a8aa7e65c89298e6a89c76dff351da675d4390ab989113dfee86cf", "ref_doc_id": "d404b096-8b45-4f72-b98c-151fec5f211f"}, "********-4ebf-4d4e-b310-2f7dc4acb46a": {"doc_hash": "e507945e2b39e3d6090cdfaad4a09752efad2de4e59be21ace9ef2e9a7aa4eec", "ref_doc_id": "39ebc5fe-82b5-435b-9c22-142e60944d16"}, "4ee1df38-a762-4773-ace5-6c3ebb1dc455": {"doc_hash": "ebd0ed998bf2eb1366461cf064643941fea5ddcf5e0407357548b219a3192f98", "ref_doc_id": "500036aa-42fd-454f-9b65-90018f5e7ede"}, "36e9f8b2-6d20-45d8-9f39-e22ee9fa1193": {"doc_hash": "d5252924f1f1e32df134e8dbdcee45f184c8bd2d9701c56ed009040c2737e40d", "ref_doc_id": "df8d0780-ab14-456e-a12e-41875863474f"}, "3ea94943-5e52-41a7-a295-3443cf805c79": {"doc_hash": "580d2a1b32e339c6fe97da39b5b350b3d221e4e7d99946f350d866a1c8c6004a", "ref_doc_id": "e465d2f7-7e58-4ea3-8948-5f4686cea1fe"}, "f18bf653-e093-467e-94e7-e33b808b0279": {"doc_hash": "dbdd725a2dc3eb9927e4dec367fa659933c9d465f94e5acd6e761f2c6253cc0a", "ref_doc_id": "5e1ce493-cd43-426d-af1d-b8ae56047e14"}, "e32f8bb5-01a8-4c29-9e60-33668c8f7051": {"doc_hash": "5f68e1b4f5c3568fdf5c92b7b5568a53fe10ad316ef1a1b4467f212b2dda586e", "ref_doc_id": "6cbcf6b1-0b15-4a06-8e25-c56a2bf61040"}, "47b7ff4e-f46a-456d-abd6-9bf37185e13c": {"doc_hash": "f6dc14c93bcd9bf78bf64b038c998188d24dd1c2eeddcb4b25d5567d4ba9ed5f", "ref_doc_id": "98596c66-a30a-43c0-8186-76ac785b6b08"}, "bf73eeff-89ed-4ebb-b416-50c9f87c6b04": {"doc_hash": "81547d30984b81ba7fc29cccfe974d8bd9da3459fa401b2c1cce79ef261b266b", "ref_doc_id": "e6af940b-30fb-417b-9913-b32daca16472"}, "befaf9ad-3e2b-4966-aa81-bd2578e7e4d1": {"doc_hash": "b79c4c370966e61ecefedd91ef8971c2951ab3d0e29e8849b061fd41e08f7ee3", "ref_doc_id": "0eb9625f-9d71-41d6-a808-5cc3d8891fd5"}, "51a1280c-0c89-4d2a-9f73-044126fa57c3": {"doc_hash": "579343072d9281bcb95a66e52645a0cf4bb82a446111864927a655e60d5d6501", "ref_doc_id": "54ed81a0-7609-4bb8-939a-b4914ad29835"}, "db850d79-6a85-4546-9e77-ea9abb6204dc": {"doc_hash": "ac85faaeb48acd761412c4d5b7c89c0dac2316a0863ca23ca7f1d8c3cc98774d", "ref_doc_id": "57a7f47f-6482-4bf5-b62b-6e4212dacde0"}, "c0a01104-c3b1-48d0-8dc6-90b73057a5ae": {"doc_hash": "006639b0301245b1a65425b30dd201c43067eea5724110d5bae59e4512840d8f", "ref_doc_id": "57a7f47f-6482-4bf5-b62b-6e4212dacde0"}, "4ee39865-3256-48b8-b270-fae7771faa6f": {"doc_hash": "801beb1ebd8d52cc9f0b9db4be01244bdde142b093dc546d4b4c9e31e3ca7cb2", "ref_doc_id": "47e674fe-7439-4a81-ab33-f2a9cea97f44"}, "3da548aa-2a5d-48d3-a9e6-b4fdf6ee6cc7": {"doc_hash": "380822a7a70728c26afaa50a184e89b91b85a12b1244c75893042ecdee7927b5", "ref_doc_id": "594cb50a-1069-4750-a6a7-08e430a89010"}, "4677e16d-8179-4903-b20f-487bafd2eb80": {"doc_hash": "29f69b946f883bb305e86221ff5fe323833c0afc29ab88c5f9e9c790f5705d0d", "ref_doc_id": "53039e48-3295-4975-9f7e-e28de1b0fa53"}, "3fbc3c48-e2d3-4369-b70c-a3ee52629c38": {"doc_hash": "8c3a3ab16baf208d2c187028ccc635a3a46159ad7b0ffbfca1e03bd8bd875069", "ref_doc_id": "26482d70-26f4-4978-bce2-c1c949b96a5e"}, "5db33814-7467-40cd-b8fc-4da924e3406f": {"doc_hash": "709a5a81d4c99d7cf9f96706993feaf00177b5136c914733e949ec536b8e2c8e", "ref_doc_id": "26482d70-26f4-4978-bce2-c1c949b96a5e"}, "9bbf0703-694e-4922-b619-e71e58f179cf": {"doc_hash": "bdcb4c5794e460093265ac30d66a71a84bcffbe7bc09e339f84a3c42fc960b99", "ref_doc_id": "ea2dc893-e788-449d-9a1a-7fc775bfec51"}, "b171ebb5-65f0-48c6-b97e-6c682d028769": {"doc_hash": "dee32cfe73d7c9df921dfa1f0311a51d1b77b68c8716cf64711d6265ad7a2b99", "ref_doc_id": "ea2dc893-e788-449d-9a1a-7fc775bfec51"}, "af447cbe-8da1-44e6-ac34-571ecfbc7ba8": {"doc_hash": "d4801c58192f82a245e88de1a19bb28a33f0a9e18d18d6f1c03a4a66ed16a6af", "ref_doc_id": "ea2dc893-e788-449d-9a1a-7fc775bfec51"}, "c30ecd03-6aeb-4c2e-8c3c-fa8846497a3a": {"doc_hash": "32abf37b5287b6d44c829b38d64ae93cf758f79da9982200d1b484688c8540bc", "ref_doc_id": "ecf4b284-422f-4385-838e-cd7835f291c9"}, "43f719c3-78f6-4bc6-84e7-a8d5c7fcff07": {"doc_hash": "690301708946823272c88235bb4222407c30655b4b4da90fd650b033675cd5a2", "ref_doc_id": "377c1932-804e-45bb-b3d8-2d6c4c0cbe55"}, "a3cc4524-5f6b-4ff4-b0c8-617fe0f54a9a": {"doc_hash": "7ea12b0a0dce3160de2a5546d4087447c0036dda87c7f9e79a90bd0ab368933c", "ref_doc_id": "147be61a-4fa8-4e02-871a-1bd66c172cf7"}, "d8bf9650-a8cd-4d0f-a4e1-02277686d127": {"doc_hash": "e2b802a7056aca94b821cde40be61f21ac0b56462fab1a1e0dde82aa717ce046", "ref_doc_id": "1d9b1636-0629-49e8-90b0-3aa88885c38a"}, "820f3e81-3be2-45ec-95f7-90c28fb234b7": {"doc_hash": "927a954ff995dcfc7cf4fdcc8f88641d6eac64c08cdd1dc7afc6d6decd6a33e6", "ref_doc_id": "a0c386e8-2aa7-49f9-89e1-c32b25aeb6b2"}, "573108c2-913f-4612-9a64-65e72615ef3a": {"doc_hash": "5c733857002e42f40790b15e572b7cf73c4be07f945c897cde0f5fed15934dda", "ref_doc_id": "9dee0eb8-7436-4d54-b1ef-dbd3536deaf9"}, "0ba1f768-e3d1-4b24-a9b2-7782463b40e2": {"doc_hash": "19c74261abd32e7209b0ecb48bde680c6dc8c6f8d03ab9f2b4187131aa26542c", "ref_doc_id": "c4487546-8f44-4009-ab58-b15294ecfe72"}, "2ba31c7e-6ad1-41e0-96c9-ba43badfa2e7": {"doc_hash": "c54ef56571301d2ad42e3a8d6700e2c0f3886e35f6ddac066ff10649210fc3fd", "ref_doc_id": "f78ac40c-9c24-45a3-b17e-afa77a639a35"}, "6d65832d-e81c-4db6-8078-1ab5674d6200": {"doc_hash": "454c25b58c793ed7fb080579bc69efd77c2571d188c5318e9eb641be479a9c1d", "ref_doc_id": "c040e8c8-0269-424d-ba9a-429256d5a847"}, "8e7b84bb-ac2e-4179-8ed5-f640fc7dfccd": {"doc_hash": "91a27c3fe45cf1b50a38ad54750809580c10c7eef5af32b7bca0ec0cf5102bc5", "ref_doc_id": "4b4c57df-0b82-40ae-8f54-e575232634fb"}, "f65a6eca-c309-40d8-9671-ea1dc818569a": {"doc_hash": "48246388c7e853bbb24325ff97c36bc61958ed84e4003b512189d6ef60cd89ee", "ref_doc_id": "842f2795-7ce2-49c1-8063-657a49e9b2fb"}, "bec190be-67d2-4bfc-bcd7-5e3b4d5d6282": {"doc_hash": "8861be49b0d32ecd06406bbe7402a4a0afc00fd5b215cfcd6d7b9d54f7f35a03", "ref_doc_id": "16e50e50-74ff-46dd-ac1f-7e955e21ce01"}, "9aacb827-58ce-49e7-9c97-f9f9fb95c1ff": {"doc_hash": "8e29a96e1a6da7442122e44029073d976986daf959d825823cad3c471eb19650", "ref_doc_id": "b10a44b3-8574-4a27-a6e2-75a63fa0b6af"}, "4a6b2555-cd2b-4097-ad96-02f7c60d6111": {"doc_hash": "95086298c5e83729393e48d3a01de69be652ccb0cce87c2d3a5413b62d44a0c7", "ref_doc_id": "52c71446-e7f2-4e4e-acc2-39a3323bbfc1"}, "c7cdc0ef-5d8c-45bd-a871-387ac92ba6d7": {"doc_hash": "cc07e8ae0ab0d9cdc301204db87ada4184fb6efa7efae198b015e134ea4ecb13", "ref_doc_id": "46299bad-ab24-4ec2-953c-4c09592c2c0a"}, "047305ab-f896-406c-995a-0503ee25888b": {"doc_hash": "4c3c29260dddb6bfa73494e12a4ae1ea63dd7c3b12eb1657e52cec74e3c81b66", "ref_doc_id": "c7fbcac9-44af-4ea3-8dc8-a50cabd6e60e"}, "b8ac8bd0-8a0f-41e9-8bc6-d565edbed2bb": {"doc_hash": "a353a7a35addd8b5c7e01934926d0c33b93337401bc2f3fe8f3533f731ea51f5", "ref_doc_id": "9ddd335b-3bc1-4ca7-9e1d-d682961ea00d"}, "c57861d6-93f5-4b0c-b506-c773eb8e9d51": {"doc_hash": "02209da8691416a3d7d77a59fc3caea5741b8db37d9e53370217d3272ea3355f", "ref_doc_id": "59d9fe73-9dd4-40f8-a07f-9db41c495a0a"}, "f64b23ac-981b-4f07-b432-5d29cc190f73": {"doc_hash": "a8986d8d95970402beb17d2ff2c413949926923508ae05cc26a00badb9dedd10", "ref_doc_id": "d21b137b-0be5-4bfe-8bef-9d9af5bcb48d"}, "83405229-960f-4d4f-bc1b-3db0309184e6": {"doc_hash": "6a5907e069bcc416fe382af8f3f42a9b706a566d9b626cf5ca16fd4881ae0e4d", "ref_doc_id": "b29706a1-1982-4a7b-a5c9-9f77d57d6f0e"}, "0c98e712-fa92-4489-9d77-cb8c5f24905c": {"doc_hash": "cc72d71f09827b9aa6fe3bbafb3817c568b27d20f421d95306ed48190c7e874b", "ref_doc_id": "b4dbda57-cb02-4fc7-9dc0-7f63a83b994a"}}, "docstore/ref_doc_info": {"d404b096-8b45-4f72-b98c-151fec5f211f": {"node_ids": ["762b15c7-5013-4e3d-9e51-6b9adfc79649"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Change Your Email Preferences", "url": "https://meghanag.supporthive.com/kb/article/9-how-to-change-your-email-preferences/", "article_id": 9, "section_id": 2, "last_updated_at": "2025-04-18T03:44:32Z", "has_attachments": false, "attachment_count": 0}}, "39ebc5fe-82b5-435b-9c22-142e60944d16": {"node_ids": ["********-4ebf-4d4e-b310-2f7dc4acb46a"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password", "url": "https://meghanag.supporthive.com/kb/article/6-how-to-reset-your-password/", "article_id": 6, "section_id": 2, "last_updated_at": "2025-04-18T03:41:50Z", "has_attachments": false, "attachment_count": 0}}, "500036aa-42fd-454f-9b65-90018f5e7ede": {"node_ids": ["4ee1df38-a762-4773-ace5-6c3ebb1dc455"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password in HappyFox", "url": "https://meghanag.supporthive.com/kb/article/11-how-to-reset-your-password-in-happyfox/", "article_id": 11, "section_id": 2, "last_updated_at": "2025-04-18T07:53:43Z", "has_attachments": false, "attachment_count": 0}}, "df8d0780-ab14-456e-a12e-41875863474f": {"node_ids": ["36e9f8b2-6d20-45d8-9f39-e22ee9fa1193"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Submit a Ticket for Support", "url": "https://meghanag.supporthive.com/kb/article/8-how-to-submit-a-ticket-for-support/", "article_id": 8, "section_id": 2, "last_updated_at": "2025-04-18T03:43:50Z", "has_attachments": false, "attachment_count": 0}}, "e465d2f7-7e58-4ea3-8948-5f4686cea1fe": {"node_ids": ["3ea94943-5e52-41a7-a295-3443cf805c79"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Update Your Contact Information", "url": "https://meghanag.supporthive.com/kb/article/7-how-to-update-your-contact-information/", "article_id": 7, "section_id": 2, "last_updated_at": "2025-04-18T03:42:44Z", "has_attachments": false, "attachment_count": 0}}, "5e1ce493-cd43-426d-af1d-b8ae56047e14": {"node_ids": ["f18bf653-e093-467e-94e7-e33b808b0279"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Use Two-Factor Authentication (2FA)", "url": "https://meghanag.supporthive.com/kb/article/10-how-to-use-two-factor-authentication-2fa/", "article_id": 10, "section_id": 2, "last_updated_at": "2025-04-18T03:45:13Z", "has_attachments": false, "attachment_count": 0}}, "6cbcf6b1-0b15-4a06-8e25-c56a2bf61040": {"node_ids": ["e32f8bb5-01a8-4c29-9e60-33668c8f7051"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Tables as image and images", "url": "https://meghanag.supporthive.com/kb/article/16-kb-1-tables-as-image-and-images/", "article_id": 16, "section_id": 2, "last_updated_at": "2025-04-29T06:01:25Z", "has_attachments": true, "attachment_count": 4}}, "98596c66-a30a-43c0-8186-76ac785b6b08": {"node_ids": ["47b7ff4e-f46a-456d-abd6-9bf37185e13c"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}}, "e6af940b-30fb-417b-9913-b32daca16472": {"node_ids": ["bf73eeff-89ed-4ebb-b416-50c9f87c6b04"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}}, "0eb9625f-9d71-41d6-a808-5cc3d8891fd5": {"node_ids": ["befaf9ad-3e2b-4966-aa81-bd2578e7e4d1"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/37f48d3f-8d70-400d-8ed6-181972e8a8e4/Screen_Shot_2022-07-13_at_12.35.04_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 53841, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}}, "54ed81a0-7609-4bb8-939a-b4914ad29835": {"node_ids": ["51a1280c-0c89-4d2a-9f73-044126fa57c3"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-\nfiles-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/681a6847-eac3-485e-8ef7-3ab20542f58e/Duralite-100E_Expansion_Kit.png", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "download_failed": true, "is_embedded_image": true}}, "57a7f47f-6482-4bf5-b62b-6e4212dacde0": {"node_ids": ["db850d79-6a85-4546-9e77-ea9abb6204dc", "c0a01104-c3b1-48d0-8dc6-90b73057a5ae"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}}, "47e674fe-7439-4a81-ab33-f2a9cea97f44": {"node_ids": ["4ee39865-3256-48b8-b270-fae7771faa6f"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}}, "594cb50a-1069-4750-a6a7-08e430a89010": {"node_ids": ["3da548aa-2a5d-48d3-a9e6-b4fdf6ee6cc7"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}}, "53039e48-3295-4975-9f7e-e28de1b0fa53": {"node_ids": ["4677e16d-8179-4903-b20f-487bafd2eb80"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 176549, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}}, "26482d70-26f4-4978-bce2-c1c949b96a5e": {"node_ids": ["3fbc3c48-e2d3-4369-b70c-a3ee52629c38", "5db33814-7467-40cd-b8fc-4da924e3406f"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 213826, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}}, "ea2dc893-e788-449d-9a1a-7fc775bfec51": {"node_ids": ["9bbf0703-694e-4922-b619-e71e58f179cf", "b171ebb5-65f0-48c6-b97e-6c682d028769", "af447cbe-8da1-44e6-ac34-571ecfbc7ba8"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}}, "ecf4b284-422f-4385-838e-cd7835f291c9": {"node_ids": ["c30ecd03-6aeb-4c2e-8c3c-fa8846497a3a"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 15, "parent_article_title": "KB-1 Text & Table", "attachment_id": "img_15_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}}, "377c1932-804e-45bb-b3d8-2d6c4c0cbe55": {"node_ids": ["43f719c3-78f6-4bc6-84e7-a8d5c7fcff07"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 15, "parent_article_title": "KB-1 Text & Table", "attachment_id": "img_15_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://hf-\nfiles-oregon.s3-us-\nwest-2.amazonaws.com/hdpdigitalwatchdog_kb_attachments/2019/09-24/4763eb2b-7c2d-49d6-bfc0-47620cc0f150/image.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 11165, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}}, "147be61a-4fa8-4e02-871a-1bd66c172cf7": {"node_ids": ["a3cc4524-5f6b-4ff4-b0c8-617fe0f54a9a"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "PDF", "url": "https://meghanag.supporthive.com/kb/article/22-pdf/", "article_id": 22, "section_id": 2, "last_updated_at": "2025-06-16T09:01:30Z", "has_attachments": true, "attachment_count": 1}}, "1d9b1636-0629-49e8-90b0-3aa88885c38a": {"node_ids": ["d8bf9650-a8cd-4d0f-a4e1-02277686d127"], "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "section_id": 2, "download_failed": true, "is_embedded_image": false}}, "a0c386e8-2aa7-49f9-89e1-c32b25aeb6b2": {"node_ids": ["820f3e81-3be2-45ec-95f7-90c28fb234b7"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "PDF-TEST-2", "url": "https://meghanag.supporthive.com/kb/article/23-pdf-test-2/", "article_id": 23, "section_id": 2, "last_updated_at": "2025-06-19T11:05:28Z", "has_attachments": true, "attachment_count": 1}}, "9dee0eb8-7436-4d54-b1ef-dbd3536deaf9": {"node_ids": ["573108c2-913f-4612-9a64-65e72615ef3a"], "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 23, "parent_article_title": "PDF-TEST-2", "attachment_id": 3, "attachment_name": "GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-19/7d02d733-d84f-4e17-a7a9-1d33c8a78798/GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "section_id": 2, "download_failed": true, "is_embedded_image": false}}, "c4487546-8f44-4009-ab58-b15294ecfe72": {"node_ids": ["0ba1f768-e3d1-4b24-a9b2-7782463b40e2"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample article", "url": "https://meghanag.supporthive.com/kb/article/12-sample-article/", "article_id": 12, "section_id": 2, "last_updated_at": "2025-04-22T05:44:42Z", "has_attachments": false, "attachment_count": 0}}, "f78ac40c-9c24-45a3-b17e-afa77a639a35": {"node_ids": ["2ba31c7e-6ad1-41e0-96c9-ba43badfa2e7"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample docx", "url": "https://meghanag.supporthive.com/kb/article/17-sample-docx/", "article_id": 17, "section_id": 2, "last_updated_at": "2025-05-12T06:18:44Z", "has_attachments": true, "attachment_count": 1}}, "c040e8c8-0269-424d-ba9a-429256d5a847": {"node_ids": ["6d65832d-e81c-4db6-8078-1ab5674d6200"], "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 17, "parent_article_title": "sample docx", "attachment_id": 1, "attachment_name": "sample_kb_article.docx", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-12/69860b5a-91bb-4339-bf21-5b9c647d9053/sample_kb_article.docx", "mime_type": "application/octet-stream", "file_extension": ".docx", "file_size": 37001, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}}, "4b4c57df-0b82-40ae-8f54-e575232634fb": {"node_ids": ["8e7b84bb-ac2e-4179-8ed5-f640fc7dfccd"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample image", "url": "https://meghanag.supporthive.com/kb/article/19-sample-image/", "article_id": 19, "section_id": 2, "last_updated_at": "2025-05-14T11:23:38Z", "has_attachments": true, "attachment_count": 1}}, "842f2795-7ce2-49c1-8063-657a49e9b2fb": {"node_ids": ["f65a6eca-c309-40d8-9671-ea1dc818569a"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 19, "parent_article_title": "sample image", "attachment_id": "img_19_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-14/89f0213e-8ddf-415e-8bce-1f32bdbe8c43/Screenshot_2025-05-14_at_4.52.20PM.png", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "download_failed": true, "is_embedded_image": true}}, "16e50e50-74ff-46dd-ac1f-7e955e21ce01": {"node_ids": ["bec190be-67d2-4bfc-bcd7-5e3b4d5d6282"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "segment", "url": "https://meghanag.supporthive.com/kb/article/20-segment/", "article_id": 20, "section_id": 2, "last_updated_at": "2025-05-15T15:07:55Z", "has_attachments": true, "attachment_count": 1}}, "b10a44b3-8574-4a27-a6e2-75a63fa0b6af": {"node_ids": ["9aacb827-58ce-49e7-9c97-f9f9fb95c1ff"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 20, "parent_article_title": "segment", "attachment_id": "img_20_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-15/9970e608-34ec-4d3e-8230-a0be6f858b31/Screenshot_2025-05-15_at_8.37.11PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 94007, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}}, "52c71446-e7f2-4e4e-acc2-39a3323bbfc1": {"node_ids": ["4a6b2555-cd2b-4097-ad96-02f7c60d6111"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article", "url": "https://meghanag.supporthive.com/kb/article/1-test-article/", "article_id": 1, "section_id": 2, "last_updated_at": "2025-04-08T16:05:41Z", "has_attachments": false, "attachment_count": 0}}, "46299bad-ab24-4ec2-953c-4c09592c2c0a": {"node_ids": ["c7cdc0ef-5d8c-45bd-a871-387ac92ba6d7"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 2", "url": "https://meghanag.supporthive.com/kb/article/2-test-article-2/", "article_id": 2, "section_id": 2, "last_updated_at": "2025-04-09T10:10:39Z", "has_attachments": false, "attachment_count": 0}}, "c7fbcac9-44af-4ea3-8dc8-a50cabd6e60e": {"node_ids": ["047305ab-f896-406c-995a-0503ee25888b"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 3", "url": "https://meghanag.supporthive.com/kb/article/3-test-article-3/", "article_id": 3, "section_id": 3, "last_updated_at": "2025-04-16T06:34:30Z", "has_attachments": false, "attachment_count": 0}}, "9ddd335b-3bc1-4ca7-9e1d-d682961ea00d": {"node_ids": ["b8ac8bd0-8a0f-41e9-8bc6-d565edbed2bb"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 4", "url": "https://meghanag.supporthive.com/kb/article/4-test-article-4/", "article_id": 4, "section_id": 2, "last_updated_at": "2025-04-16T06:36:18Z", "has_attachments": false, "attachment_count": 0}}, "59d9fe73-9dd4-40f8-a07f-9db41c495a0a": {"node_ids": ["c57861d6-93f5-4b0c-b506-c773eb8e9d51"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 5", "url": "https://meghanag.supporthive.com/kb/article/5-test-article-5/", "article_id": 5, "section_id": 2, "last_updated_at": "2025-04-16T07:44:33Z", "has_attachments": false, "attachment_count": 0}}, "d21b137b-0be5-4bfe-8bef-9d9af5bcb48d": {"node_ids": ["f64b23ac-981b-4f07-b432-5d29cc190f73"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "test image", "url": "https://meghanag.supporthive.com/kb/article/18-test-image/", "article_id": 18, "section_id": 2, "last_updated_at": "2025-05-13T04:34:16Z", "has_attachments": true, "attachment_count": 1}}, "b29706a1-1982-4a7b-a5c9-9f77d57d6f0e": {"node_ids": ["83405229-960f-4d4f-bc1b-3db0309184e6"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 18, "parent_article_title": "test image", "attachment_id": "img_18_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-13/156de1d0-0a3e-4fbe-b6f1-fc888c203515/Screenshot_2025-05-13_at_10.03.18AM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 266051, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}}, "b4dbda57-cb02-4fc7-9dc0-7f63a83b994a": {"node_ids": ["0c98e712-fa92-4489-9d77-cb8c5f24905c"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "url sample", "url": "https://meghanag.supporthive.com/kb/article/21-url-sample/", "article_id": 21, "section_id": 2, "last_updated_at": "2025-05-16T04:09:12Z", "has_attachments": false, "attachment_count": 0}}}, "docstore/data": {"762b15c7-5013-4e3d-9e51-6b9adfc79649": {"__data__": {"id_": "762b15c7-5013-4e3d-9e51-6b9adfc79649", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Change Your Email Preferences", "url": "https://meghanag.supporthive.com/kb/article/9-how-to-change-your-email-preferences/", "article_id": 9, "section_id": 2, "last_updated_at": "2025-04-18T03:44:32Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "d404b096-8b45-4f72-b98c-151fec5f211f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Change Your Email Preferences", "url": "https://meghanag.supporthive.com/kb/article/9-how-to-change-your-email-preferences/", "article_id": 9, "section_id": 2, "last_updated_at": "2025-04-18T03:44:32Z", "has_attachments": false, "attachment_count": 0}, "hash": "fed439483c0cc24384562934cb28505168d786fa1acbc5769afd1c0e12835bcd", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "To customize your email preferences, follow these steps:\n\n  1. Log in to your account.\n\n  2. Navigate to \"Settings\" in the top-right corner.\n\n  3. Click on \"Email Preferences.\"\n\n  4. Choose which types of emails you’d like to receive (e.g., system alerts, updates, newsletters).\n\n  5. Save your changes.\n\n**Troubleshooting:**\n\n  * If you no longer want to receive certain types of emails, make sure to unsubscribe or adjust your email preferences.\n\n  * If emails are being marked as spam, whitelist our email address in your email client.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 538, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "********-4ebf-4d4e-b310-2f7dc4acb46a": {"__data__": {"id_": "********-4ebf-4d4e-b310-2f7dc4acb46a", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password", "url": "https://meghanag.supporthive.com/kb/article/6-how-to-reset-your-password/", "article_id": 6, "section_id": 2, "last_updated_at": "2025-04-18T03:41:50Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "39ebc5fe-82b5-435b-9c22-142e60944d16", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password", "url": "https://meghanag.supporthive.com/kb/article/6-how-to-reset-your-password/", "article_id": 6, "section_id": 2, "last_updated_at": "2025-04-18T03:41:50Z", "has_attachments": false, "attachment_count": 0}, "hash": "8a5494c441ece48633a53b75daf0b05311652c44f689c4ba9733268764f59dcc", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "If you’ve forgotten your password or wish to change it for security reasons,\nfollow these steps to reset your password:\n\n  1. Go to the login page.\n\n  2. Click on the \"Forgot Password?\" link.\n\n  3. Enter your registered email address.\n\n  4. Check your inbox for a password reset email.\n\n  5. Click on the \"Reset Password\" link in the email.\n\n  6. Enter a new password and confirm it.\n\n  7. Log in with your new password.\n\n**Troubleshooting:**\n\n  * If you do not receive the reset email, check your spam or junk folder.\n\n  * Ensure you’ve entered the correct email address linked to your account.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 595, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4ee1df38-a762-4773-ace5-6c3ebb1dc455": {"__data__": {"id_": "4ee1df38-a762-4773-ace5-6c3ebb1dc455", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password in HappyFox", "url": "https://meghanag.supporthive.com/kb/article/11-how-to-reset-your-password-in-happyfox/", "article_id": 11, "section_id": 2, "last_updated_at": "2025-04-18T07:53:43Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "500036aa-42fd-454f-9b65-90018f5e7ede", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password in HappyFox", "url": "https://meghanag.supporthive.com/kb/article/11-how-to-reset-your-password-in-happyfox/", "article_id": 11, "section_id": 2, "last_updated_at": "2025-04-18T07:53:43Z", "has_attachments": false, "attachment_count": 0}, "hash": "91080773e4183c3fb155cf64a6c0162acb64dec6fe7df206a999bf678614c36b", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "If you need to reset your password for your HappyFox account, follow these\nsteps:  \n  \n  1. Go to the login page.\n\n  2. Click on the \"Forgot Password\" link.\n\n  3. Enter your registered email address.\n\n  4. You'll receive an email with a link to reset your password.\n\n  5. Click the link, and you'll be able to set a new password.\n\n**Note:** If you do not receive the email within a few minutes, check your\nspam folder or try again later.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 437, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "36e9f8b2-6d20-45d8-9f39-e22ee9fa1193": {"__data__": {"id_": "36e9f8b2-6d20-45d8-9f39-e22ee9fa1193", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Submit a Ticket for Support", "url": "https://meghanag.supporthive.com/kb/article/8-how-to-submit-a-ticket-for-support/", "article_id": 8, "section_id": 2, "last_updated_at": "2025-04-18T03:43:50Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "df8d0780-ab14-456e-a12e-41875863474f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Submit a Ticket for Support", "url": "https://meghanag.supporthive.com/kb/article/8-how-to-submit-a-ticket-for-support/", "article_id": 8, "section_id": 2, "last_updated_at": "2025-04-18T03:43:50Z", "has_attachments": false, "attachment_count": 0}, "hash": "0793cfd15db579cb4e293e94f2bc8560749057301bd65ac3e7e32502a4da3921", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "If you need further assistance, submitting a support ticket is the best way to\nget help from our team:\n\n  1. Log in to your account.\n\n  2. Go to the \"Support\" section.\n\n  3. Click on \"Submit a Ticket.\"\n\n  4. Select a category for your issue.\n\n  5. Fill out the ticket form with all relevant details.\n\n  6. Click \"Submit\" to send your ticket to our support team.\n\n  7. You will receive a confirmation email with a ticket number.\n\n**Troubleshooting:**\n\n  * If you do not see the ticket form, ensure you are logged in to your account.\n\n  * Make sure to provide as much detail as possible to expedite the resolution process.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 620, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "3ea94943-5e52-41a7-a295-3443cf805c79": {"__data__": {"id_": "3ea94943-5e52-41a7-a295-3443cf805c79", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Update Your Contact Information", "url": "https://meghanag.supporthive.com/kb/article/7-how-to-update-your-contact-information/", "article_id": 7, "section_id": 2, "last_updated_at": "2025-04-18T03:42:44Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "e465d2f7-7e58-4ea3-8948-5f4686cea1fe", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Update Your Contact Information", "url": "https://meghanag.supporthive.com/kb/article/7-how-to-update-your-contact-information/", "article_id": 7, "section_id": 2, "last_updated_at": "2025-04-18T03:42:44Z", "has_attachments": false, "attachment_count": 0}, "hash": "b0b36b00f4924589f7716b0e660006c6f5a576940cfc53283c232b07727b2660", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "You can update your contact information directly from your account settings:\n\n  1. Log in to your account.\n\n  2. Click on your profile picture in the top-right corner.\n\n  3. Select \"Account Settings.\"\n\n  4. Under \"Personal Information,\" click \"Edit.\"\n\n  5. Update your name, email, and phone number as needed.\n\n  6. Save your changes.\n\n**Troubleshooting:**\n\n  * Ensure the email address is valid and correctly formatted.\n\n  * If you experience issues saving changes, clear your browser cache or try a different browser.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 519, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "f18bf653-e093-467e-94e7-e33b808b0279": {"__data__": {"id_": "f18bf653-e093-467e-94e7-e33b808b0279", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Use Two-Factor Authentication (2FA)", "url": "https://meghanag.supporthive.com/kb/article/10-how-to-use-two-factor-authentication-2fa/", "article_id": 10, "section_id": 2, "last_updated_at": "2025-04-18T03:45:13Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "5e1ce493-cd43-426d-af1d-b8ae56047e14", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Use Two-Factor Authentication (2FA)", "url": "https://meghanag.supporthive.com/kb/article/10-how-to-use-two-factor-authentication-2fa/", "article_id": 10, "section_id": 2, "last_updated_at": "2025-04-18T03:45:13Z", "has_attachments": false, "attachment_count": 0}, "hash": "d44a3ea2fc59d7eb8f9fa036a2871a8178290980950207be3df0ad42ab132548", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Two-factor authentication (2FA) adds an extra layer of security to your\naccount. Here’s how to enable it:\n\n  1. Log in to your account.\n\n  2. Go to \"Account Settings.\"\n\n  3. Under \"Security Settings,\" click on \"Enable Two-Factor Authentication.\"\n\n  4. Choose your preferred method (e.g., mobile app, email).\n\n  5. Follow the instructions to link your account to your 2FA method.\n\n  6. Enter the verification code sent to your device to complete the setup.\n\n**Troubleshooting:**\n\n  * If you lose access to your 2FA method, contact support for assistance.\n\n  * Ensure that the device used for 2FA is set up correctly to avoid verification issues.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 644, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "e32f8bb5-01a8-4c29-9e60-33668c8f7051": {"__data__": {"id_": "e32f8bb5-01a8-4c29-9e60-33668c8f7051", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Tables as image and images", "url": "https://meghanag.supporthive.com/kb/article/16-kb-1-tables-as-image-and-images/", "article_id": 16, "section_id": 2, "last_updated_at": "2025-04-29T06:01:25Z", "has_attachments": true, "attachment_count": 4}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "6cbcf6b1-0b15-4a06-8e25-c56a2bf61040", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Tables as image and images", "url": "https://meghanag.supporthive.com/kb/article/16-kb-1-tables-as-image-and-images/", "article_id": 16, "section_id": 2, "last_updated_at": "2025-04-29T06:01:25Z", "has_attachments": true, "attachment_count": 4}, "hash": "210ad9ca2605aa5359af49ece2a2e89183f9592a31acbc1859cc1afd5e5f586a", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Duralite-100E Expansion Kit\n\n![print\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg)\n\nMonsoon Gohain\n\nSep 01, 2022\n\n![views\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg) 927\n\n![](https://hf-files-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/37f48d3f-8d70-400d-8ed6-181972e8a8e4/Screen_Shot_2022-07-13_at_12.35.04_PM.png)![](https://hf-\nfiles-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/681a6847-eac3-485e-8ef7-3ab20542f58e/Duralite-100E_Expansion_Kit.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 592, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "47b7ff4e-f46a-456d-abd6-9bf37185e13c": {"__data__": {"id_": "47b7ff4e-f46a-456d-abd6-9bf37185e13c", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "98596c66-a30a-43c0-8186-76ac785b6b08", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "hash": "d1fa6f7372ce8fb79760860e0cf454b837d8276b706a6b2863f4fcef925eb41b", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_1.png\nFile Type: image/png\nURL: https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg\n\nProcessing failed: Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 349, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "bf73eeff-89ed-4ebb-b416-50c9f87c6b04": {"__data__": {"id_": "bf73eeff-89ed-4ebb-b416-50c9f87c6b04", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "e6af940b-30fb-417b-9913-b32daca16472", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "hash": "84765eaa2bf28ee0ed61f27d804943888b25195481617e0254c86ecfe75183c0", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_2.png\nFile Type: image/png\nURL: https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg\n\nProcessing failed: Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 355, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "befaf9ad-3e2b-4966-aa81-bd2578e7e4d1": {"__data__": {"id_": "befaf9ad-3e2b-4966-aa81-bd2578e7e4d1", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/37f48d3f-8d70-400d-8ed6-181972e8a8e4/Screen_Shot_2022-07-13_at_12.35.04_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 53841, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "0eb9625f-9d71-41d6-a808-5cc3d8891fd5", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/37f48d3f-8d70-400d-8ed6-181972e8a8e4/Screen_Shot_2022-07-13_at_12.35.04_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 53841, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "05c0e76ba0732aee02b15c93ae3d99c72afd7dd07a2c8d70c517fb0175155440", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "# Description\n\n!Table[The table contains a list of 7 items describing various components or features of a device, likely a portable power-related product. The table has two columns: a number column and a description column. The items are:\n1. Handle\n2. Foldable legs\n3. USB ports & LED indicator\n4. DC output port\n5. 2x Extension Cable\n6. Barrel Connector (for connecting to portable power stations)\n7. MC4 Connector (for connecting to portable power stations)]", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 460, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "51a1280c-0c89-4d2a-9f73-044126fa57c3": {"__data__": {"id_": "51a1280c-0c89-4d2a-9f73-044126fa57c3", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-\nfiles-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/681a6847-eac3-485e-8ef7-3ab20542f58e/Duralite-100E_Expansion_Kit.png", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "download_failed": true, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "54ed81a0-7609-4bb8-939a-b4914ad29835", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-\nfiles-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/681a6847-eac3-485e-8ef7-3ab20542f58e/Duralite-100E_Expansion_Kit.png", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "download_failed": true, "is_embedded_image": true}, "hash": "e88d3cf79db8d71ddbc0b7a5f1839132417ed1aca35073617876a51a268b3875", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_4.png\nFile Type: image/png\nURL: https://hf-\nfiles-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/681a6847-eac3-485e-8ef7-3ab20542f58e/Duralite-100E_Expansion_Kit.png\n\nDownload failed but image detected.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 248, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "db850d79-6a85-4546-9e77-ea9abb6204dc": {"__data__": {"id_": "db850d79-6a85-4546-9e77-ea9abb6204dc", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "57a7f47f-6482-4bf5-b62b-6e4212dacde0", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "hash": "3d8f0fd8446ed2b6f59eff6c9b4f3847538ba4acd8b12c0c51c06a25d48d2b6f", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "c0a01104-c3b1-48d0-8dc6-90b73057a5ae", "node_type": "1", "metadata": {}, "hash": "f134e096d4bdf5b4db087bb4babc987598a1a8c457e30d2146add173551b2a14", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Automate Okta Identity Lifecycle Actions for requests in Zendesk\n\n![print\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg)\n\nNov 05, 2021\n\n![views\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg) 2209\n\nBy using HappyFox Workflows, IT admins can automate everyday identity\nlifecycle actions in Okta for user requests from Zendesk.\n\n**List of Okta actions that can be invoked/automated inside Zendesk:**\n\n  * Password resets (including expiring temporary password)\n  * Account unlocks\n  * Resetting any MFA enrollments\n  * Clearing existing user sessions\n  * Account suspensions\n  * Account deactivations.\n\n_[Click here](https://www.happyfox.com/workflows-for-zendesk-support/#book-\ndemo) _to book a 1-on-1 demo with our product experts\n\n**Pre-requisite**\n\nAn active HappyFox Workflows account with Zendesk support and Okta\nsuccessfully integrated.\n\n_Quicklinks:_\n\n  * [Zendesk configuration with HappyFox Workflows](https://support.happyfox.com/kb/article/1002-how-to-configure-happyfox-workflows-for-zendesk-support/)\n  * [Okta configuration with HappyFox Workflows](https://support.happyfox.com/kb/article/1136-configure-okta-integration-with-happyfox-workflows/)\n\n**Automated Workflow Example:**\n\n  1. A User raises an Okta unlock request in Zendesk\n  2. HappyFox Workflows automatically detects an unlock request and validates it.\n  3. HappyFox Workflows initiates the user unlock action in Okta automatically.\n  4. HappyFox Workflows sends an update to the user on the next steps.\n\n!", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1584, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "c0a01104-c3b1-48d0-8dc6-90b73057a5ae": {"__data__": {"id_": "c0a01104-c3b1-48d0-8dc6-90b73057a5ae", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "57a7f47f-6482-4bf5-b62b-6e4212dacde0", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "hash": "3d8f0fd8446ed2b6f59eff6c9b4f3847538ba4acd8b12c0c51c06a25d48d2b6f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "db850d79-6a85-4546-9e77-ea9abb6204dc", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "hash": "ac85faaeb48acd761412c4d5b7c89c0dac2316a0863ca23ca7f1d8c3cc98774d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "3. HappyFox Workflows initiates the user unlock action in Okta automatically.\n  4. HappyFox Workflows sends an update to the user on the next steps.\n\n![](https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png)\n\n**Manually Triggering Okta Actions:**\n\n<PERSON><PERSON> can also set up HappyFox Workflows configuration to manually trigger\nOkta actions from the context of a Zendesk Ticket.\n\n![](https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png)\n\nFeedback\n\n4 out of 4 found this helpful", "mimetype": "text/plain", "start_char_idx": 1433, "end_char_idx": 2113, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4ee39865-3256-48b8-b270-fae7771faa6f": {"__data__": {"id_": "4ee39865-3256-48b8-b270-fae7771faa6f", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "47e674fe-7439-4a81-ab33-f2a9cea97f44", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "hash": "fec774e6ae9f4cad6c7b360c60b8ee472c47b05b05ef6d18d252c6f0d7d5fca4", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_1.png\nFile Type: image/png\nURL: https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg\n\nProcessing failed: Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 349, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "3da548aa-2a5d-48d3-a9e6-b4fdf6ee6cc7": {"__data__": {"id_": "3da548aa-2a5d-48d3-a9e6-b4fdf6ee6cc7", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "594cb50a-1069-4750-a6a7-08e430a89010", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "hash": "f12aafbb8e54fcc0beddaed8f2b9df8697209e867fc57335f0f221d18cab303b", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_2.png\nFile Type: image/png\nURL: https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg\n\nProcessing failed: Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 355, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4677e16d-8179-4903-b20f-487bafd2eb80": {"__data__": {"id_": "4677e16d-8179-4903-b20f-487bafd2eb80", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 176549, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "53039e48-3295-4975-9f7e-e28de1b0fa53", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 176549, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "6c76d34dee9106588b8dc4b749349b2f78ba5f4d0ac2b0182e43460e770fd781", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "# Help! I got locked out of my account\n\n7 minutes ago • <PERSON> <EMAIL> via Adikeshav c (change) • from Zendesk Support\n\n## Interactions\n\n### Public reply Internal note\n\nTo <PERSON> CC\n\n### Conversations\n\nAll (3) Public (1) Internal (1)\n\n#### Workflow Bot (assign)\n3 minutes ago\n\nHey <PERSON>,\n\nThis is an automated response from the priority support team. We have successfully initiated the unlock procedure for your Okta account. Kindly check your email for the next steps!\n\n#### Adikeshav c (assign)\n4 minutes ago\n\nDetected that this is an account unlock account <NAME_EMAIL>. Proceeding to unlock the agent.\n\n#### <PERSON> (assign)\n7 minutes ago\n\nHey! I got Locked out of my Okta account. Could you please unlock my account?\n\n### Apply macro\n\nClose tab Submit as Open", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 804, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "3fbc3c48-e2d3-4369-b70c-a3ee52629c38": {"__data__": {"id_": "3fbc3c48-e2d3-4369-b70c-a3ee52629c38", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 213826, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "26482d70-26f4-4978-bce2-c1c949b96a5e", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 213826, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "30cb98dcd2f7ccab9fbdf422e0feecc77838a1086720d0616d62027ec29d053e", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "5db33814-7467-40cd-b8fc-4da924e3406f", "node_type": "1", "metadata": {}, "hash": "efa88e591bb8f9aa2a65a9b9e4d59b7ea3ff50a78f460e6e4cbae3277bdd7404", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "# Help! I got locked out of my account\n\n## Ticket Details\n\n- Ticket #: 274\n- Status: OPEN\n- Customer: <PERSON>\n- Created: 23 minutes ago\n- Source: <EMAIL> via Adikeshav c (change)\n- From: Zendesk Support\n\n## Customer Information\n\n- Name: <PERSON>\n- Email: <EMAIL>\n- Phone: +************\n- Time Zone: Eastern Time (US & Canada)\n- Language: English (United States)\n- Product: product_a\n\n## Interactions\n\n### Help! I got locked out of my account\n24 minutes ago - 6 comments\n\n### My order is not delivered. I had order...\nSep 02 05:08 - 2 comments\n\n### asdoijasdoiajsodijasd\nSep 02 04:46 - 2 comments\n\n### Need help with refund. headphones ...\nSep 02 04:40 - 3 comments\n\n### <PERSON>\nSep 02 04:25 - 2 comments\n\n## Current Conversation\n\n### Workflow Bot (assign)\n19 minutes ago\n\nHey <PERSON>,\n\nThis is an automated response from the priority support team. We have successfully initiated the unlock procedure for your Okta account. Kindly check your email for the next steps.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 996, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "5db33814-7467-40cd-b8fc-4da924e3406f": {"__data__": {"id_": "5db33814-7467-40cd-b8fc-4da924e3406f", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 213826, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "26482d70-26f4-4978-bce2-c1c949b96a5e", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 213826, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "30cb98dcd2f7ccab9fbdf422e0feecc77838a1086720d0616d62027ec29d053e", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "3fbc3c48-e2d3-4369-b70c-a3ee52629c38", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 213826, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "8c3a3ab16baf208d2c187028ccc635a3a46159ad7b0ffbfca1e03bd8bd875069", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "We have successfully initiated the unlock procedure for your Okta account. Kindly check your email for the next steps.\n\n## Actions\n\n- Apply macro\n- Submit as Open\n\n## Apps\n\n### Assist Bot\n\n### Whatsapp\n\n### HappyFox Workflows\n\n- Activate a User\n- Add a User to a Group\n- Assign an App to a User\n- Clear all open sessions for a User\n- Deactivate a User\n- Expire the password for a User\n- Get User properties\n- Reactivate a User\n- Remove a User from a Group\n- Reset password for a User\n- Reset the Login Factors for a User", "mimetype": "text/plain", "start_char_idx": 878, "end_char_idx": 1398, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "9bbf0703-694e-4922-b619-e71e58f179cf": {"__data__": {"id_": "9bbf0703-694e-4922-b619-e71e58f179cf", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "ea2dc893-e788-449d-9a1a-7fc775bfec51", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "hash": "7534a49773494e782ca6357ae21128bd9b4102502f93741a331d577977fd468d", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "b171ebb5-65f0-48c6-b97e-6c682d028769", "node_type": "1", "metadata": {}, "hash": "acfd6453f89c628bc19f6949a31a8aa520a37b48688a8ff900926532cd9b072e", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Recorder and RAID Default Login List\n\n![print\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg)\n\n![https://hf-files-oregon.s3-us-\nwest-2.amazonaws.com/hdpdigitalwatchdog_kb_attachments/2019/09-24/4763eb2b-7c2d-49d6-bfc0-47620cc0f150/image.png](https://hf-\nfiles-oregon.s3-us-\nwest-2.amazonaws.com/hdpdigitalwatchdog_kb_attachments/2019/09-24/4763eb2b-7c2d-49d6-bfc0-47620cc0f150/image.png)\n\n# **Recorder and RAID Default Login List**\n\n\\-----------------------------------\n\n**Affected Roles:** Administrator, Owner\n\n**Related Digital Watchdog VMS Apps:** DW Spectrum® IPVMS\n\n**Last Edit:** August 6, 2024\n\n\\-----------------------------------\n\n# **De<PERSON>ult <PERSON>**\n\nThe DW Blackjack® Series and VMAX® Series units are shipped to use the default\nlogin credentials. DW Blackjack® units will typically not require a login to\naccess the OS, but VMAX® DVRs and NVRs do require the user to enter a login\nupon booting to use the unit.\n\nAdditionally, some DW Blackjack® Servers feature the LSI RAID Manager\nsoftware, which allows Administrators to manage and maintain the RAID array of\nthose special units.\n\nThis article will list the default login credentials of the DW Blackjack® and\nVMAX® recording units, as well as the default login to the LSI RAID Manager\nprogram for DW Blackjack® units with RAID support.\n\n****NOTE:** The default login for the DW Spectrum® IPVMS software\n(admin/admin12345) is the same on all DW Blackjack® units.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1474, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "b171ebb5-65f0-48c6-b97e-6c682d028769": {"__data__": {"id_": "b171ebb5-65f0-48c6-b97e-6c682d028769", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "ea2dc893-e788-449d-9a1a-7fc775bfec51", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "hash": "7534a49773494e782ca6357ae21128bd9b4102502f93741a331d577977fd468d", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "9bbf0703-694e-4922-b619-e71e58f179cf", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "hash": "bdcb4c5794e460093265ac30d66a71a84bcffbe7bc09e339f84a3c42fc960b99", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "af447cbe-8da1-44e6-ac34-571ecfbc7ba8", "node_type": "1", "metadata": {}, "hash": "953bec790a5244a0c3353b325dae66da5a7e2aea7181eb05fe3829cf6a9ba033", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "****NOTE:** The default login for the DW Spectrum® IPVMS software\n(admin/admin12345) is the same on all DW Blackjack® units.\n\n# **Supported/Affected Devices:**\n\n  * DW Blackjack® Cube Series\n  * DW Blackjack® E-Rack/P-Rack Series\n  * DW Blackjack® X-Rack Series\n  * DW Blackjack® Intel Xeon Silver Processor 2U Series\n  * DW Blackjack® Tower Series\n  * DW Blackjack® MINI Series\n  * DW Blackjack® NAS Series\n  * VMAX® A1 Plus™ Series\n  * VMAX® IP Plus™ Series\n\n# **Default DW Blackjack Login List**\n\n**Device Series** |  **Default IP Address** |  **Default Unit Login**  \n---|---|---  \nDW Blackjack® Cube |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® E-Rack & P-Rack |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® Tower & Mid-Tower |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® X-Rack |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® Intel Xeon Silver Processor 2U |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® MINI |  DHCP |  admin/admin1234  \nDW Blackjack® NAS |  ************* |  admin/admin1234  \n  \n# ****NOTE:** For DW Blackjack® Servers with Windows, purchased prior to June\n18, 2021, the previously utilized default OS login is “ _admin/admin_ ”.\n\n****NOTE:** For DW Blackjack® Servers with Linux, purchased prior to June 18,\n2021, the previously utilized default OS login is “ _admin/admin_ ”.", "mimetype": "text/plain", "start_char_idx": 1350, "end_char_idx": 2643, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "af447cbe-8da1-44e6-ac34-571ecfbc7ba8": {"__data__": {"id_": "af447cbe-8da1-44e6-ac34-571ecfbc7ba8", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "ea2dc893-e788-449d-9a1a-7fc775bfec51", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "hash": "7534a49773494e782ca6357ae21128bd9b4102502f93741a331d577977fd468d", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "b171ebb5-65f0-48c6-b97e-6c682d028769", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "hash": "dee32cfe73d7c9df921dfa1f0311a51d1b77b68c8716cf64711d6265ad7a2b99", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "****NOTE:** For DW Blackjack® Servers with Linux, purchased prior to June 18,\n2021, the previously utilized default OS login is “ _admin/admin_ ”.\n\n# **Default LSI RAID Manager Login List**\n\n**Device Series** |  **Default RAID Login** |  **Example Login**  \n---|---|---  \nDW Blackjack® P-Rack and E-Rack (Windows) |  <system name>/admin |  _bjer4u120t/admin_  \nDW Blackjack® P-Rack and E-Rack (Ubuntu/Linux) |  root/admin |   \nDW Blackjack® X-Rack |  x-rack/Xrack1234 |   \n  \n# **Default VMAX® Unit Login List**\n\n**Device Series** |  **Default IP Address** |  **Default Unit Login**  \n---|---|---  \nVMAX® A1 Plus and VMAX A1 G4 |  ************* |  admin/<no password>  \nVMAX® IP Plus and VMAX VG4 |  ************* |  admin/<no password>  \nDW Compressor |  ************* |  admin/admin", "mimetype": "text/plain", "start_char_idx": 2497, "end_char_idx": 3281, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "c30ecd03-6aeb-4c2e-8c3c-fa8846497a3a": {"__data__": {"id_": "c30ecd03-6aeb-4c2e-8c3c-fa8846497a3a", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 15, "parent_article_title": "KB-1 Text & Table", "attachment_id": "img_15_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "ecf4b284-422f-4385-838e-cd7835f291c9", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 15, "parent_article_title": "KB-1 Text & Table", "attachment_id": "img_15_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "hash": "afe85a7a80d5774b143cde13a1056a6d342d34b9e83b6e2fdeb8e11c4bcd772c", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_1.png\nFile Type: image/png\nURL: https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg\n\nProcessing failed: Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 349, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "43f719c3-78f6-4bc6-84e7-a8d5c7fcff07": {"__data__": {"id_": "43f719c3-78f6-4bc6-84e7-a8d5c7fcff07", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 15, "parent_article_title": "KB-1 Text & Table", "attachment_id": "img_15_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://hf-\nfiles-oregon.s3-us-\nwest-2.amazonaws.com/hdpdigitalwatchdog_kb_attachments/2019/09-24/4763eb2b-7c2d-49d6-bfc0-47620cc0f150/image.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 11165, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "377c1932-804e-45bb-b3d8-2d6c4c0cbe55", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 15, "parent_article_title": "KB-1 Text & Table", "attachment_id": "img_15_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://hf-\nfiles-oregon.s3-us-\nwest-2.amazonaws.com/hdpdigitalwatchdog_kb_attachments/2019/09-24/4763eb2b-7c2d-49d6-bfc0-47620cc0f150/image.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 11165, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "ae255fc4ac34ded08f4fcb88ccc05c646f71a6c9f2c0b22f425a3bbb50004229", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "!Image[The image shows a logo for a company called \"DIGITAL WATCHDOG\". The logo consists of a stylized graphic element formed by the letters \"DW\" in orange, with the full company name \"DIGITAL WATCHDOG\" written below it in large, bold, sans-serif font. The orange color of the logo contrasts sharply against a white background, creating a striking visual effect. The design is simple yet impactful, likely serving as a strong branding element for the company.]", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 460, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "a3cc4524-5f6b-4ff4-b0c8-617fe0f54a9a": {"__data__": {"id_": "a3cc4524-5f6b-4ff4-b0c8-617fe0f54a9a", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "PDF", "url": "https://meghanag.supporthive.com/kb/article/22-pdf/", "article_id": 22, "section_id": 2, "last_updated_at": "2025-06-16T09:01:30Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "147be61a-4fa8-4e02-871a-1bd66c172cf7", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "PDF", "url": "https://meghanag.supporthive.com/kb/article/22-pdf/", "article_id": 22, "section_id": 2, "last_updated_at": "2025-06-16T09:01:30Z", "has_attachments": true, "attachment_count": 1}, "hash": "c221f98bf900ef40f44012a543dc67506611ee2b91ecc14f516565cd873755d4", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "attached documents test", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 23, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "d8bf9650-a8cd-4d0f-a4e1-02277686d127": {"__data__": {"id_": "d8bf9650-a8cd-4d0f-a4e1-02277686d127", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "section_id": 2, "download_failed": true, "is_embedded_image": false}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "1d9b1636-0629-49e8-90b0-3aa88885c38a", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "section_id": 2, "download_failed": true, "is_embedded_image": false}, "hash": "a801616038c20676ecb574b3c77a62bd6ce1344d400591f5a8fb74224b1f0ecc", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Attachment: Manual_GP-PWM-10-FM.pdf\nFile Type: application/pdf\nURL: https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf\n\nDownload failed but attachment detected.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 247, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "820f3e81-3be2-45ec-95f7-90c28fb234b7": {"__data__": {"id_": "820f3e81-3be2-45ec-95f7-90c28fb234b7", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "PDF-TEST-2", "url": "https://meghanag.supporthive.com/kb/article/23-pdf-test-2/", "article_id": 23, "section_id": 2, "last_updated_at": "2025-06-19T11:05:28Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "a0c386e8-2aa7-49f9-89e1-c32b25aeb6b2", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "PDF-TEST-2", "url": "https://meghanag.supporthive.com/kb/article/23-pdf-test-2/", "article_id": 23, "section_id": 2, "last_updated_at": "2025-06-19T11:05:28Z", "has_attachments": true, "attachment_count": 1}, "hash": "e58004a99ad879347f48597e71c8d5d9f61d03ff57f368a4b28cbba77b0dbbd7", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "PDF TEST 2", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 10, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "573108c2-913f-4612-9a64-65e72615ef3a": {"__data__": {"id_": "573108c2-913f-4612-9a64-65e72615ef3a", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 23, "parent_article_title": "PDF-TEST-2", "attachment_id": 3, "attachment_name": "GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-19/7d02d733-d84f-4e17-a7a9-1d33c8a78798/GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "section_id": 2, "download_failed": true, "is_embedded_image": false}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "9dee0eb8-7436-4d54-b1ef-dbd3536deaf9", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 23, "parent_article_title": "PDF-TEST-2", "attachment_id": 3, "attachment_name": "GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-19/7d02d733-d84f-4e17-a7a9-1d33c8a78798/GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "section_id": 2, "download_failed": true, "is_embedded_image": false}, "hash": "4d704bf37710ec4f67abbbba948db3b1c24999dc03f01e29bac8eb3778d468ac", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Attachment: GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf\nFile Type: application/pdf\nURL: https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-19/7d02d733-d84f-4e17-a7a9-1d33c8a78798/GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf\n\nDownload failed but attachment detected.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 339, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "0ba1f768-e3d1-4b24-a9b2-7782463b40e2": {"__data__": {"id_": "0ba1f768-e3d1-4b24-a9b2-7782463b40e2", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample article", "url": "https://meghanag.supporthive.com/kb/article/12-sample-article/", "article_id": 12, "section_id": 2, "last_updated_at": "2025-04-22T05:44:42Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "c4487546-8f44-4009-ab58-b15294ecfe72", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample article", "url": "https://meghanag.supporthive.com/kb/article/12-sample-article/", "article_id": 12, "section_id": 2, "last_updated_at": "2025-04-22T05:44:42Z", "has_attachments": false, "attachment_count": 0}, "hash": "f1313277034e8b093b35b3352fa6f57c45a1829a90009999587d67527e82c0ae", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Understanding Your Billing Cycle\n\nYour billing cycle is determined by your signup date and plan:\n\n\\- Monthly plans renew every 30 days.  \n\\- Annual plans renew once every 12 months.  \n\\- You’ll be billed automatically unless auto-renew is turned off.  \n\\- Receipts are emailed after each successful payment.\n\nTo view your billing history, go to Settings > Billing > History.  \n\nHow to Delete Your Account\n\nWarning: Deleting your account is permanent.\n\nSteps to delete your account:\n\n1\\. Log in and go to Settings > Account.  \n2\\. <PERSON><PERSON> down and click on \"Delete My Account\".  \n3\\. Enter your password to confirm the action.  \n4\\. You will receive a confirmation email. Click the link to complete\ndeletion.\n\nYour data will be permanently removed within 7 days. This action is\nirreversible.  \n\nHow to change email address\n\nTo update your registered email address:\n\n1\\. Log in to your account.  \n2\\. Navigate to Settings > Profile.  \n3\\. Click \"Edit\" next to your email address.  \n4\\. Enter the new email and confirm.  \n5\\. A verification email will be sent to the new address.  \n6\\. Click the verification link to complete the update.\n\nNote: You must verify the new address before it takes effect.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1196, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "2ba31c7e-6ad1-41e0-96c9-ba43badfa2e7": {"__data__": {"id_": "2ba31c7e-6ad1-41e0-96c9-ba43badfa2e7", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample docx", "url": "https://meghanag.supporthive.com/kb/article/17-sample-docx/", "article_id": 17, "section_id": 2, "last_updated_at": "2025-05-12T06:18:44Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "f78ac40c-9c24-45a3-b17e-afa77a639a35", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample docx", "url": "https://meghanag.supporthive.com/kb/article/17-sample-docx/", "article_id": 17, "section_id": 2, "last_updated_at": "2025-05-12T06:18:44Z", "has_attachments": true, "attachment_count": 1}, "hash": "94daa94105e4847e0d84debabad77a2032e2b63fe25745cd0ec4a35de5556ae9", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "test docx", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 9, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "6d65832d-e81c-4db6-8078-1ab5674d6200": {"__data__": {"id_": "6d65832d-e81c-4db6-8078-1ab5674d6200", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 17, "parent_article_title": "sample docx", "attachment_id": 1, "attachment_name": "sample_kb_article.docx", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-12/69860b5a-91bb-4339-bf21-5b9c647d9053/sample_kb_article.docx", "mime_type": "application/octet-stream", "file_extension": ".docx", "file_size": 37001, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "c040e8c8-0269-424d-ba9a-429256d5a847", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 17, "parent_article_title": "sample docx", "attachment_id": 1, "attachment_name": "sample_kb_article.docx", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-12/69860b5a-91bb-4339-bf21-5b9c647d9053/sample_kb_article.docx", "mime_type": "application/octet-stream", "file_extension": ".docx", "file_size": 37001, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "db7d581f477a2bc6df40c80c33308ce401eebefc4e70ceee2abb42579aa5261e", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Attachment: sample_kb_article.docx\nFile Type: application/octet-stream\nSize: 37001 bytes\n\nContent parsing not supported for this file type.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 139, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8e7b84bb-ac2e-4179-8ed5-f640fc7dfccd": {"__data__": {"id_": "8e7b84bb-ac2e-4179-8ed5-f640fc7dfccd", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample image", "url": "https://meghanag.supporthive.com/kb/article/19-sample-image/", "article_id": 19, "section_id": 2, "last_updated_at": "2025-05-14T11:23:38Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "4b4c57df-0b82-40ae-8f54-e575232634fb", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample image", "url": "https://meghanag.supporthive.com/kb/article/19-sample-image/", "article_id": 19, "section_id": 2, "last_updated_at": "2025-05-14T11:23:38Z", "has_attachments": true, "attachment_count": 1}, "hash": "f75323da85e9730f572c96f760cd00697eaa56b143fbbf748f3b7d2d0f3c474b", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "![](https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-14/89f0213e-8ddf-415e-8bce-1f32bdbe8c43/Screenshot_2025-05-14_at_4.52.20PM.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 158, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "f65a6eca-c309-40d8-9671-ea1dc818569a": {"__data__": {"id_": "f65a6eca-c309-40d8-9671-ea1dc818569a", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 19, "parent_article_title": "sample image", "attachment_id": "img_19_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-14/89f0213e-8ddf-415e-8bce-1f32bdbe8c43/Screenshot_2025-05-14_at_4.52.20PM.png", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "download_failed": true, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "842f2795-7ce2-49c1-8063-657a49e9b2fb", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 19, "parent_article_title": "sample image", "attachment_id": "img_19_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-14/89f0213e-8ddf-415e-8bce-1f32bdbe8c43/Screenshot_2025-05-14_at_4.52.20PM.png", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "download_failed": true, "is_embedded_image": true}, "hash": "240f1af6e250c9e0bdb7ce4f5f25d1f9d2c285f51bb74b95a6075e8d85e2ca1e", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_1.png\nFile Type: image/png\nURL: https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-14/89f0213e-8ddf-415e-8bce-1f32bdbe8c43/Screenshot_2025-05-14_at_4.52.20PM.png\n\nDownload failed but image detected.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 253, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "bec190be-67d2-4bfc-bcd7-5e3b4d5d6282": {"__data__": {"id_": "bec190be-67d2-4bfc-bcd7-5e3b4d5d6282", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "segment", "url": "https://meghanag.supporthive.com/kb/article/20-segment/", "article_id": 20, "section_id": 2, "last_updated_at": "2025-05-15T15:07:55Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "16e50e50-74ff-46dd-ac1f-7e955e21ce01", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "segment", "url": "https://meghanag.supporthive.com/kb/article/20-segment/", "article_id": 20, "section_id": 2, "last_updated_at": "2025-05-15T15:07:55Z", "has_attachments": true, "attachment_count": 1}, "hash": "ed3f62cbe935a18eef58b651a4711eb818d9dcae7596c9f67608233353d93e3b", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "![](https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-15/9970e608-34ec-4d3e-8230-a0be6f858b31/Screenshot_2025-05-15_at_8.37.11PM.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 158, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "9aacb827-58ce-49e7-9c97-f9f9fb95c1ff": {"__data__": {"id_": "9aacb827-58ce-49e7-9c97-f9f9fb95c1ff", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 20, "parent_article_title": "segment", "attachment_id": "img_20_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-15/9970e608-34ec-4d3e-8230-a0be6f858b31/Screenshot_2025-05-15_at_8.37.11PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 94007, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "b10a44b3-8574-4a27-a6e2-75a63fa0b6af", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 20, "parent_article_title": "segment", "attachment_id": "img_20_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-15/9970e608-34ec-4d3e-8230-a0be6f858b31/Screenshot_2025-05-15_at_8.37.11PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 94007, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "4fbdd043bcdf1692d04d1e24c64b3b4fad1e25532021280d87e6778f44a21c12", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "# Getting Started with Segment\n\n1. Create a Segment account at segment.com\n2. Log in and go to the Dashboard\n3. Click on 'Add Source' to connect your app\n4. Choose your platform (iOS, Android, Web, etc.)\n5. Copy the generated write key\n6. Install the Segment SDK and initialize with the key\n7. Verify events in the Segment debugger", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 331, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4a6b2555-cd2b-4097-ad96-02f7c60d6111": {"__data__": {"id_": "4a6b2555-cd2b-4097-ad96-02f7c60d6111", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article", "url": "https://meghanag.supporthive.com/kb/article/1-test-article/", "article_id": 1, "section_id": 2, "last_updated_at": "2025-04-08T16:05:41Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "52c71446-e7f2-4e4e-acc2-39a3323bbfc1", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article", "url": "https://meghanag.supporthive.com/kb/article/1-test-article/", "article_id": 1, "section_id": 2, "last_updated_at": "2025-04-08T16:05:41Z", "has_attachments": false, "attachment_count": 0}, "hash": "ffa5c5d8d8cec1e1f4458b09a64779bd868870ac763e29aaadc2796130dfbd8d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "This is a sample knowledge base article to test the HappyFox API reader.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 72, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "c7cdc0ef-5d8c-45bd-a871-387ac92ba6d7": {"__data__": {"id_": "c7cdc0ef-5d8c-45bd-a871-387ac92ba6d7", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 2", "url": "https://meghanag.supporthive.com/kb/article/2-test-article-2/", "article_id": 2, "section_id": 2, "last_updated_at": "2025-04-09T10:10:39Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "46299bad-ab24-4ec2-953c-4c09592c2c0a", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 2", "url": "https://meghanag.supporthive.com/kb/article/2-test-article-2/", "article_id": 2, "section_id": 2, "last_updated_at": "2025-04-09T10:10:39Z", "has_attachments": false, "attachment_count": 0}, "hash": "7494f46e4dc66339eca63b3f344828bb960c709fab60fee43a26b072773fb5ea", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "`This is another test article to verify HappyFox reader updates.`", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 65, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "047305ab-f896-406c-995a-0503ee25888b": {"__data__": {"id_": "047305ab-f896-406c-995a-0503ee25888b", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 3", "url": "https://meghanag.supporthive.com/kb/article/3-test-article-3/", "article_id": 3, "section_id": 3, "last_updated_at": "2025-04-16T06:34:30Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "c7fbcac9-44af-4ea3-8dc8-a50cabd6e60e", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 3", "url": "https://meghanag.supporthive.com/kb/article/3-test-article-3/", "article_id": 3, "section_id": 3, "last_updated_at": "2025-04-16T06:34:30Z", "has_attachments": false, "attachment_count": 0}, "hash": "7de16eccb9a8b2cbc6f52743973366a2c2a4750f7755c5bafe60a7c0a0ef9174", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Having trouble logging into your account? Here are some common causes and\nquick fixes:\n\n1\\. Incorrect Username or Password****  \nDouble-check for typos. Passwords are case-sensitive.\n\n2\\. B<PERSON><PERSON> Cache and Cookies****  \nClear your browser's cache and cookies and try logging in again.\n\n3\\. Two-Factor Authentication Issues****  \nIf you're not receiving the OTP, check your spam folder or ensure your\nregistered phone number is correct.\n\n4\\. Account Lockouts  \nAfter multiple failed attempts, your account might be temporarily locked. Wait\n15 minutes before trying again.\n\nIf you're still having trouble, please contact support or reset your password\nusing the \"Forgot Password\" link.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 683, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "b8ac8bd0-8a0f-41e9-8bc6-d565edbed2bb": {"__data__": {"id_": "b8ac8bd0-8a0f-41e9-8bc6-d565edbed2bb", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 4", "url": "https://meghanag.supporthive.com/kb/article/4-test-article-4/", "article_id": 4, "section_id": 2, "last_updated_at": "2025-04-16T06:36:18Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "9ddd335b-3bc1-4ca7-9e1d-d682961ea00d", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 4", "url": "https://meghanag.supporthive.com/kb/article/4-test-article-4/", "article_id": 4, "section_id": 2, "last_updated_at": "2025-04-16T06:36:18Z", "has_attachments": false, "attachment_count": 0}, "hash": "f346b26dd871456feb7dba1fa4d576fdb2bbcff9fed3ac65eb84b798f05cb4ff", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Title:\n\nContent:\n\nIf you've forgotten the answers to your security questions or wish to update\nthem, follow the steps below:\n\n1\\. Log into your account using your username and password.  \n2\\. Navigate to Settings > Security Settings.  \n3\\. Click on \"Reset Security Questions\".  \n4\\. Enter your current password for verification.  \n5\\. Choose new questions from the dropdown and provide new answers.  \n6\\. Click Save to update.\n\nNote: If you're unable to access your account, please click on “Need Help?” on\nthe login screen and select \"Contact Support\" to verify your identity and\nrequest a manual reset.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 604, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "c57861d6-93f5-4b0c-b506-c773eb8e9d51": {"__data__": {"id_": "c57861d6-93f5-4b0c-b506-c773eb8e9d51", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 5", "url": "https://meghanag.supporthive.com/kb/article/5-test-article-5/", "article_id": 5, "section_id": 2, "last_updated_at": "2025-04-16T07:44:33Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "59d9fe73-9dd4-40f8-a07f-9db41c495a0a", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 5", "url": "https://meghanag.supporthive.com/kb/article/5-test-article-5/", "article_id": 5, "section_id": 2, "last_updated_at": "2025-04-16T07:44:33Z", "has_attachments": false, "attachment_count": 0}, "hash": "98a366847844471630118aeeff9aa1dbc676574bd387c0c200fcd0ed8f82bff2", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Updating your profile ensures your contact information and preferences stay\ncurrent.\n\nSteps to update your profile:\n\n1\\. Log in to your Helpdesk account.  \n2\\. Click on your profile icon in the top-right corner.  \n3\\. Select “My Profile” from the dropdown menu.  \n4\\. Update the following fields as needed:  \n\\- Full Name  \n\\- Email Address  \n\\- Phone Number  \n\\- Preferred Language  \n5\\. Click “Save Changes” at the bottom of the page.\n\nNotes:  \n\\- If you're unable to update certain fields (like email), contact support for\nhelp.  \n\\- Your changes may take a few minutes to reflect across the system.\n\nFor privacy reasons, make sure your profile does not contain sensitive or\nincorrect information.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 700, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "f64b23ac-981b-4f07-b432-5d29cc190f73": {"__data__": {"id_": "f64b23ac-981b-4f07-b432-5d29cc190f73", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "test image", "url": "https://meghanag.supporthive.com/kb/article/18-test-image/", "article_id": 18, "section_id": 2, "last_updated_at": "2025-05-13T04:34:16Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "d21b137b-0be5-4bfe-8bef-9d9af5bcb48d", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "test image", "url": "https://meghanag.supporthive.com/kb/article/18-test-image/", "article_id": 18, "section_id": 2, "last_updated_at": "2025-05-13T04:34:16Z", "has_attachments": true, "attachment_count": 1}, "hash": "9f3ae6b5402b9047fc4eac154bf9ebf7ed5fa61b2281644279125ccbdb614b98", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "![](https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-13/156de1d0-0a3e-4fbe-b6f1-fc888c203515/Screenshot_2025-05-13_at_10.03.18AM.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 159, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "83405229-960f-4d4f-bc1b-3db0309184e6": {"__data__": {"id_": "83405229-960f-4d4f-bc1b-3db0309184e6", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 18, "parent_article_title": "test image", "attachment_id": "img_18_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-13/156de1d0-0a3e-4fbe-b6f1-fc888c203515/Screenshot_2025-05-13_at_10.03.18AM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 266051, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "b29706a1-1982-4a7b-a5c9-9f77d57d6f0e", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 18, "parent_article_title": "test image", "attachment_id": "img_18_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-13/156de1d0-0a3e-4fbe-b6f1-fc888c203515/Screenshot_2025-05-13_at_10.03.18AM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 266051, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "0d697121bf222c9c77a13ee04bcab9162c1f7dd09448d1f088f753a2639ae0ba", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "# Setting up your online store\n\nAfter you've set up Shopify, most of the work is done.\n\n- Your online store will automatically use your Shopify settings for checkout and order fulfillment.\n- Your products will automatically appear on your online store.\n- Your online store is automatically assigned a unique myshopify domain website address. This looks like your-store-name.myshopify.com and it's based on the store name that you entered when you signed up.\n\nThere are a few steps you should follow before launching to make sure your online store is ready for customers.\n\n## Grow your business\n\nIf you need help setting up or customizing your online store, then you can hire a Shopify expert.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 692, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "0c98e712-fa92-4489-9d77-cb8c5f24905c": {"__data__": {"id_": "0c98e712-fa92-4489-9d77-cb8c5f24905c", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "url sample", "url": "https://meghanag.supporthive.com/kb/article/21-url-sample/", "article_id": 21, "section_id": 2, "last_updated_at": "2025-05-16T04:09:12Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "b4dbda57-cb02-4fc7-9dc0-7f63a83b994a", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "url sample", "url": "https://meghanag.supporthive.com/kb/article/21-url-sample/", "article_id": 21, "section_id": 2, "last_updated_at": "2025-05-16T04:09:12Z", "has_attachments": false, "attachment_count": 0}, "hash": "c7c848b616e153c8035355ea56b0a60c223b05bdeccd47754d93f9bd2a2ca173", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "<http://example.com>", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 20, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}}}