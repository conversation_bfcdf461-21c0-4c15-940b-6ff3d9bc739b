{"docstore/metadata": {"b7623a99-6886-4ee4-83e7-a217f29ef022": {"doc_hash": "fed439483c0cc24384562934cb28505168d786fa1acbc5769afd1c0e12835bcd"}, "e4f3510f-6cd8-4366-be53-60fd832db96d": {"doc_hash": "8a5494c441ece48633a53b75daf0b05311652c44f689c4ba9733268764f59dcc"}, "6627be6b-6f09-4626-ab91-ed5bd855b788": {"doc_hash": "2a28e58b4932f7c9607679250c65ac717418c470d4199269ada2af86dd087997"}, "b37cf1c3-9d58-447a-9ec6-779c15ee2b31": {"doc_hash": "0793cfd15db579cb4e293e94f2bc8560749057301bd65ac3e7e32502a4da3921"}, "a03cf2ed-611d-49cc-bf8b-2c3923d108ca": {"doc_hash": "b0b36b00f4924589f7716b0e660006c6f5a576940cfc53283c232b07727b2660"}, "f5fcf4b1-2fa9-4d66-ad4c-bda2e5fdad17": {"doc_hash": "d44a3ea2fc59d7eb8f9fa036a2871a8178290980950207be3df0ad42ab132548"}, "8e45c498-c26d-48b1-a025-0a3db975e255": {"doc_hash": "210ad9ca2605aa5359af49ece2a2e89183f9592a31acbc1859cc1afd5e5f586a"}, "ce8231ec-e06c-48ca-802c-f9275f545851": {"doc_hash": "d1fa6f7372ce8fb79760860e0cf454b837d8276b706a6b2863f4fcef925eb41b"}, "cdb4429a-cbf8-4c62-969f-f3ea968aa402": {"doc_hash": "84765eaa2bf28ee0ed61f27d804943888b25195481617e0254c86ecfe75183c0"}, "38a14b79-5b50-489a-b4f6-4ce07094ec17": {"doc_hash": "d3b545a0637468c4379facce80e3df6b2f67dba3b7fc970f037537e54453bfac"}, "541a3843-9b62-4253-a6fc-10943ee6a02a": {"doc_hash": "37c643e72e3100317c2bacb258f156458923a4bd28cfe4a598b976d1f7a298cc"}, "53000ebb-5054-456c-9f69-a3ca76c27da5": {"doc_hash": "3d8f0fd8446ed2b6f59eff6c9b4f3847538ba4acd8b12c0c51c06a25d48d2b6f"}, "944024ad-f109-4fc7-bc26-10dd8f2eb12d": {"doc_hash": "fec774e6ae9f4cad6c7b360c60b8ee472c47b05b05ef6d18d252c6f0d7d5fca4"}, "758744ea-14b1-48c4-bb1c-235bad923457": {"doc_hash": "f12aafbb8e54fcc0beddaed8f2b9df8697209e867fc57335f0f221d18cab303b"}, "b4cfc1fc-f10b-4f25-9fb2-e702e73a58fa": {"doc_hash": "3aed720d6e0e2ffdd14309527b62d62a78e7d965fbcba030ed04a28208a2a09f"}, "c9bd71cc-1bf5-482a-9dc2-9178bef75fbe": {"doc_hash": "18ee9a3097f303d498e049cee8d2ebdf47aa80835580242d7899529203712d90"}, "c4832127-a552-47c6-9d93-ec5ce661498a": {"doc_hash": "c221f98bf900ef40f44012a543dc67506611ee2b91ecc14f516565cd873755d4"}, "94ada80e-7e09-4106-b635-6e721cb9eb97": {"doc_hash": "a801616038c20676ecb574b3c77a62bd6ce1344d400591f5a8fb74224b1f0ecc"}, "b18dd1f8-f170-4cb7-b72d-53c4da0bde5d": {"doc_hash": "e58004a99ad879347f48597e71c8d5d9f61d03ff57f368a4b28cbba77b0dbbd7"}, "aa6be186-3d22-4fb4-8f91-39cf5b172f74": {"doc_hash": "ac2111a7441032070f439e660c917d4ab77ac5eddc34b07d3e2e69dfb6ac80c7"}, "7c7e830e-f1b2-41ba-9732-61e105c54aa9": {"doc_hash": "94daa94105e4847e0d84debabad77a2032e2b63fe25745cd0ec4a35de5556ae9"}, "4cb8e1b6-7da9-447e-adeb-93e23734a31b": {"doc_hash": "db7d581f477a2bc6df40c80c33308ce401eebefc4e70ceee2abb42579aa5261e"}, "062fc48a-860f-44c8-a773-cf861eef3e19": {"doc_hash": "f75323da85e9730f572c96f760cd00697eaa56b143fbbf748f3b7d2d0f3c474b"}, "15cf7417-0093-4728-b93d-b852f31590ec": {"doc_hash": "240f1af6e250c9e0bdb7ce4f5f25d1f9d2c285f51bb74b95a6075e8d85e2ca1e"}, "39d02ba2-e676-4d64-a2b1-02e4e02b62f3": {"doc_hash": "ed3f62cbe935a18eef58b651a4711eb818d9dcae7596c9f67608233353d93e3b"}, "b9b52d7c-c83d-42a2-bc69-be9711dbe71a": {"doc_hash": "4fbdd043bcdf1692d04d1e24c64b3b4fad1e25532021280d87e6778f44a21c12"}, "fce54a93-8eac-4301-a3b3-ee12d81e578b": {"doc_hash": "ffa5c5d8d8cec1e1f4458b09a64779bd868870ac763e29aaadc2796130dfbd8d"}, "0f562da1-76b3-4fad-bfa5-5bdd9ae21dd7": {"doc_hash": "7494f46e4dc66339eca63b3f344828bb960c709fab60fee43a26b072773fb5ea"}, "1ccec29d-339f-4f72-a6dd-54dd713e8544": {"doc_hash": "7de16eccb9a8b2cbc6f52743973366a2c2a4750f7755c5bafe60a7c0a0ef9174"}, "dd040cbd-7618-47e1-a654-3e6f76825547": {"doc_hash": "f346b26dd871456feb7dba1fa4d576fdb2bbcff9fed3ac65eb84b798f05cb4ff"}, "0b7b19bf-a866-4cd5-be64-85f46d7feb20": {"doc_hash": "98a366847844471630118aeeff9aa1dbc676574bd387c0c200fcd0ed8f82bff2"}, "5b91b3f6-c851-439c-8179-61240a8680c1": {"doc_hash": "9f3ae6b5402b9047fc4eac154bf9ebf7ed5fa61b2281644279125ccbdb614b98"}, "a10b4a94-9113-4dfa-89a6-fd7a90126941": {"doc_hash": "0d697121bf222c9c77a13ee04bcab9162c1f7dd09448d1f088f753a2639ae0ba"}, "c247c53c-fa97-4fef-8020-388cd1e3013b": {"doc_hash": "c7c848b616e153c8035355ea56b0a60c223b05bdeccd47754d93f9bd2a2ca173"}, "02553acd-19c3-46fb-b86b-62bab79fcd81": {"doc_hash": "46a86abed7a8aa7e65c89298e6a89c76dff351da675d4390ab989113dfee86cf", "ref_doc_id": "b7623a99-6886-4ee4-83e7-a217f29ef022"}, "2d9a2f99-8348-4d28-9d92-3ae998fc194a": {"doc_hash": "e507945e2b39e3d6090cdfaad4a09752efad2de4e59be21ace9ef2e9a7aa4eec", "ref_doc_id": "e4f3510f-6cd8-4366-be53-60fd832db96d"}, "2e6526ae-863d-4044-b1eb-6c2a4f9801bd": {"doc_hash": "e8076cdbf063a169a85606ab841d0b0dbe7f0ae8d5923f8d17cbadc695115ebc", "ref_doc_id": "6627be6b-6f09-4626-ab91-ed5bd855b788"}, "69e710d4-dc02-450d-8b4f-28890c952e14": {"doc_hash": "d5252924f1f1e32df134e8dbdcee45f184c8bd2d9701c56ed009040c2737e40d", "ref_doc_id": "b37cf1c3-9d58-447a-9ec6-779c15ee2b31"}, "758b4cbe-8f2e-4823-8cda-5939746d6192": {"doc_hash": "580d2a1b32e339c6fe97da39b5b350b3d221e4e7d99946f350d866a1c8c6004a", "ref_doc_id": "a03cf2ed-611d-49cc-bf8b-2c3923d108ca"}, "af7f4bcc-c290-4cff-9197-e206547dad70": {"doc_hash": "dbdd725a2dc3eb9927e4dec367fa659933c9d465f94e5acd6e761f2c6253cc0a", "ref_doc_id": "f5fcf4b1-2fa9-4d66-ad4c-bda2e5fdad17"}, "efe04166-7965-4d69-a3c0-91ceb217e9a1": {"doc_hash": "5f68e1b4f5c3568fdf5c92b7b5568a53fe10ad316ef1a1b4467f212b2dda586e", "ref_doc_id": "8e45c498-c26d-48b1-a025-0a3db975e255"}, "b8f3e5c7-162e-4ccb-b75f-fdcb97e95d78": {"doc_hash": "f6dc14c93bcd9bf78bf64b038c998188d24dd1c2eeddcb4b25d5567d4ba9ed5f", "ref_doc_id": "ce8231ec-e06c-48ca-802c-f9275f545851"}, "abd08c21-674b-473a-a1ae-ce1a3e5f707a": {"doc_hash": "81547d30984b81ba7fc29cccfe974d8bd9da3459fa401b2c1cce79ef261b266b", "ref_doc_id": "cdb4429a-cbf8-4c62-969f-f3ea968aa402"}, "36178db6-58b8-4111-859e-12ed98afde07": {"doc_hash": "c4db49f9cbc88b92c505aa3c33817fec71b2bf669ce49feb4fb2920649f22c69", "ref_doc_id": "38a14b79-5b50-489a-b4f6-4ce07094ec17"}, "b473f8bf-c7fe-40e9-bdf2-b49ebe82cd2c": {"doc_hash": "bf42c007346bd8734d40e173ff2b25957a1fcdcb6b66a2a5d49fa8664ab135df", "ref_doc_id": "541a3843-9b62-4253-a6fc-10943ee6a02a"}, "4e2d10b3-2605-4e8f-8988-4c197b3f5d8c": {"doc_hash": "ac85faaeb48acd761412c4d5b7c89c0dac2316a0863ca23ca7f1d8c3cc98774d", "ref_doc_id": "53000ebb-5054-456c-9f69-a3ca76c27da5"}, "52b40780-efb9-4f62-a4f0-9ec2dca3fc4b": {"doc_hash": "006639b0301245b1a65425b30dd201c43067eea5724110d5bae59e4512840d8f", "ref_doc_id": "53000ebb-5054-456c-9f69-a3ca76c27da5"}, "16fb866f-3c65-427a-85c8-eaf63869ef4e": {"doc_hash": "801beb1ebd8d52cc9f0b9db4be01244bdde142b093dc546d4b4c9e31e3ca7cb2", "ref_doc_id": "944024ad-f109-4fc7-bc26-10dd8f2eb12d"}, "8c8c2ba8-7ba9-4bb5-a89f-002131dadbc6": {"doc_hash": "380822a7a70728c26afaa50a184e89b91b85a12b1244c75893042ecdee7927b5", "ref_doc_id": "758744ea-14b1-48c4-bb1c-235bad923457"}, "526d7903-5936-4393-b9fd-095d66a21b5d": {"doc_hash": "ffd35b4a5562b5aa2098f31afae8a7cd3af41bea738a0627832be7d97a4e0bcd", "ref_doc_id": "b4cfc1fc-f10b-4f25-9fb2-e702e73a58fa"}, "66fa2897-6b63-4510-bd31-4eb16eb9ec4e": {"doc_hash": "7e3698c3890c86b367e7a230f82efa3ee78d46c186b171dca9ec825a146eb13b", "ref_doc_id": "c9bd71cc-1bf5-482a-9dc2-9178bef75fbe"}, "5c6c92c7-2abb-4f08-9e91-4800d90d1a08": {"doc_hash": "56884a4203e0089facc2d04d368187846771372c63936324526db21729966866", "ref_doc_id": "c9bd71cc-1bf5-482a-9dc2-9178bef75fbe"}, "300ab3c8-3353-4684-9027-a4d8c66c497a": {"doc_hash": "7ea12b0a0dce3160de2a5546d4087447c0036dda87c7f9e79a90bd0ab368933c", "ref_doc_id": "c4832127-a552-47c6-9d93-ec5ce661498a"}, "38cefedd-8c21-48a1-8d0e-777453d1e93e": {"doc_hash": "e2b802a7056aca94b821cde40be61f21ac0b56462fab1a1e0dde82aa717ce046", "ref_doc_id": "94ada80e-7e09-4106-b635-6e721cb9eb97"}, "bc084c92-2155-44f1-bd44-a8d20d8cbfd2": {"doc_hash": "927a954ff995dcfc7cf4fdcc8f88641d6eac64c08cdd1dc7afc6d6decd6a33e6", "ref_doc_id": "b18dd1f8-f170-4cb7-b72d-53c4da0bde5d"}, "633cf3a1-630f-4a50-8209-06135478c096": {"doc_hash": "4210dc1f05e133141f92b5691ed9159e48cc9269edbf59579b572d441c57122b", "ref_doc_id": "aa6be186-3d22-4fb4-8f91-39cf5b172f74"}, "b3e8e03d-97d9-43ae-9df8-62de3a0464c1": {"doc_hash": "2ab1850737d3609c5f41fa0cf2759c2a17665313f854986b3215651971792729", "ref_doc_id": "aa6be186-3d22-4fb4-8f91-39cf5b172f74"}, "a0cb552d-47b8-42ff-8a15-e9ef08c13b30": {"doc_hash": "86a0356f46a26bf84ab41622eab325a45c14a8bd96bea91f4841c04b2dcbcb74", "ref_doc_id": "aa6be186-3d22-4fb4-8f91-39cf5b172f74"}, "8a7970d0-983e-4aca-9463-6f6fc5995255": {"doc_hash": "a6e973d325ead7165aac76d22a0111f42ca8fc9fd1593b03012e2fd6ff3dfcc1", "ref_doc_id": "aa6be186-3d22-4fb4-8f91-39cf5b172f74"}, "ff5126a3-ba2d-4083-9d22-407e0378fd1b": {"doc_hash": "c54ef56571301d2ad42e3a8d6700e2c0f3886e35f6ddac066ff10649210fc3fd", "ref_doc_id": "7c7e830e-f1b2-41ba-9732-61e105c54aa9"}, "a83e28f0-a5e1-4e55-af39-9c64b9f21a9d": {"doc_hash": "454c25b58c793ed7fb080579bc69efd77c2571d188c5318e9eb641be479a9c1d", "ref_doc_id": "4cb8e1b6-7da9-447e-adeb-93e23734a31b"}, "9ece5fc0-844c-4edf-8b76-2c2a91a83c83": {"doc_hash": "91a27c3fe45cf1b50a38ad54750809580c10c7eef5af32b7bca0ec0cf5102bc5", "ref_doc_id": "062fc48a-860f-44c8-a773-cf861eef3e19"}, "fbc9def7-2ebd-4ace-844a-0fc3bc84b115": {"doc_hash": "48246388c7e853bbb24325ff97c36bc61958ed84e4003b512189d6ef60cd89ee", "ref_doc_id": "15cf7417-0093-4728-b93d-b852f31590ec"}, "e13e19ce-0101-4001-a422-d9acc38d5139": {"doc_hash": "8861be49b0d32ecd06406bbe7402a4a0afc00fd5b215cfcd6d7b9d54f7f35a03", "ref_doc_id": "39d02ba2-e676-4d64-a2b1-02e4e02b62f3"}, "8b4b0bab-4c2b-4ebf-ac6f-dfeea7976e91": {"doc_hash": "8e29a96e1a6da7442122e44029073d976986daf959d825823cad3c471eb19650", "ref_doc_id": "b9b52d7c-c83d-42a2-bc69-be9711dbe71a"}, "d1a59a00-c730-4f26-8b96-01de7b504f65": {"doc_hash": "95086298c5e83729393e48d3a01de69be652ccb0cce87c2d3a5413b62d44a0c7", "ref_doc_id": "fce54a93-8eac-4301-a3b3-ee12d81e578b"}, "f972a30d-b37d-47fe-ae6a-af7cbbc1aea3": {"doc_hash": "cc07e8ae0ab0d9cdc301204db87ada4184fb6efa7efae198b015e134ea4ecb13", "ref_doc_id": "0f562da1-76b3-4fad-bfa5-5bdd9ae21dd7"}, "3deb0654-7cff-46ad-9975-fbc1e013a6fb": {"doc_hash": "4c3c29260dddb6bfa73494e12a4ae1ea63dd7c3b12eb1657e52cec74e3c81b66", "ref_doc_id": "1ccec29d-339f-4f72-a6dd-54dd713e8544"}, "f752c0ba-57e5-4c23-ab09-21f4650cc1b1": {"doc_hash": "a353a7a35addd8b5c7e01934926d0c33b93337401bc2f3fe8f3533f731ea51f5", "ref_doc_id": "dd040cbd-7618-47e1-a654-3e6f76825547"}, "4e5966f6-484d-40a6-a653-e0aaad6aa77a": {"doc_hash": "02209da8691416a3d7d77a59fc3caea5741b8db37d9e53370217d3272ea3355f", "ref_doc_id": "0b7b19bf-a866-4cd5-be64-85f46d7feb20"}, "68132a5f-44d0-4480-957c-c8c7833dfbbb": {"doc_hash": "a8986d8d95970402beb17d2ff2c413949926923508ae05cc26a00badb9dedd10", "ref_doc_id": "5b91b3f6-c851-439c-8179-61240a8680c1"}, "81700bd3-2fab-4127-ac76-a52c01aed172": {"doc_hash": "6a5907e069bcc416fe382af8f3f42a9b706a566d9b626cf5ca16fd4881ae0e4d", "ref_doc_id": "a10b4a94-9113-4dfa-89a6-fd7a90126941"}, "f1cde0af-6a56-4d9a-b12c-79c3ec286a5a": {"doc_hash": "cc72d71f09827b9aa6fe3bbafb3817c568b27d20f421d95306ed48190c7e874b", "ref_doc_id": "c247c53c-fa97-4fef-8020-388cd1e3013b"}}, "docstore/ref_doc_info": {"b7623a99-6886-4ee4-83e7-a217f29ef022": {"node_ids": ["02553acd-19c3-46fb-b86b-62bab79fcd81"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Change Your Email Preferences", "url": "https://meghanag.supporthive.com/kb/article/9-how-to-change-your-email-preferences/", "article_id": 9, "section_id": 2, "last_updated_at": "2025-04-18T03:44:32Z", "has_attachments": false, "attachment_count": 0}}, "e4f3510f-6cd8-4366-be53-60fd832db96d": {"node_ids": ["2d9a2f99-8348-4d28-9d92-3ae998fc194a"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password", "url": "https://meghanag.supporthive.com/kb/article/6-how-to-reset-your-password/", "article_id": 6, "section_id": 2, "last_updated_at": "2025-04-18T03:41:50Z", "has_attachments": false, "attachment_count": 0}}, "6627be6b-6f09-4626-ab91-ed5bd855b788": {"node_ids": ["2e6526ae-863d-4044-b1eb-6c2a4f9801bd"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password in HappyFox", "url": "https://meghanag.supporthive.com/kb/article/11-how-to-reset-your-password-in-happyfox/", "article_id": 11, "section_id": 2, "last_updated_at": "2025-04-18T07:53:43Z", "has_attachments": false, "attachment_count": 0}}, "b37cf1c3-9d58-447a-9ec6-779c15ee2b31": {"node_ids": ["69e710d4-dc02-450d-8b4f-28890c952e14"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Submit a Ticket for Support", "url": "https://meghanag.supporthive.com/kb/article/8-how-to-submit-a-ticket-for-support/", "article_id": 8, "section_id": 2, "last_updated_at": "2025-04-18T03:43:50Z", "has_attachments": false, "attachment_count": 0}}, "a03cf2ed-611d-49cc-bf8b-2c3923d108ca": {"node_ids": ["758b4cbe-8f2e-4823-8cda-5939746d6192"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Update Your Contact Information", "url": "https://meghanag.supporthive.com/kb/article/7-how-to-update-your-contact-information/", "article_id": 7, "section_id": 2, "last_updated_at": "2025-04-18T03:42:44Z", "has_attachments": false, "attachment_count": 0}}, "f5fcf4b1-2fa9-4d66-ad4c-bda2e5fdad17": {"node_ids": ["af7f4bcc-c290-4cff-9197-e206547dad70"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Use Two-Factor Authentication (2FA)", "url": "https://meghanag.supporthive.com/kb/article/10-how-to-use-two-factor-authentication-2fa/", "article_id": 10, "section_id": 2, "last_updated_at": "2025-04-18T03:45:13Z", "has_attachments": false, "attachment_count": 0}}, "8e45c498-c26d-48b1-a025-0a3db975e255": {"node_ids": ["efe04166-7965-4d69-a3c0-91ceb217e9a1"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Tables as image and images", "url": "https://meghanag.supporthive.com/kb/article/16-kb-1-tables-as-image-and-images/", "article_id": 16, "section_id": 2, "last_updated_at": "2025-04-29T06:01:25Z", "has_attachments": true, "attachment_count": 4}}, "ce8231ec-e06c-48ca-802c-f9275f545851": {"node_ids": ["b8f3e5c7-162e-4ccb-b75f-fdcb97e95d78"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}}, "cdb4429a-cbf8-4c62-969f-f3ea968aa402": {"node_ids": ["abd08c21-674b-473a-a1ae-ce1a3e5f707a"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}}, "38a14b79-5b50-489a-b4f6-4ce07094ec17": {"node_ids": ["36178db6-58b8-4111-859e-12ed98afde07"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/37f48d3f-8d70-400d-8ed6-181972e8a8e4/Screen_Shot_2022-07-13_at_12.35.04_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 53841, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}}, "541a3843-9b62-4253-a6fc-10943ee6a02a": {"node_ids": ["b473f8bf-c7fe-40e9-bdf2-b49ebe82cd2c"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-\nfiles-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/681a6847-eac3-485e-8ef7-3ab20542f58e/Duralite-100E_Expansion_Kit.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 1615879, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}}, "53000ebb-5054-456c-9f69-a3ca76c27da5": {"node_ids": ["4e2d10b3-2605-4e8f-8988-4c197b3f5d8c", "52b40780-efb9-4f62-a4f0-9ec2dca3fc4b"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}}, "944024ad-f109-4fc7-bc26-10dd8f2eb12d": {"node_ids": ["16fb866f-3c65-427a-85c8-eaf63869ef4e"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}}, "758744ea-14b1-48c4-bb1c-235bad923457": {"node_ids": ["8c8c2ba8-7ba9-4bb5-a89f-002131dadbc6"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}}, "b4cfc1fc-f10b-4f25-9fb2-e702e73a58fa": {"node_ids": ["526d7903-5936-4393-b9fd-095d66a21b5d"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 176549, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}}, "c9bd71cc-1bf5-482a-9dc2-9178bef75fbe": {"node_ids": ["66fa2897-6b63-4510-bd31-4eb16eb9ec4e", "5c6c92c7-2abb-4f08-9e91-4800d90d1a08"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 213826, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}}, "c4832127-a552-47c6-9d93-ec5ce661498a": {"node_ids": ["300ab3c8-3353-4684-9027-a4d8c66c497a"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "PDF", "url": "https://meghanag.supporthive.com/kb/article/22-pdf/", "article_id": 22, "section_id": 2, "last_updated_at": "2025-06-16T09:01:30Z", "has_attachments": true, "attachment_count": 1}}, "94ada80e-7e09-4106-b635-6e721cb9eb97": {"node_ids": ["38cefedd-8c21-48a1-8d0e-777453d1e93e"], "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "section_id": 2, "download_failed": true, "is_embedded_image": false}}, "b18dd1f8-f170-4cb7-b72d-53c4da0bde5d": {"node_ids": ["bc084c92-2155-44f1-bd44-a8d20d8cbfd2"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "PDF-TEST-2", "url": "https://meghanag.supporthive.com/kb/article/23-pdf-test-2/", "article_id": 23, "section_id": 2, "last_updated_at": "2025-06-19T11:05:28Z", "has_attachments": true, "attachment_count": 1}}, "aa6be186-3d22-4fb4-8f91-39cf5b172f74": {"node_ids": ["633cf3a1-630f-4a50-8209-06135478c096", "b3e8e03d-97d9-43ae-9df8-62de3a0464c1", "a0cb552d-47b8-42ff-8a15-e9ef08c13b30", "8a7970d0-983e-4aca-9463-6f6fc5995255"], "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 23, "parent_article_title": "PDF-TEST-2", "attachment_id": 3, "attachment_name": "GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-19/7d02d733-d84f-4e17-a7a9-1d33c8a78798/GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 528258, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}}, "7c7e830e-f1b2-41ba-9732-61e105c54aa9": {"node_ids": ["ff5126a3-ba2d-4083-9d22-407e0378fd1b"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample docx", "url": "https://meghanag.supporthive.com/kb/article/17-sample-docx/", "article_id": 17, "section_id": 2, "last_updated_at": "2025-05-12T06:18:44Z", "has_attachments": true, "attachment_count": 1}}, "4cb8e1b6-7da9-447e-adeb-93e23734a31b": {"node_ids": ["a83e28f0-a5e1-4e55-af39-9c64b9f21a9d"], "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 17, "parent_article_title": "sample docx", "attachment_id": 1, "attachment_name": "sample_kb_article.docx", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-12/69860b5a-91bb-4339-bf21-5b9c647d9053/sample_kb_article.docx", "mime_type": "application/octet-stream", "file_extension": ".docx", "file_size": 37001, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}}, "062fc48a-860f-44c8-a773-cf861eef3e19": {"node_ids": ["9ece5fc0-844c-4edf-8b76-2c2a91a83c83"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample image", "url": "https://meghanag.supporthive.com/kb/article/19-sample-image/", "article_id": 19, "section_id": 2, "last_updated_at": "2025-05-14T11:23:38Z", "has_attachments": true, "attachment_count": 1}}, "15cf7417-0093-4728-b93d-b852f31590ec": {"node_ids": ["fbc9def7-2ebd-4ace-844a-0fc3bc84b115"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 19, "parent_article_title": "sample image", "attachment_id": "img_19_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-14/89f0213e-8ddf-415e-8bce-1f32bdbe8c43/Screenshot_2025-05-14_at_4.52.20PM.png", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "download_failed": true, "is_embedded_image": true}}, "39d02ba2-e676-4d64-a2b1-02e4e02b62f3": {"node_ids": ["e13e19ce-0101-4001-a422-d9acc38d5139"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "segment", "url": "https://meghanag.supporthive.com/kb/article/20-segment/", "article_id": 20, "section_id": 2, "last_updated_at": "2025-05-15T15:07:55Z", "has_attachments": true, "attachment_count": 1}}, "b9b52d7c-c83d-42a2-bc69-be9711dbe71a": {"node_ids": ["8b4b0bab-4c2b-4ebf-ac6f-dfeea7976e91"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 20, "parent_article_title": "segment", "attachment_id": "img_20_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-15/9970e608-34ec-4d3e-8230-a0be6f858b31/Screenshot_2025-05-15_at_8.37.11PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 94007, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}}, "fce54a93-8eac-4301-a3b3-ee12d81e578b": {"node_ids": ["d1a59a00-c730-4f26-8b96-01de7b504f65"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article", "url": "https://meghanag.supporthive.com/kb/article/1-test-article/", "article_id": 1, "section_id": 2, "last_updated_at": "2025-04-08T16:05:41Z", "has_attachments": false, "attachment_count": 0}}, "0f562da1-76b3-4fad-bfa5-5bdd9ae21dd7": {"node_ids": ["f972a30d-b37d-47fe-ae6a-af7cbbc1aea3"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 2", "url": "https://meghanag.supporthive.com/kb/article/2-test-article-2/", "article_id": 2, "section_id": 2, "last_updated_at": "2025-04-09T10:10:39Z", "has_attachments": false, "attachment_count": 0}}, "1ccec29d-339f-4f72-a6dd-54dd713e8544": {"node_ids": ["3deb0654-7cff-46ad-9975-fbc1e013a6fb"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 3", "url": "https://meghanag.supporthive.com/kb/article/3-test-article-3/", "article_id": 3, "section_id": 3, "last_updated_at": "2025-04-16T06:34:30Z", "has_attachments": false, "attachment_count": 0}}, "dd040cbd-7618-47e1-a654-3e6f76825547": {"node_ids": ["f752c0ba-57e5-4c23-ab09-21f4650cc1b1"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 4", "url": "https://meghanag.supporthive.com/kb/article/4-test-article-4/", "article_id": 4, "section_id": 2, "last_updated_at": "2025-04-16T06:36:18Z", "has_attachments": false, "attachment_count": 0}}, "0b7b19bf-a866-4cd5-be64-85f46d7feb20": {"node_ids": ["4e5966f6-484d-40a6-a653-e0aaad6aa77a"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 5", "url": "https://meghanag.supporthive.com/kb/article/5-test-article-5/", "article_id": 5, "section_id": 2, "last_updated_at": "2025-04-16T07:44:33Z", "has_attachments": false, "attachment_count": 0}}, "5b91b3f6-c851-439c-8179-61240a8680c1": {"node_ids": ["68132a5f-44d0-4480-957c-c8c7833dfbbb"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "test image", "url": "https://meghanag.supporthive.com/kb/article/18-test-image/", "article_id": 18, "section_id": 2, "last_updated_at": "2025-05-13T04:34:16Z", "has_attachments": true, "attachment_count": 1}}, "a10b4a94-9113-4dfa-89a6-fd7a90126941": {"node_ids": ["81700bd3-2fab-4127-ac76-a52c01aed172"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 18, "parent_article_title": "test image", "attachment_id": "img_18_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-13/156de1d0-0a3e-4fbe-b6f1-fc888c203515/Screenshot_2025-05-13_at_10.03.18AM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 266051, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}}, "c247c53c-fa97-4fef-8020-388cd1e3013b": {"node_ids": ["f1cde0af-6a56-4d9a-b12c-79c3ec286a5a"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "url sample", "url": "https://meghanag.supporthive.com/kb/article/21-url-sample/", "article_id": 21, "section_id": 2, "last_updated_at": "2025-05-16T04:09:12Z", "has_attachments": false, "attachment_count": 0}}}, "docstore/data": {"02553acd-19c3-46fb-b86b-62bab79fcd81": {"__data__": {"id_": "02553acd-19c3-46fb-b86b-62bab79fcd81", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Change Your Email Preferences", "url": "https://meghanag.supporthive.com/kb/article/9-how-to-change-your-email-preferences/", "article_id": 9, "section_id": 2, "last_updated_at": "2025-04-18T03:44:32Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "b7623a99-6886-4ee4-83e7-a217f29ef022", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Change Your Email Preferences", "url": "https://meghanag.supporthive.com/kb/article/9-how-to-change-your-email-preferences/", "article_id": 9, "section_id": 2, "last_updated_at": "2025-04-18T03:44:32Z", "has_attachments": false, "attachment_count": 0}, "hash": "fed439483c0cc24384562934cb28505168d786fa1acbc5769afd1c0e12835bcd", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "To customize your email preferences, follow these steps:\n\n  1. Log in to your account.\n\n  2. Navigate to \"Settings\" in the top-right corner.\n\n  3. Click on \"Email Preferences.\"\n\n  4. Choose which types of emails you’d like to receive (e.g., system alerts, updates, newsletters).\n\n  5. Save your changes.\n\n**Troubleshooting:**\n\n  * If you no longer want to receive certain types of emails, make sure to unsubscribe or adjust your email preferences.\n\n  * If emails are being marked as spam, whitelist our email address in your email client.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 538, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "2d9a2f99-8348-4d28-9d92-3ae998fc194a": {"__data__": {"id_": "2d9a2f99-8348-4d28-9d92-3ae998fc194a", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password", "url": "https://meghanag.supporthive.com/kb/article/6-how-to-reset-your-password/", "article_id": 6, "section_id": 2, "last_updated_at": "2025-04-18T03:41:50Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "e4f3510f-6cd8-4366-be53-60fd832db96d", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password", "url": "https://meghanag.supporthive.com/kb/article/6-how-to-reset-your-password/", "article_id": 6, "section_id": 2, "last_updated_at": "2025-04-18T03:41:50Z", "has_attachments": false, "attachment_count": 0}, "hash": "8a5494c441ece48633a53b75daf0b05311652c44f689c4ba9733268764f59dcc", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "If you’ve forgotten your password or wish to change it for security reasons,\nfollow these steps to reset your password:\n\n  1. Go to the login page.\n\n  2. Click on the \"Forgot Password?\" link.\n\n  3. Enter your registered email address.\n\n  4. Check your inbox for a password reset email.\n\n  5. Click on the \"Reset Password\" link in the email.\n\n  6. Enter a new password and confirm it.\n\n  7. Log in with your new password.\n\n**Troubleshooting:**\n\n  * If you do not receive the reset email, check your spam or junk folder.\n\n  * Ensure you’ve entered the correct email address linked to your account.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 595, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "2e6526ae-863d-4044-b1eb-6c2a4f9801bd": {"__data__": {"id_": "2e6526ae-863d-4044-b1eb-6c2a4f9801bd", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password in HappyFox", "url": "https://meghanag.supporthive.com/kb/article/11-how-to-reset-your-password-in-happyfox/", "article_id": 11, "section_id": 2, "last_updated_at": "2025-04-18T07:53:43Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "6627be6b-6f09-4626-ab91-ed5bd855b788", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password in HappyFox", "url": "https://meghanag.supporthive.com/kb/article/11-how-to-reset-your-password-in-happyfox/", "article_id": 11, "section_id": 2, "last_updated_at": "2025-04-18T07:53:43Z", "has_attachments": false, "attachment_count": 0}, "hash": "2a28e58b4932f7c9607679250c65ac717418c470d4199269ada2af86dd087997", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "If you need to reset your password for your HappyFox account, follow these\nsteps:\n\n  1. Go to the login page.\n\n  2. Click on the \"Forgot Password\" link.\n\n  3. Enter your registered email address.\n\n  4. You'll receive an email with a link to reset your password.\n\n  5. Click the link, and you'll be able to set a new password.\n\n**Note:** If you do not receive the email within a few minutes, check your\nspam folder or try again later.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 433, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "69e710d4-dc02-450d-8b4f-28890c952e14": {"__data__": {"id_": "69e710d4-dc02-450d-8b4f-28890c952e14", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Submit a Ticket for Support", "url": "https://meghanag.supporthive.com/kb/article/8-how-to-submit-a-ticket-for-support/", "article_id": 8, "section_id": 2, "last_updated_at": "2025-04-18T03:43:50Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "b37cf1c3-9d58-447a-9ec6-779c15ee2b31", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Submit a Ticket for Support", "url": "https://meghanag.supporthive.com/kb/article/8-how-to-submit-a-ticket-for-support/", "article_id": 8, "section_id": 2, "last_updated_at": "2025-04-18T03:43:50Z", "has_attachments": false, "attachment_count": 0}, "hash": "0793cfd15db579cb4e293e94f2bc8560749057301bd65ac3e7e32502a4da3921", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "If you need further assistance, submitting a support ticket is the best way to\nget help from our team:\n\n  1. Log in to your account.\n\n  2. Go to the \"Support\" section.\n\n  3. Click on \"Submit a Ticket.\"\n\n  4. Select a category for your issue.\n\n  5. Fill out the ticket form with all relevant details.\n\n  6. Click \"Submit\" to send your ticket to our support team.\n\n  7. You will receive a confirmation email with a ticket number.\n\n**Troubleshooting:**\n\n  * If you do not see the ticket form, ensure you are logged in to your account.\n\n  * Make sure to provide as much detail as possible to expedite the resolution process.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 620, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "758b4cbe-8f2e-4823-8cda-5939746d6192": {"__data__": {"id_": "758b4cbe-8f2e-4823-8cda-5939746d6192", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Update Your Contact Information", "url": "https://meghanag.supporthive.com/kb/article/7-how-to-update-your-contact-information/", "article_id": 7, "section_id": 2, "last_updated_at": "2025-04-18T03:42:44Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "a03cf2ed-611d-49cc-bf8b-2c3923d108ca", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Update Your Contact Information", "url": "https://meghanag.supporthive.com/kb/article/7-how-to-update-your-contact-information/", "article_id": 7, "section_id": 2, "last_updated_at": "2025-04-18T03:42:44Z", "has_attachments": false, "attachment_count": 0}, "hash": "b0b36b00f4924589f7716b0e660006c6f5a576940cfc53283c232b07727b2660", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "You can update your contact information directly from your account settings:\n\n  1. Log in to your account.\n\n  2. Click on your profile picture in the top-right corner.\n\n  3. Select \"Account Settings.\"\n\n  4. Under \"Personal Information,\" click \"Edit.\"\n\n  5. Update your name, email, and phone number as needed.\n\n  6. Save your changes.\n\n**Troubleshooting:**\n\n  * Ensure the email address is valid and correctly formatted.\n\n  * If you experience issues saving changes, clear your browser cache or try a different browser.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 519, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "af7f4bcc-c290-4cff-9197-e206547dad70": {"__data__": {"id_": "af7f4bcc-c290-4cff-9197-e206547dad70", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Use Two-Factor Authentication (2FA)", "url": "https://meghanag.supporthive.com/kb/article/10-how-to-use-two-factor-authentication-2fa/", "article_id": 10, "section_id": 2, "last_updated_at": "2025-04-18T03:45:13Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "f5fcf4b1-2fa9-4d66-ad4c-bda2e5fdad17", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Use Two-Factor Authentication (2FA)", "url": "https://meghanag.supporthive.com/kb/article/10-how-to-use-two-factor-authentication-2fa/", "article_id": 10, "section_id": 2, "last_updated_at": "2025-04-18T03:45:13Z", "has_attachments": false, "attachment_count": 0}, "hash": "d44a3ea2fc59d7eb8f9fa036a2871a8178290980950207be3df0ad42ab132548", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Two-factor authentication (2FA) adds an extra layer of security to your\naccount. Here’s how to enable it:\n\n  1. Log in to your account.\n\n  2. Go to \"Account Settings.\"\n\n  3. Under \"Security Settings,\" click on \"Enable Two-Factor Authentication.\"\n\n  4. Choose your preferred method (e.g., mobile app, email).\n\n  5. Follow the instructions to link your account to your 2FA method.\n\n  6. Enter the verification code sent to your device to complete the setup.\n\n**Troubleshooting:**\n\n  * If you lose access to your 2FA method, contact support for assistance.\n\n  * Ensure that the device used for 2FA is set up correctly to avoid verification issues.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 644, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "efe04166-7965-4d69-a3c0-91ceb217e9a1": {"__data__": {"id_": "efe04166-7965-4d69-a3c0-91ceb217e9a1", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Tables as image and images", "url": "https://meghanag.supporthive.com/kb/article/16-kb-1-tables-as-image-and-images/", "article_id": 16, "section_id": 2, "last_updated_at": "2025-04-29T06:01:25Z", "has_attachments": true, "attachment_count": 4}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "8e45c498-c26d-48b1-a025-0a3db975e255", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Tables as image and images", "url": "https://meghanag.supporthive.com/kb/article/16-kb-1-tables-as-image-and-images/", "article_id": 16, "section_id": 2, "last_updated_at": "2025-04-29T06:01:25Z", "has_attachments": true, "attachment_count": 4}, "hash": "210ad9ca2605aa5359af49ece2a2e89183f9592a31acbc1859cc1afd5e5f586a", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Duralite-100E Expansion Kit\n\n![print\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg)\n\nMonsoon Gohain\n\nSep 01, 2022\n\n![views\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg) 927\n\n![](https://hf-files-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/37f48d3f-8d70-400d-8ed6-181972e8a8e4/Screen_Shot_2022-07-13_at_12.35.04_PM.png)![](https://hf-\nfiles-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/681a6847-eac3-485e-8ef7-3ab20542f58e/Duralite-100E_Expansion_Kit.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 592, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "b8f3e5c7-162e-4ccb-b75f-fdcb97e95d78": {"__data__": {"id_": "b8f3e5c7-162e-4ccb-b75f-fdcb97e95d78", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "ce8231ec-e06c-48ca-802c-f9275f545851", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "hash": "d1fa6f7372ce8fb79760860e0cf454b837d8276b706a6b2863f4fcef925eb41b", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_1.png\nFile Type: image/png\nURL: https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg\n\nProcessing failed: Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 349, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "abd08c21-674b-473a-a1ae-ce1a3e5f707a": {"__data__": {"id_": "abd08c21-674b-473a-a1ae-ce1a3e5f707a", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "cdb4429a-cbf8-4c62-969f-f3ea968aa402", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "hash": "84765eaa2bf28ee0ed61f27d804943888b25195481617e0254c86ecfe75183c0", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_2.png\nFile Type: image/png\nURL: https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg\n\nProcessing failed: Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 355, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "36178db6-58b8-4111-859e-12ed98afde07": {"__data__": {"id_": "36178db6-58b8-4111-859e-12ed98afde07", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/37f48d3f-8d70-400d-8ed6-181972e8a8e4/Screen_Shot_2022-07-13_at_12.35.04_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 53841, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "38a14b79-5b50-489a-b4f6-4ce07094ec17", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/37f48d3f-8d70-400d-8ed6-181972e8a8e4/Screen_Shot_2022-07-13_at_12.35.04_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 53841, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "d3b545a0637468c4379facce80e3df6b2f67dba3b7fc970f037537e54453bfac", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "# Description\n\n!Table[The table contains a list of 7 items describing components or features of a device, likely a portable electronic device with power-related functionalities. The table has two columns: a numbered list (1-7) and descriptions for each item. The items are:\n1. Handle\n2. Foldable legs\n3. USB ports & LED indicator\n4. DC output port\n5. 2x Extension Cable\n6. Barrel Connector (for connecting to portable power stations)\n7. MC4 Connector (for connecting to portable power stations)]", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 495, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "b473f8bf-c7fe-40e9-bdf2-b49ebe82cd2c": {"__data__": {"id_": "b473f8bf-c7fe-40e9-bdf2-b49ebe82cd2c", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-\nfiles-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/681a6847-eac3-485e-8ef7-3ab20542f58e/Duralite-100E_Expansion_Kit.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 1615879, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "541a3843-9b62-4253-a6fc-10943ee6a02a", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-\nfiles-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/681a6847-eac3-485e-8ef7-3ab20542f58e/Duralite-100E_Expansion_Kit.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 1615879, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "37c643e72e3100317c2bacb258f156458923a4bd28cfe4a598b976d1f7a298cc", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "!Diagram[The diagram shows a technical illustration of the DURALITE GP-DURALITE-100 portable solar panel or power bank. The main image depicts the device from a side angle, showing its triangular shape when set up. Key components are labeled with numbers 1-4:\n\n1. Handle: Located at the top left corner of the device.\n2. Foldable Legs: Two legs that extend from the bottom to prop up the device.\n3. USB Ports & LED Indicator: Shown in a small inset image at the top right, displaying the DURALITE logo and ports.\n4. DC Output Port: Located on the right side of the device.\n\nBelow the main illustration are three smaller diagrams showing accessories:\n\n5. 2x Extension Cable: A coiled cable with connectors on both ends.\n6. Barrel Connector: A cable with different connectors on each end, labeled as \"for connecting to portable power stations\".\n7. MC4 Connector: Another cable similar to the Barrel Connector, also for connecting to portable power stations.\n\nEach component is clearly labeled and numbered, providing a comprehensive overview of the DURALITE device and its accessories.]\n\n1. Handle\n2. Foldable Legs\n3. USB Ports & LED Indicator\n4. DC Output Port\n5. 2x Extension Cable\n6. Barrel Connector (for connecting to portable power stations)\n7. MC4 Connector (for connecting to portable power stations)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1306, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4e2d10b3-2605-4e8f-8988-4c197b3f5d8c": {"__data__": {"id_": "4e2d10b3-2605-4e8f-8988-4c197b3f5d8c", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "53000ebb-5054-456c-9f69-a3ca76c27da5", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "hash": "3d8f0fd8446ed2b6f59eff6c9b4f3847538ba4acd8b12c0c51c06a25d48d2b6f", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "52b40780-efb9-4f62-a4f0-9ec2dca3fc4b", "node_type": "1", "metadata": {}, "hash": "f134e096d4bdf5b4db087bb4babc987598a1a8c457e30d2146add173551b2a14", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Automate Okta Identity Lifecycle Actions for requests in Zendesk\n\n![print\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg)\n\nNov 05, 2021\n\n![views\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg) 2209\n\nBy using HappyFox Workflows, IT admins can automate everyday identity\nlifecycle actions in Okta for user requests from Zendesk.\n\n**List of Okta actions that can be invoked/automated inside Zendesk:**\n\n  * Password resets (including expiring temporary password)\n  * Account unlocks\n  * Resetting any MFA enrollments\n  * Clearing existing user sessions\n  * Account suspensions\n  * Account deactivations.\n\n_[Click here](https://www.happyfox.com/workflows-for-zendesk-support/#book-\ndemo) _to book a 1-on-1 demo with our product experts\n\n**Pre-requisite**\n\nAn active HappyFox Workflows account with Zendesk support and Okta\nsuccessfully integrated.\n\n_Quicklinks:_\n\n  * [Zendesk configuration with HappyFox Workflows](https://support.happyfox.com/kb/article/1002-how-to-configure-happyfox-workflows-for-zendesk-support/)\n  * [Okta configuration with HappyFox Workflows](https://support.happyfox.com/kb/article/1136-configure-okta-integration-with-happyfox-workflows/)\n\n**Automated Workflow Example:**\n\n  1. A User raises an Okta unlock request in Zendesk\n  2. HappyFox Workflows automatically detects an unlock request and validates it.\n  3. HappyFox Workflows initiates the user unlock action in Okta automatically.\n  4. HappyFox Workflows sends an update to the user on the next steps.\n\n!", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1584, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "52b40780-efb9-4f62-a4f0-9ec2dca3fc4b": {"__data__": {"id_": "52b40780-efb9-4f62-a4f0-9ec2dca3fc4b", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "53000ebb-5054-456c-9f69-a3ca76c27da5", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "hash": "3d8f0fd8446ed2b6f59eff6c9b4f3847538ba4acd8b12c0c51c06a25d48d2b6f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "4e2d10b3-2605-4e8f-8988-4c197b3f5d8c", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "hash": "ac85faaeb48acd761412c4d5b7c89c0dac2316a0863ca23ca7f1d8c3cc98774d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "3. HappyFox Workflows initiates the user unlock action in Okta automatically.\n  4. HappyFox Workflows sends an update to the user on the next steps.\n\n![](https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png)\n\n**Manually Triggering Okta Actions:**\n\n<PERSON><PERSON> can also set up HappyFox Workflows configuration to manually trigger\nOkta actions from the context of a Zendesk Ticket.\n\n![](https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png)\n\nFeedback\n\n4 out of 4 found this helpful", "mimetype": "text/plain", "start_char_idx": 1433, "end_char_idx": 2113, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "16fb866f-3c65-427a-85c8-eaf63869ef4e": {"__data__": {"id_": "16fb866f-3c65-427a-85c8-eaf63869ef4e", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "944024ad-f109-4fc7-bc26-10dd8f2eb12d", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "hash": "fec774e6ae9f4cad6c7b360c60b8ee472c47b05b05ef6d18d252c6f0d7d5fca4", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_1.png\nFile Type: image/png\nURL: https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg\n\nProcessing failed: Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 349, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8c8c2ba8-7ba9-4bb5-a89f-002131dadbc6": {"__data__": {"id_": "8c8c2ba8-7ba9-4bb5-a89f-002131dadbc6", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "758744ea-14b1-48c4-bb1c-235bad923457", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "hash": "f12aafbb8e54fcc0beddaed8f2b9df8697209e867fc57335f0f221d18cab303b", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_2.png\nFile Type: image/png\nURL: https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg\n\nProcessing failed: Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 355, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "526d7903-5936-4393-b9fd-095d66a21b5d": {"__data__": {"id_": "526d7903-5936-4393-b9fd-095d66a21b5d", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 176549, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "b4cfc1fc-f10b-4f25-9fb2-e702e73a58fa", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 176549, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "3aed720d6e0e2ffdd14309527b62d62a78e7d965fbcba030ed04a28208a2a09f", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "# Help! I got locked out of my account\n\n7 minutes ago • <PERSON> <EMAIL> via Adikeshav c (change) • from Zendesk Support\n\n## Interactions\n\n### Public reply Internal note\n\nTo <PERSON> CC\n\n### Conversations\n\nAll (4) Public (3) Internal (1)\n\n#### Workflow Bot (assign)\n3 minutes ago\n\nHey <PERSON>,\n\nThis is an automated response from the priority support team. We have successfully initiated the unlock procedure for your Okta account. Kindly check your email for the next steps!\n\n#### Adikeshav c (assign)\n4 minutes ago\n\nDetected that this is an account unlock account <NAME_EMAIL>. Proceeding to unlock the agent.\n\n#### <PERSON> (assign)\n7 minutes ago\n\nHey! I got Locked out of my Okta account. Could you please unlock my account?\n\n### Apply macro\n\nClose tab Submit as Open", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 804, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "66fa2897-6b63-4510-bd31-4eb16eb9ec4e": {"__data__": {"id_": "66fa2897-6b63-4510-bd31-4eb16eb9ec4e", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 213826, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "c9bd71cc-1bf5-482a-9dc2-9178bef75fbe", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 213826, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "18ee9a3097f303d498e049cee8d2ebdf47aa80835580242d7899529203712d90", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "5c6c92c7-2abb-4f08-9e91-4800d90d1a08", "node_type": "1", "metadata": {}, "hash": "e4a2c92d6f5d8dfb1deb7506f6a3a25caa763b9898840287f0acd583f974ed43", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "# Help! I got locked out of my account\n\n## Ticket Details\n\n**Ticket #:** 274\n**Status:** OPEN\n**Requester:** <PERSON>\n**Created:** 23 minutes ago\n**Email:** <EMAIL> via Adikeshav c (change)\n**Source:** Zendesk Support\n\n## User Information\n\n**Name:** <PERSON>\n**Email:** <EMAIL>\n**Phone:** +************\n**Time Zone:** Eastern Time (US & Canada)\n**Language:** English (United States)\n**Product:** product_a\n\n## Conversation\n\n### Workflow Bot (19 minutes ago)\n\nHey <PERSON>,\n\nThis is an automated response from the priority support team. We have successfully initiated the unlock procedure for your Okta account. Kindly check your email for the next steps.\n\n## Sidebar Information\n\n### Interactions\n\n1. Help! I got locked out of my account\n   24 minutes ago - 6 comments\n\n2. My order is not delivered. I had order...\n   Sep 02 05:08 - 2 comments\n\n3. as<PERSON><PERSON>sdoiajsodijasd\n   Sep 02 04:46 - 2 comments\n\n4. Need help with refund. headphones ...\n   Sep 02 04:40 - 3 comments\n\n5.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 996, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "5c6c92c7-2abb-4f08-9e91-4800d90d1a08": {"__data__": {"id_": "5c6c92c7-2abb-4f08-9e91-4800d90d1a08", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 213826, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "c9bd71cc-1bf5-482a-9dc2-9178bef75fbe", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 213826, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "18ee9a3097f303d498e049cee8d2ebdf47aa80835580242d7899529203712d90", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "66fa2897-6b63-4510-bd31-4eb16eb9ec4e", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 213826, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "7e3698c3890c86b367e7a230f82efa3ee78d46c186b171dca9ec825a146eb13b", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Need help with refund. headphones ...\n   Sep 02 04:40 - 3 comments\n\n5. <PERSON>\n   Sep 02 04:25 - 2 comments\n\n### Apps\n\n#### Assist Bot\n\n#### Whatsapp\n\n### HappyFox Workflows\n\n- Activate a User\n- Add a User to a Group\n- Assign an App to a User\n- Clear all open sessions for a User\n- Deactivate a User\n- Expire the password for a User\n- Get User properties\n- Reactivate a User\n- Remove a User from a Group\n- Reset password for a User\n- Reset the Login Factors for a User\n\n## Actions\n\n- Apply macro\n- Submit as Open", "mimetype": "text/plain", "start_char_idx": 926, "end_char_idx": 1443, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "300ab3c8-3353-4684-9027-a4d8c66c497a": {"__data__": {"id_": "300ab3c8-3353-4684-9027-a4d8c66c497a", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "PDF", "url": "https://meghanag.supporthive.com/kb/article/22-pdf/", "article_id": 22, "section_id": 2, "last_updated_at": "2025-06-16T09:01:30Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "c4832127-a552-47c6-9d93-ec5ce661498a", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "PDF", "url": "https://meghanag.supporthive.com/kb/article/22-pdf/", "article_id": 22, "section_id": 2, "last_updated_at": "2025-06-16T09:01:30Z", "has_attachments": true, "attachment_count": 1}, "hash": "c221f98bf900ef40f44012a543dc67506611ee2b91ecc14f516565cd873755d4", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "attached documents test", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 23, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "38cefedd-8c21-48a1-8d0e-777453d1e93e": {"__data__": {"id_": "38cefedd-8c21-48a1-8d0e-777453d1e93e", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "section_id": 2, "download_failed": true, "is_embedded_image": false}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "94ada80e-7e09-4106-b635-6e721cb9eb97", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "section_id": 2, "download_failed": true, "is_embedded_image": false}, "hash": "a801616038c20676ecb574b3c77a62bd6ce1344d400591f5a8fb74224b1f0ecc", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Attachment: Manual_GP-PWM-10-FM.pdf\nFile Type: application/pdf\nURL: https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf\n\nDownload failed but attachment detected.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 247, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "bc084c92-2155-44f1-bd44-a8d20d8cbfd2": {"__data__": {"id_": "bc084c92-2155-44f1-bd44-a8d20d8cbfd2", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "PDF-TEST-2", "url": "https://meghanag.supporthive.com/kb/article/23-pdf-test-2/", "article_id": 23, "section_id": 2, "last_updated_at": "2025-06-19T11:05:28Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "b18dd1f8-f170-4cb7-b72d-53c4da0bde5d", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "PDF-TEST-2", "url": "https://meghanag.supporthive.com/kb/article/23-pdf-test-2/", "article_id": 23, "section_id": 2, "last_updated_at": "2025-06-19T11:05:28Z", "has_attachments": true, "attachment_count": 1}, "hash": "e58004a99ad879347f48597e71c8d5d9f61d03ff57f368a4b28cbba77b0dbbd7", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "PDF TEST 2", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 10, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "633cf3a1-630f-4a50-8209-06135478c096": {"__data__": {"id_": "633cf3a1-630f-4a50-8209-06135478c096", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 23, "parent_article_title": "PDF-TEST-2", "attachment_id": 3, "attachment_name": "GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-19/7d02d733-d84f-4e17-a7a9-1d33c8a78798/GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 528258, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "aa6be186-3d22-4fb4-8f91-39cf5b172f74", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 23, "parent_article_title": "PDF-TEST-2", "attachment_id": 3, "attachment_name": "GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-19/7d02d733-d84f-4e17-a7a9-1d33c8a78798/GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 528258, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "ac2111a7441032070f439e660c917d4ab77ac5eddc34b07d3e2e69dfb6ac80c7", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "b3e8e03d-97d9-43ae-9df8-62de3a0464c1", "node_type": "1", "metadata": {}, "hash": "f53bd6cb24d52f81fe34b2a1927324d96d02d19de8d0fb9ad23d1ca9dfb6c473", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "# GP-RVC-MPPT-30 - CHARGING STAGES\n\nKandis Whitmire    Jan 23, 2024\n\n## CHARGING STAGES\n\nMaximum power point tracking is used to charge the batteries with the highest current possible, but this is only part of the equation. A battery cannot be charged at maximum power all the time for safety reasons, so multiple stages are used. These stages include: bulk, absorption, float and, for some types of batteries, equalization as indicated below.\n\n!Chart[This diagram illustrates the charging stages of a battery using two stacked charts showing Battery Voltage and Battery Current over time. The stages depicted are:\n\n1. Bulk (Stage 1): Battery voltage rises rapidly while current remains high.\n2. Absorption (Stage 2): Voltage reaches and maintains the Absorption Voltage level, while current gradually decreases.\n3. Equalize (Stage 4): For some battery types, voltage increases to Equalize Voltage level briefly.\n4. Float (Stage 3): Voltage drops to Float Voltage level, and current remains low.\n5. Recharge: When voltage drops to Recharge Voltage, the cycle restarts with a brief current spike.\n\nThe diagram effectively visualizes the relationship between voltage and current throughout the charging process, demonstrating how the controller adjusts these parameters to optimize battery charging and maintenance.]\n\n### STAGE 1: BULK\n\nIn quick charge stage, the battery voltage has not yet reached the set value of full charge voltage (i.e.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1440, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "b3e8e03d-97d9-43ae-9df8-62de3a0464c1": {"__data__": {"id_": "b3e8e03d-97d9-43ae-9df8-62de3a0464c1", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 23, "parent_article_title": "PDF-TEST-2", "attachment_id": 3, "attachment_name": "GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-19/7d02d733-d84f-4e17-a7a9-1d33c8a78798/GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 528258, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "aa6be186-3d22-4fb4-8f91-39cf5b172f74", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 23, "parent_article_title": "PDF-TEST-2", "attachment_id": 3, "attachment_name": "GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-19/7d02d733-d84f-4e17-a7a9-1d33c8a78798/GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 528258, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "ac2111a7441032070f439e660c917d4ab77ac5eddc34b07d3e2e69dfb6ac80c7", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "633cf3a1-630f-4a50-8209-06135478c096", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 23, "parent_article_title": "PDF-TEST-2", "attachment_id": 3, "attachment_name": "GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-19/7d02d733-d84f-4e17-a7a9-1d33c8a78798/GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 528258, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "4210dc1f05e133141f92b5691ed9159e48cc9269edbf59579b572d441c57122b", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "a0cb552d-47b8-42ff-8a15-e9ef08c13b30", "node_type": "1", "metadata": {}, "hash": "91077b4c7e6896d22a961146bf3ccc3274a299af5a0a92fb508b5b5e9caf72a7", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "### STAGE 1: BULK\n\nIn quick charge stage, the battery voltage has not yet reached the set value of full charge voltage (i.e. equalizing/boost charge voltage) and the controller will perform MPPT charging, which will provide maximum solar energy to charge the battery. When the battery voltage reaches the pre-set value, Stage 2 charge will begin.\n\n# STAGE 2: ABSORPTION\n\nWhen the battery voltage reaches the absorption voltage, the controller will perform constant voltage charging. This is no longer MPPT charging, and the charging current will gradually decrease with time.\n\n# STAGE 3: FLOAT\n\nFloat charge is conducted following the absorption charge stage. The controller will reduce the charge current to a small amount in order to reduce sulfates on the battery plates or to allow a lithium battery to balance it's cells. If the load exceeds this small current the battery voltage will start to decrease until it reaches the recharge voltage. When the battery voltage falls below the recharge voltage, the controller will switch back to bulk charging.\n\n# STAGE 4: EQUALIZE\n\n*Warning: Risk of explosion!*\n\nEqualizing vented lead-acid battery may generate explosive gases. So, the battery compartment must be well ventilated.\n\n*Caution: Damage of device!*\n\nEqualization can increase the battery voltage to levels that may damage sensitive DC loads. It is necessary to verify that the allowable input voltage of all system loads is greater than the equalizing charge set value.\n\n*Caution: Damage of device!", "mimetype": "text/plain", "start_char_idx": 1316, "end_char_idx": 2824, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "a0cb552d-47b8-42ff-8a15-e9ef08c13b30": {"__data__": {"id_": "a0cb552d-47b8-42ff-8a15-e9ef08c13b30", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 23, "parent_article_title": "PDF-TEST-2", "attachment_id": 3, "attachment_name": "GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-19/7d02d733-d84f-4e17-a7a9-1d33c8a78798/GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 528258, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "aa6be186-3d22-4fb4-8f91-39cf5b172f74", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 23, "parent_article_title": "PDF-TEST-2", "attachment_id": 3, "attachment_name": "GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-19/7d02d733-d84f-4e17-a7a9-1d33c8a78798/GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 528258, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "ac2111a7441032070f439e660c917d4ab77ac5eddc34b07d3e2e69dfb6ac80c7", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "b3e8e03d-97d9-43ae-9df8-62de3a0464c1", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 23, "parent_article_title": "PDF-TEST-2", "attachment_id": 3, "attachment_name": "GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-19/7d02d733-d84f-4e17-a7a9-1d33c8a78798/GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 528258, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "2ab1850737d3609c5f41fa0cf2759c2a17665313f854986b3215651971792729", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "8a7970d0-983e-4aca-9463-6f6fc5995255", "node_type": "1", "metadata": {}, "hash": "a0b506d12175adc0f265037e2f1b251f3fc492822b1b0f4b0f36ed01a80c92dd", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "*\n\nEqualization can increase the battery voltage to levels that may damage sensitive DC loads. It is necessary to verify that the allowable input voltage of all system loads is greater than the equalizing charge set value.\n\n*Caution: Damage of device!*\n\nOver charge and excessive gas evolution may damage the battery plates and cause active substances on the battery plate to come off. Equalizing charge may cause damage if voltage is too high or time is too long. Please carefully check the specific requirements of battery used in the system.\n\nCertain battery types benefit from regular equalizing charge, which can stir electrolytes, balance battery voltage, and complete the chemical reaction. The equalize charge increases the battery voltage above a standard voltage, causing vaporization of battery electrolyte. By default, this happens every 30 days for flooded batteries.\n\n# RECHARGE\n\nAfter the battery is completely charged the charging cycle completes and the battery is allowed to slowly discharge until it reaches the charge return voltage at which point a new charge cycle is initiated.\n\n# CHARGE PARAMETERS FOR VARIOUS BATTERY TYPES\n\n#  - Go Power!\n\n!Table[This comprehensive table presents charging parameters for various battery types: GEL, AGM, Flooded, LiFePO4, and Custom. It covers 14 key parameters including voltage levels for different charging stages, time durations, and safety thresholds. Notable observations include:\n\n1. Voltage parameters are generally similar across battery types, with minor variations.\n2. LiFePO4 batteries have distinct values and fewer applicable parameters.\n3.", "mimetype": "text/plain", "start_char_idx": 2573, "end_char_idx": 4186, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8a7970d0-983e-4aca-9463-6f6fc5995255": {"__data__": {"id_": "8a7970d0-983e-4aca-9463-6f6fc5995255", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 23, "parent_article_title": "PDF-TEST-2", "attachment_id": 3, "attachment_name": "GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-19/7d02d733-d84f-4e17-a7a9-1d33c8a78798/GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 528258, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "aa6be186-3d22-4fb4-8f91-39cf5b172f74", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 23, "parent_article_title": "PDF-TEST-2", "attachment_id": 3, "attachment_name": "GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-19/7d02d733-d84f-4e17-a7a9-1d33c8a78798/GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 528258, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "ac2111a7441032070f439e660c917d4ab77ac5eddc34b07d3e2e69dfb6ac80c7", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "a0cb552d-47b8-42ff-8a15-e9ef08c13b30", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 23, "parent_article_title": "PDF-TEST-2", "attachment_id": 3, "attachment_name": "GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-19/7d02d733-d84f-4e17-a7a9-1d33c8a78798/GP-RVC-MPPT-30_-_CHARGING_STAGES_-_Go_Power_-_powered_by_HappyFox.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 528258, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "86a0356f46a26bf84ab41622eab325a45c14a8bd96bea91f4841c04b2dcbcb74", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Notable observations include:\n\n1. Voltage parameters are generally similar across battery types, with minor variations.\n2. LiFePO4 batteries have distinct values and fewer applicable parameters.\n3. The Custom column allows for user-defined settings in most cases.\n4. Time-based parameters like Absorption Duration and Equalize Interval vary significantly between battery types.\n5. The Temperature Compensation Factor is consistent at -24 mV/°C for all specified battery types.\n\nThis table is crucial for understanding the charging behavior and requirements of different battery types, allowing for optimal configuration of the GP-RVC-MPPT-30 charging system.]\n\nhttps://gopower.happyfox.com/kb/article/388-gp-rvc-mppt-30-charging-stages/\n\n - Go Power! - powered by HappyFox\t19/06/25, 4:29 PM\n\nPage 3 of 3", "mimetype": "text/plain", "start_char_idx": 3989, "end_char_idx": 4792, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "ff5126a3-ba2d-4083-9d22-407e0378fd1b": {"__data__": {"id_": "ff5126a3-ba2d-4083-9d22-407e0378fd1b", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample docx", "url": "https://meghanag.supporthive.com/kb/article/17-sample-docx/", "article_id": 17, "section_id": 2, "last_updated_at": "2025-05-12T06:18:44Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "7c7e830e-f1b2-41ba-9732-61e105c54aa9", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample docx", "url": "https://meghanag.supporthive.com/kb/article/17-sample-docx/", "article_id": 17, "section_id": 2, "last_updated_at": "2025-05-12T06:18:44Z", "has_attachments": true, "attachment_count": 1}, "hash": "94daa94105e4847e0d84debabad77a2032e2b63fe25745cd0ec4a35de5556ae9", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "test docx", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 9, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "a83e28f0-a5e1-4e55-af39-9c64b9f21a9d": {"__data__": {"id_": "a83e28f0-a5e1-4e55-af39-9c64b9f21a9d", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 17, "parent_article_title": "sample docx", "attachment_id": 1, "attachment_name": "sample_kb_article.docx", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-12/69860b5a-91bb-4339-bf21-5b9c647d9053/sample_kb_article.docx", "mime_type": "application/octet-stream", "file_extension": ".docx", "file_size": 37001, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "4cb8e1b6-7da9-447e-adeb-93e23734a31b", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 17, "parent_article_title": "sample docx", "attachment_id": 1, "attachment_name": "sample_kb_article.docx", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-12/69860b5a-91bb-4339-bf21-5b9c647d9053/sample_kb_article.docx", "mime_type": "application/octet-stream", "file_extension": ".docx", "file_size": 37001, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "db7d581f477a2bc6df40c80c33308ce401eebefc4e70ceee2abb42579aa5261e", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Attachment: sample_kb_article.docx\nFile Type: application/octet-stream\nSize: 37001 bytes\n\nContent parsing not supported for this file type.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 139, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "9ece5fc0-844c-4edf-8b76-2c2a91a83c83": {"__data__": {"id_": "9ece5fc0-844c-4edf-8b76-2c2a91a83c83", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample image", "url": "https://meghanag.supporthive.com/kb/article/19-sample-image/", "article_id": 19, "section_id": 2, "last_updated_at": "2025-05-14T11:23:38Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "062fc48a-860f-44c8-a773-cf861eef3e19", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample image", "url": "https://meghanag.supporthive.com/kb/article/19-sample-image/", "article_id": 19, "section_id": 2, "last_updated_at": "2025-05-14T11:23:38Z", "has_attachments": true, "attachment_count": 1}, "hash": "f75323da85e9730f572c96f760cd00697eaa56b143fbbf748f3b7d2d0f3c474b", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "![](https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-14/89f0213e-8ddf-415e-8bce-1f32bdbe8c43/Screenshot_2025-05-14_at_4.52.20PM.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 158, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "fbc9def7-2ebd-4ace-844a-0fc3bc84b115": {"__data__": {"id_": "fbc9def7-2ebd-4ace-844a-0fc3bc84b115", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 19, "parent_article_title": "sample image", "attachment_id": "img_19_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-14/89f0213e-8ddf-415e-8bce-1f32bdbe8c43/Screenshot_2025-05-14_at_4.52.20PM.png", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "download_failed": true, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "15cf7417-0093-4728-b93d-b852f31590ec", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 19, "parent_article_title": "sample image", "attachment_id": "img_19_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-14/89f0213e-8ddf-415e-8bce-1f32bdbe8c43/Screenshot_2025-05-14_at_4.52.20PM.png", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "download_failed": true, "is_embedded_image": true}, "hash": "240f1af6e250c9e0bdb7ce4f5f25d1f9d2c285f51bb74b95a6075e8d85e2ca1e", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_1.png\nFile Type: image/png\nURL: https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-14/89f0213e-8ddf-415e-8bce-1f32bdbe8c43/Screenshot_2025-05-14_at_4.52.20PM.png\n\nDownload failed but image detected.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 253, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "e13e19ce-0101-4001-a422-d9acc38d5139": {"__data__": {"id_": "e13e19ce-0101-4001-a422-d9acc38d5139", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "segment", "url": "https://meghanag.supporthive.com/kb/article/20-segment/", "article_id": 20, "section_id": 2, "last_updated_at": "2025-05-15T15:07:55Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "39d02ba2-e676-4d64-a2b1-02e4e02b62f3", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "segment", "url": "https://meghanag.supporthive.com/kb/article/20-segment/", "article_id": 20, "section_id": 2, "last_updated_at": "2025-05-15T15:07:55Z", "has_attachments": true, "attachment_count": 1}, "hash": "ed3f62cbe935a18eef58b651a4711eb818d9dcae7596c9f67608233353d93e3b", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "![](https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-15/9970e608-34ec-4d3e-8230-a0be6f858b31/Screenshot_2025-05-15_at_8.37.11PM.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 158, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8b4b0bab-4c2b-4ebf-ac6f-dfeea7976e91": {"__data__": {"id_": "8b4b0bab-4c2b-4ebf-ac6f-dfeea7976e91", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 20, "parent_article_title": "segment", "attachment_id": "img_20_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-15/9970e608-34ec-4d3e-8230-a0be6f858b31/Screenshot_2025-05-15_at_8.37.11PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 94007, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "b9b52d7c-c83d-42a2-bc69-be9711dbe71a", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 20, "parent_article_title": "segment", "attachment_id": "img_20_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-15/9970e608-34ec-4d3e-8230-a0be6f858b31/Screenshot_2025-05-15_at_8.37.11PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 94007, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "4fbdd043bcdf1692d04d1e24c64b3b4fad1e25532021280d87e6778f44a21c12", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "# Getting Started with Segment\n\n1. Create a Segment account at segment.com\n2. Log in and go to the Dashboard\n3. Click on 'Add Source' to connect your app\n4. Choose your platform (iOS, Android, Web, etc.)\n5. Copy the generated write key\n6. Install the Segment SDK and initialize with the key\n7. Verify events in the Segment debugger", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 331, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "d1a59a00-c730-4f26-8b96-01de7b504f65": {"__data__": {"id_": "d1a59a00-c730-4f26-8b96-01de7b504f65", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article", "url": "https://meghanag.supporthive.com/kb/article/1-test-article/", "article_id": 1, "section_id": 2, "last_updated_at": "2025-04-08T16:05:41Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "fce54a93-8eac-4301-a3b3-ee12d81e578b", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article", "url": "https://meghanag.supporthive.com/kb/article/1-test-article/", "article_id": 1, "section_id": 2, "last_updated_at": "2025-04-08T16:05:41Z", "has_attachments": false, "attachment_count": 0}, "hash": "ffa5c5d8d8cec1e1f4458b09a64779bd868870ac763e29aaadc2796130dfbd8d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "This is a sample knowledge base article to test the HappyFox API reader.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 72, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "f972a30d-b37d-47fe-ae6a-af7cbbc1aea3": {"__data__": {"id_": "f972a30d-b37d-47fe-ae6a-af7cbbc1aea3", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 2", "url": "https://meghanag.supporthive.com/kb/article/2-test-article-2/", "article_id": 2, "section_id": 2, "last_updated_at": "2025-04-09T10:10:39Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "0f562da1-76b3-4fad-bfa5-5bdd9ae21dd7", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 2", "url": "https://meghanag.supporthive.com/kb/article/2-test-article-2/", "article_id": 2, "section_id": 2, "last_updated_at": "2025-04-09T10:10:39Z", "has_attachments": false, "attachment_count": 0}, "hash": "7494f46e4dc66339eca63b3f344828bb960c709fab60fee43a26b072773fb5ea", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "`This is another test article to verify HappyFox reader updates.`", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 65, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "3deb0654-7cff-46ad-9975-fbc1e013a6fb": {"__data__": {"id_": "3deb0654-7cff-46ad-9975-fbc1e013a6fb", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 3", "url": "https://meghanag.supporthive.com/kb/article/3-test-article-3/", "article_id": 3, "section_id": 3, "last_updated_at": "2025-04-16T06:34:30Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "1ccec29d-339f-4f72-a6dd-54dd713e8544", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 3", "url": "https://meghanag.supporthive.com/kb/article/3-test-article-3/", "article_id": 3, "section_id": 3, "last_updated_at": "2025-04-16T06:34:30Z", "has_attachments": false, "attachment_count": 0}, "hash": "7de16eccb9a8b2cbc6f52743973366a2c2a4750f7755c5bafe60a7c0a0ef9174", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Having trouble logging into your account? Here are some common causes and\nquick fixes:\n\n1\\. Incorrect Username or Password****  \nDouble-check for typos. Passwords are case-sensitive.\n\n2\\. B<PERSON><PERSON> Cache and Cookies****  \nClear your browser's cache and cookies and try logging in again.\n\n3\\. Two-Factor Authentication Issues****  \nIf you're not receiving the OTP, check your spam folder or ensure your\nregistered phone number is correct.\n\n4\\. Account Lockouts  \nAfter multiple failed attempts, your account might be temporarily locked. Wait\n15 minutes before trying again.\n\nIf you're still having trouble, please contact support or reset your password\nusing the \"Forgot Password\" link.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 683, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "f752c0ba-57e5-4c23-ab09-21f4650cc1b1": {"__data__": {"id_": "f752c0ba-57e5-4c23-ab09-21f4650cc1b1", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 4", "url": "https://meghanag.supporthive.com/kb/article/4-test-article-4/", "article_id": 4, "section_id": 2, "last_updated_at": "2025-04-16T06:36:18Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "dd040cbd-7618-47e1-a654-3e6f76825547", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 4", "url": "https://meghanag.supporthive.com/kb/article/4-test-article-4/", "article_id": 4, "section_id": 2, "last_updated_at": "2025-04-16T06:36:18Z", "has_attachments": false, "attachment_count": 0}, "hash": "f346b26dd871456feb7dba1fa4d576fdb2bbcff9fed3ac65eb84b798f05cb4ff", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Title:\n\nContent:\n\nIf you've forgotten the answers to your security questions or wish to update\nthem, follow the steps below:\n\n1\\. Log into your account using your username and password.  \n2\\. Navigate to Settings > Security Settings.  \n3\\. Click on \"Reset Security Questions\".  \n4\\. Enter your current password for verification.  \n5\\. Choose new questions from the dropdown and provide new answers.  \n6\\. Click Save to update.\n\nNote: If you're unable to access your account, please click on “Need Help?” on\nthe login screen and select \"Contact Support\" to verify your identity and\nrequest a manual reset.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 604, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4e5966f6-484d-40a6-a653-e0aaad6aa77a": {"__data__": {"id_": "4e5966f6-484d-40a6-a653-e0aaad6aa77a", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 5", "url": "https://meghanag.supporthive.com/kb/article/5-test-article-5/", "article_id": 5, "section_id": 2, "last_updated_at": "2025-04-16T07:44:33Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "0b7b19bf-a866-4cd5-be64-85f46d7feb20", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 5", "url": "https://meghanag.supporthive.com/kb/article/5-test-article-5/", "article_id": 5, "section_id": 2, "last_updated_at": "2025-04-16T07:44:33Z", "has_attachments": false, "attachment_count": 0}, "hash": "98a366847844471630118aeeff9aa1dbc676574bd387c0c200fcd0ed8f82bff2", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Updating your profile ensures your contact information and preferences stay\ncurrent.\n\nSteps to update your profile:\n\n1\\. Log in to your Helpdesk account.  \n2\\. Click on your profile icon in the top-right corner.  \n3\\. Select “My Profile” from the dropdown menu.  \n4\\. Update the following fields as needed:  \n\\- Full Name  \n\\- Email Address  \n\\- Phone Number  \n\\- Preferred Language  \n5\\. Click “Save Changes” at the bottom of the page.\n\nNotes:  \n\\- If you're unable to update certain fields (like email), contact support for\nhelp.  \n\\- Your changes may take a few minutes to reflect across the system.\n\nFor privacy reasons, make sure your profile does not contain sensitive or\nincorrect information.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 700, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "68132a5f-44d0-4480-957c-c8c7833dfbbb": {"__data__": {"id_": "68132a5f-44d0-4480-957c-c8c7833dfbbb", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "test image", "url": "https://meghanag.supporthive.com/kb/article/18-test-image/", "article_id": 18, "section_id": 2, "last_updated_at": "2025-05-13T04:34:16Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "5b91b3f6-c851-439c-8179-61240a8680c1", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "test image", "url": "https://meghanag.supporthive.com/kb/article/18-test-image/", "article_id": 18, "section_id": 2, "last_updated_at": "2025-05-13T04:34:16Z", "has_attachments": true, "attachment_count": 1}, "hash": "9f3ae6b5402b9047fc4eac154bf9ebf7ed5fa61b2281644279125ccbdb614b98", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "![](https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-13/156de1d0-0a3e-4fbe-b6f1-fc888c203515/Screenshot_2025-05-13_at_10.03.18AM.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 159, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "81700bd3-2fab-4127-ac76-a52c01aed172": {"__data__": {"id_": "81700bd3-2fab-4127-ac76-a52c01aed172", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 18, "parent_article_title": "test image", "attachment_id": "img_18_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-13/156de1d0-0a3e-4fbe-b6f1-fc888c203515/Screenshot_2025-05-13_at_10.03.18AM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 266051, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "a10b4a94-9113-4dfa-89a6-fd7a90126941", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 18, "parent_article_title": "test image", "attachment_id": "img_18_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-13/156de1d0-0a3e-4fbe-b6f1-fc888c203515/Screenshot_2025-05-13_at_10.03.18AM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 266051, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "0d697121bf222c9c77a13ee04bcab9162c1f7dd09448d1f088f753a2639ae0ba", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "# Setting up your online store\n\nAfter you've set up Shopify, most of the work is done.\n\n- Your online store will automatically use your Shopify settings for checkout and order fulfillment.\n- Your products will automatically appear on your online store.\n- Your online store is automatically assigned a unique myshopify domain website address. This looks like your-store-name.myshopify.com and it's based on the store name that you entered when you signed up.\n\nThere are a few steps you should follow before launching to make sure your online store is ready for customers.\n\n## Grow your business\n\nIf you need help setting up or customizing your online store, then you can hire a Shopify expert.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 692, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "f1cde0af-6a56-4d9a-b12c-79c3ec286a5a": {"__data__": {"id_": "f1cde0af-6a56-4d9a-b12c-79c3ec286a5a", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "url sample", "url": "https://meghanag.supporthive.com/kb/article/21-url-sample/", "article_id": 21, "section_id": 2, "last_updated_at": "2025-05-16T04:09:12Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "c247c53c-fa97-4fef-8020-388cd1e3013b", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "url sample", "url": "https://meghanag.supporthive.com/kb/article/21-url-sample/", "article_id": 21, "section_id": 2, "last_updated_at": "2025-05-16T04:09:12Z", "has_attachments": false, "attachment_count": 0}, "hash": "c7c848b616e153c8035355ea56b0a60c223b05bdeccd47754d93f9bd2a2ca173", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "<http://example.com>", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 20, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}}}