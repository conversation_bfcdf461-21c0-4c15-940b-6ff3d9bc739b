{"docstore/metadata": {"31454265-b315-4152-a5d2-8d257f21f3df": {"doc_hash": "fed439483c0cc24384562934cb28505168d786fa1acbc5769afd1c0e12835bcd"}, "9f81e201-2810-44f1-9ee1-e4db1c7eb040": {"doc_hash": "8a5494c441ece48633a53b75daf0b05311652c44f689c4ba9733268764f59dcc"}, "a5deda65-bad9-40a0-a02c-bc3d15a2dde7": {"doc_hash": "2a28e58b4932f7c9607679250c65ac717418c470d4199269ada2af86dd087997"}, "fccb76cf-33ed-4e85-a513-0c1ab07d8e7b": {"doc_hash": "0793cfd15db579cb4e293e94f2bc8560749057301bd65ac3e7e32502a4da3921"}, "642e9028-7c27-45b7-8932-29cf3190e02d": {"doc_hash": "b0b36b00f4924589f7716b0e660006c6f5a576940cfc53283c232b07727b2660"}, "5954606f-f6c5-47c3-98d0-80cc9fc9f440": {"doc_hash": "d44a3ea2fc59d7eb8f9fa036a2871a8178290980950207be3df0ad42ab132548"}, "173c0c05-8637-4ba3-8026-8fe78a0018ce": {"doc_hash": "210ad9ca2605aa5359af49ece2a2e89183f9592a31acbc1859cc1afd5e5f586a"}, "8aeff3a3-b020-474c-b9c5-922c81514d98": {"doc_hash": "d1fa6f7372ce8fb79760860e0cf454b837d8276b706a6b2863f4fcef925eb41b"}, "50ad735a-7533-4c0f-b7d7-40f7f7234675": {"doc_hash": "84765eaa2bf28ee0ed61f27d804943888b25195481617e0254c86ecfe75183c0"}, "5ad3194e-e0cc-4caa-b66f-80a7ae898f96": {"doc_hash": "12b36b3c8eff916b966f2919a06114f2f92df51ff6869221371588a2b9cee116"}, "cb73052e-6bdf-4601-8971-f985702aa13c": {"doc_hash": "6e93c8072234979ca31dd632988a346a9bdee4582290cef922644f73559aee2e"}, "82924936-53ac-4bc0-9575-9847153c3f8d": {"doc_hash": "3d8f0fd8446ed2b6f59eff6c9b4f3847538ba4acd8b12c0c51c06a25d48d2b6f"}, "a0785772-393b-40dd-9000-abcd433f2c58": {"doc_hash": "fec774e6ae9f4cad6c7b360c60b8ee472c47b05b05ef6d18d252c6f0d7d5fca4"}, "90f6f0a4-81c1-453e-93ad-5e3afb8cb944": {"doc_hash": "f12aafbb8e54fcc0beddaed8f2b9df8697209e867fc57335f0f221d18cab303b"}, "9dbd0bd9-a3aa-4908-bb04-c6a6f4cc72d4": {"doc_hash": "5fc34357275c386018359f8121500b449f9c515e0234bd5aaa14a92553ce2565"}, "6cb44aee-6b88-4ad0-8ecc-5227837fcc12": {"doc_hash": "19fbdd0e91c80636d2159a8b4c0f6a73e6a6e20d08d0c931d0cfa26795058233"}, "12c93cd7-c76a-459c-87bd-5ec0bf8bc1c8": {"doc_hash": "7534a49773494e782ca6357ae21128bd9b4102502f93741a331d577977fd468d"}, "13a2dadd-93f6-4704-915a-0db2e08c7170": {"doc_hash": "afe85a7a80d5774b143cde13a1056a6d342d34b9e83b6e2fdeb8e11c4bcd772c"}, "7b0a86ae-f23f-4e14-9705-4413336dd0e0": {"doc_hash": "09144b56315d2d2541cb8b4ab0cb4b1177df56e8b6d30edde4c8b1b89221e19f"}, "dbfe0957-e0fe-4be1-9d78-1e89cb3fbd3b": {"doc_hash": "c221f98bf900ef40f44012a543dc67506611ee2b91ecc14f516565cd873755d4"}, "acfd3404-e36c-4893-8ea1-29e78e2fa37f": {"doc_hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f"}, "01833d1b-bcae-4be6-af5e-722d9ba3703a": {"doc_hash": "f1313277034e8b093b35b3352fa6f57c45a1829a90009999587d67527e82c0ae"}, "64bfae43-527d-453f-a18d-03aa6c5d088c": {"doc_hash": "94daa94105e4847e0d84debabad77a2032e2b63fe25745cd0ec4a35de5556ae9"}, "97278268-8d8f-4fc7-ae7a-9b77163f0f07": {"doc_hash": "db7d581f477a2bc6df40c80c33308ce401eebefc4e70ceee2abb42579aa5261e"}, "8809b4bd-2bd2-4efd-acb7-c26fc9cff0e7": {"doc_hash": "f75323da85e9730f572c96f760cd00697eaa56b143fbbf748f3b7d2d0f3c474b"}, "285904d3-a667-4e6b-9282-1d5a26a91093": {"doc_hash": "57020a0d5b3c5ff7cfbd32e43e13e2b828d913a3ac2b843ebf17dd2e2f7b4420"}, "8f54c51d-023a-4fc8-a57e-62210797a7a3": {"doc_hash": "ed3f62cbe935a18eef58b651a4711eb818d9dcae7596c9f67608233353d93e3b"}, "3178530f-ad9f-4b8b-a466-d213f8ac5118": {"doc_hash": "4fbdd043bcdf1692d04d1e24c64b3b4fad1e25532021280d87e6778f44a21c12"}, "de29d540-51c4-4110-b83e-bea5e13a457f": {"doc_hash": "ffa5c5d8d8cec1e1f4458b09a64779bd868870ac763e29aaadc2796130dfbd8d"}, "a5e51cfd-c339-4bd8-97d1-6d45fb549900": {"doc_hash": "7494f46e4dc66339eca63b3f344828bb960c709fab60fee43a26b072773fb5ea"}, "ae3d92f6-9367-42b1-8176-bcacc65b4e56": {"doc_hash": "7de16eccb9a8b2cbc6f52743973366a2c2a4750f7755c5bafe60a7c0a0ef9174"}, "08dc8b9e-dcb0-4f0a-a80b-1d7f0bf608e9": {"doc_hash": "f346b26dd871456feb7dba1fa4d576fdb2bbcff9fed3ac65eb84b798f05cb4ff"}, "9dc886c9-beca-434b-a9cf-ecf4996d395d": {"doc_hash": "f22a7bbbdca0ad17de472cf27b3944f7471c6fc1da5cd3f00bed67a1938fa369"}, "d8094c2e-cc0d-4ebb-826b-d3ef6c09faa8": {"doc_hash": "9f3ae6b5402b9047fc4eac154bf9ebf7ed5fa61b2281644279125ccbdb614b98"}, "dc255705-c6cf-4bea-bdac-3a7fda860d16": {"doc_hash": "0d697121bf222c9c77a13ee04bcab9162c1f7dd09448d1f088f753a2639ae0ba"}, "a8c110ab-466a-45d7-b919-00b2d16d37c1": {"doc_hash": "c7c848b616e153c8035355ea56b0a60c223b05bdeccd47754d93f9bd2a2ca173"}, "a95f822b-dec8-429e-929f-18486319e22b": {"doc_hash": "46a86abed7a8aa7e65c89298e6a89c76dff351da675d4390ab989113dfee86cf", "ref_doc_id": "31454265-b315-4152-a5d2-8d257f21f3df"}, "04bc6a19-3f1a-4fe7-ac9e-4eead3cdefae": {"doc_hash": "e507945e2b39e3d6090cdfaad4a09752efad2de4e59be21ace9ef2e9a7aa4eec", "ref_doc_id": "9f81e201-2810-44f1-9ee1-e4db1c7eb040"}, "9624e278-aab2-49a9-8130-f6ba26c9d649": {"doc_hash": "e8076cdbf063a169a85606ab841d0b0dbe7f0ae8d5923f8d17cbadc695115ebc", "ref_doc_id": "a5deda65-bad9-40a0-a02c-bc3d15a2dde7"}, "213286ee-501a-491c-a1bf-c2fa8b9c8aa5": {"doc_hash": "d5252924f1f1e32df134e8dbdcee45f184c8bd2d9701c56ed009040c2737e40d", "ref_doc_id": "fccb76cf-33ed-4e85-a513-0c1ab07d8e7b"}, "2cc0f9f0-3236-4ed9-9d3b-6b29f0b4edce": {"doc_hash": "580d2a1b32e339c6fe97da39b5b350b3d221e4e7d99946f350d866a1c8c6004a", "ref_doc_id": "642e9028-7c27-45b7-8932-29cf3190e02d"}, "d832ddcb-9f26-494a-8be1-5bd296646c39": {"doc_hash": "dbdd725a2dc3eb9927e4dec367fa659933c9d465f94e5acd6e761f2c6253cc0a", "ref_doc_id": "5954606f-f6c5-47c3-98d0-80cc9fc9f440"}, "59c8d5d2-d165-4611-912d-9ff13cfcfdd0": {"doc_hash": "5f68e1b4f5c3568fdf5c92b7b5568a53fe10ad316ef1a1b4467f212b2dda586e", "ref_doc_id": "173c0c05-8637-4ba3-8026-8fe78a0018ce"}, "8a5df7ec-d6e4-4a12-9ce1-f7dcc6bab1e9": {"doc_hash": "f6dc14c93bcd9bf78bf64b038c998188d24dd1c2eeddcb4b25d5567d4ba9ed5f", "ref_doc_id": "8aeff3a3-b020-474c-b9c5-922c81514d98"}, "f889e586-66e7-4b6b-b7e1-5e9cfe6ec0bd": {"doc_hash": "81547d30984b81ba7fc29cccfe974d8bd9da3459fa401b2c1cce79ef261b266b", "ref_doc_id": "50ad735a-7533-4c0f-b7d7-40f7f7234675"}, "49ee5980-81ce-43f9-8e10-ea045ad48985": {"doc_hash": "693b1868666acb14e70149bfd038ad8cef2b0bbf39bb3cffb72d880cc8aacd89", "ref_doc_id": "5ad3194e-e0cc-4caa-b66f-80a7ae898f96"}, "cccf526c-46b8-4af1-9b2f-1daf9117b7ff": {"doc_hash": "f6e586de98a44701284288c2a2ae14add80a1c88b13ca098e4971abb33c17036", "ref_doc_id": "cb73052e-6bdf-4601-8971-f985702aa13c"}, "954d8f14-61df-46aa-95a7-eb5bef5ad178": {"doc_hash": "ac85faaeb48acd761412c4d5b7c89c0dac2316a0863ca23ca7f1d8c3cc98774d", "ref_doc_id": "82924936-53ac-4bc0-9575-9847153c3f8d"}, "7f99b50c-f044-45b4-a031-02e1731b657f": {"doc_hash": "006639b0301245b1a65425b30dd201c43067eea5724110d5bae59e4512840d8f", "ref_doc_id": "82924936-53ac-4bc0-9575-9847153c3f8d"}, "3e1a2b4d-dd39-4488-8214-03c98ae89271": {"doc_hash": "801beb1ebd8d52cc9f0b9db4be01244bdde142b093dc546d4b4c9e31e3ca7cb2", "ref_doc_id": "a0785772-393b-40dd-9000-abcd433f2c58"}, "2c39380f-831c-47fa-a078-ed4895fac52d": {"doc_hash": "380822a7a70728c26afaa50a184e89b91b85a12b1244c75893042ecdee7927b5", "ref_doc_id": "90f6f0a4-81c1-453e-93ad-5e3afb8cb944"}, "efe60a7d-0f2d-4c17-b9d5-95c896886e9c": {"doc_hash": "03e4b3711417c9616e77368455d27f5032adc3d99a263d1f933ea7c70865fc16", "ref_doc_id": "9dbd0bd9-a3aa-4908-bb04-c6a6f4cc72d4"}, "8a67bfed-30c6-4de2-9b3c-7e58b74f5f92": {"doc_hash": "3a45789429fe92c8621a7ec8659fd49f13322c8ef613566d7fb5b7bcb9129a41", "ref_doc_id": "9dbd0bd9-a3aa-4908-bb04-c6a6f4cc72d4"}, "9011688d-6a57-4e60-9622-bf6496da684a": {"doc_hash": "30155641c86e21feb45cf25c15f9aea11593e75621dd49c9f54a7b89023535f3", "ref_doc_id": "6cb44aee-6b88-4ad0-8ecc-5227837fcc12"}, "3de7d6a8-291c-4377-bac0-d3c9505de23e": {"doc_hash": "e0b837c538e317c6d6a609c7ce0d19e6be829dab54d0ba902962e52248dbe228", "ref_doc_id": "6cb44aee-6b88-4ad0-8ecc-5227837fcc12"}, "7abf9b73-c148-4ec3-a83a-6c2062e5d53b": {"doc_hash": "bdcb4c5794e460093265ac30d66a71a84bcffbe7bc09e339f84a3c42fc960b99", "ref_doc_id": "12c93cd7-c76a-459c-87bd-5ec0bf8bc1c8"}, "10494bec-8295-417c-a102-855f66d7292f": {"doc_hash": "dee32cfe73d7c9df921dfa1f0311a51d1b77b68c8716cf64711d6265ad7a2b99", "ref_doc_id": "12c93cd7-c76a-459c-87bd-5ec0bf8bc1c8"}, "59b5bf04-0429-4edb-a1b4-6945fe683241": {"doc_hash": "d4801c58192f82a245e88de1a19bb28a33f0a9e18d18d6f1c03a4a66ed16a6af", "ref_doc_id": "12c93cd7-c76a-459c-87bd-5ec0bf8bc1c8"}, "88377197-92fa-4a83-8bbe-7f60087297e5": {"doc_hash": "32abf37b5287b6d44c829b38d64ae93cf758f79da9982200d1b484688c8540bc", "ref_doc_id": "13a2dadd-93f6-4704-915a-0db2e08c7170"}, "5735e0f4-87da-4e56-b5d3-020afa1693a1": {"doc_hash": "ee49e75b9f5a42f898f5557222fd94c2f2ee8590de0b60ebe1996e59e37030c4", "ref_doc_id": "7b0a86ae-f23f-4e14-9705-4413336dd0e0"}, "0aa7c03e-bd4e-46e9-b488-dd01f42be369": {"doc_hash": "7ea12b0a0dce3160de2a5546d4087447c0036dda87c7f9e79a90bd0ab368933c", "ref_doc_id": "dbfe0957-e0fe-4be1-9d78-1e89cb3fbd3b"}, "5b23067d-24d0-4222-8c31-7175cb4e6729": {"doc_hash": "5bfa81a993f202f499f35f74d23997a103b69f643381c30c7fde49e60bce9fd2", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "42924160-e02f-459e-8d85-bf2d1130080e": {"doc_hash": "827f4e7ad86c18c0eac8b5c836b4a4fe143074a7b34aae4f3fa5e7a503b950e6", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "0f5010dc-d1bb-43c5-ad9b-75ea3f0f45ee": {"doc_hash": "e16523dd6c4075a247e2d821ba56a466f8b909ced561095aad025459f1215c43", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "6cb1005f-f6ad-417a-92e3-ea55b48756b1": {"doc_hash": "69dc5ddd3666b3a290912ea035fa4270a217f79b6d5a4e72aabfd0c13af7dcb8", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "7e2a1bcc-50f4-4bef-b09e-c40e8fc826c7": {"doc_hash": "ade4d99f768478a9b62c2caae1c769d96b1752831523144628513c40d7e14aa4", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "0e3a0854-1c0a-47c8-9ade-299b015b71ad": {"doc_hash": "69ba0ef9d57acc62166ce6c9d1238a9590c1f5101cd852d3f6f72d179ba32309", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "4ec32129-7c18-4b67-a31a-fd9b3350857a": {"doc_hash": "816c1bbd216981b344ca30f2bbef39b4c68413be9ac000adc41076b5f94033a3", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "743176a4-bdc9-43cf-adeb-4efa0348924d": {"doc_hash": "ffd862ffa8c0a7c5260ddfb98c3dab4d0c6085945e640a0f3c8405c9a17b4c3e", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "56e8e050-b5db-4987-99ae-354c33c2e0dc": {"doc_hash": "5ada284cbd1cd53e4e319e19198ff49c00c427869d99c0297914cdeb42ba0a9f", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "89b1412d-99a5-45a2-a474-a69ff348ef3f": {"doc_hash": "7237ddc8bd32b7678ea148cc9db28f27344b98f2f5e2807367da8bc6e6d820c0", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "70a1e8e9-c497-4cca-9c77-b80b6db41029": {"doc_hash": "987f6e8bba169fe798f23cb46e872caf436812131561e3cbfef4174d6c1cc2e3", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "ab57d733-4bca-4192-b118-a112c10c180d": {"doc_hash": "85fbe36ac1f7618e60ca725f5c52ec769462f0ad72cbe9fd19ba7026f8d91fed", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "0f81384d-7038-49c1-ac5a-a66638b6f261": {"doc_hash": "8d37f1a165260bb300e70eb5e9263b7b05543d338d0f979362325c60ff7a8904", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "f94d4348-5252-4fe8-8517-c54667f90df9": {"doc_hash": "6e0da5200e4d7c0f9aa16ccb8075c1672a523c2d3db384ee738b85b811bb6753", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "527a8230-8908-462e-a7be-2725a631c9aa": {"doc_hash": "d736846cfc7c88c1823560fad14672ebe1e591590223935a293d787e60967d45", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "8737ca0c-2fa6-4c0e-bc29-33eb09025c5c": {"doc_hash": "1e1587c4faf131719b3ef8a4467924868714ad2c339a9c292b60295c70337f08", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "4c0e3361-57e3-41c1-a26d-bfa6086a99f9": {"doc_hash": "9903ab6763660d4728c0851b72fcdfb45b8382f4f6e36227a984edf1473d6641", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "7bf10cf5-8409-403f-b1c2-83be3841c896": {"doc_hash": "94d666c047687ad93e26484a8836ad782a4ec082f0862f47f4ad70f7b3235bdd", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "4afb02cc-35fe-4f37-bfc4-26a32bee0ef0": {"doc_hash": "d5558bafa118d95349c0326191e9fe8929bc314725d4023e90fc81ec36f47575", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "fa4feccb-61a5-41cb-a482-c72e69e54856": {"doc_hash": "18ba13030d48db1a0c2b36fdd8f3144e0a1a9f7ab8597e862bb73194a7a58be1", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "af05490a-bca2-4b79-be69-a515bdcf4d83": {"doc_hash": "08d04f1abd1e0dd027f02cee8e73be16e9bd5b6810f6029366914cb718e001bf", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "99103616-545e-4890-9e77-2a033aebdc6b": {"doc_hash": "18125259d646d4ff9da81e99c926aeaa9ac7bf4a0379b0fcb19b16e5e4ac78c5", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "a950df32-e82e-47b6-a3b5-822129b40abd": {"doc_hash": "2996433c3e697e2f89095750aaa861d2904ec85b7962227374f8ff8ed4e8aa2e", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "ef91e41c-2978-4f08-a067-401bb98200eb": {"doc_hash": "fe86fb0771ef0410920686bf23d5d472bab5896247efd5aae0d38090c33981e3", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "8272c857-048a-447b-91b3-a7cb20b00acd": {"doc_hash": "a87c3cb40c5578c44076c584cc3561e9a6cb013b409db5a481cd2790f95f8954", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "530c03ec-ed23-449d-b409-1fd5c2ddfd0d": {"doc_hash": "da28631130a3a6513f7e2e001c5e62040e233f008c7cce77c43d7d059a871fd0", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "4653dbba-baf0-4169-8695-cb9322b901f7": {"doc_hash": "99b37d651cef36e8a5050628fee9402075204d51962bc4106d830a4a69cd7b9f", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "36876a93-2937-4094-a829-f4c9e50a068d": {"doc_hash": "d22611244d1e8534d80994efc8329f524bc1993b849bf005eac69a0dd1e0688a", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "d352348a-364a-4e5e-a2cb-c44d7580d6f0": {"doc_hash": "04865b3c4cef7fb363c445bd36eb93c0324559b3d0c11998149d671ffc176a34", "ref_doc_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f"}, "828a63b2-6f36-4328-9adb-c4ebb5bce407": {"doc_hash": "19c74261abd32e7209b0ecb48bde680c6dc8c6f8d03ab9f2b4187131aa26542c", "ref_doc_id": "01833d1b-bcae-4be6-af5e-722d9ba3703a"}, "d7cf8378-6f8c-4082-8f9d-73477246ae63": {"doc_hash": "c54ef56571301d2ad42e3a8d6700e2c0f3886e35f6ddac066ff10649210fc3fd", "ref_doc_id": "64bfae43-527d-453f-a18d-03aa6c5d088c"}, "e8a17a3e-b5d4-49cd-87ee-cdd6d37427af": {"doc_hash": "454c25b58c793ed7fb080579bc69efd77c2571d188c5318e9eb641be479a9c1d", "ref_doc_id": "97278268-8d8f-4fc7-ae7a-9b77163f0f07"}, "b00e2cc1-0ea9-4563-b228-a8bfe9dbb63f": {"doc_hash": "91a27c3fe45cf1b50a38ad54750809580c10c7eef5af32b7bca0ec0cf5102bc5", "ref_doc_id": "8809b4bd-2bd2-4efd-acb7-c26fc9cff0e7"}, "1489556f-32ab-41b3-9c81-bf0516c36c99": {"doc_hash": "01f5e700452c22143ad17116491522f1ca0a37df3df64866e5e2bffd8d92ab89", "ref_doc_id": "285904d3-a667-4e6b-9282-1d5a26a91093"}, "c21bbb19-da47-4594-bde1-cba69b6b2b4f": {"doc_hash": "8861be49b0d32ecd06406bbe7402a4a0afc00fd5b215cfcd6d7b9d54f7f35a03", "ref_doc_id": "8f54c51d-023a-4fc8-a57e-62210797a7a3"}, "718ca858-f9d3-4177-9eb2-cdf93e783643": {"doc_hash": "8e29a96e1a6da7442122e44029073d976986daf959d825823cad3c471eb19650", "ref_doc_id": "3178530f-ad9f-4b8b-a466-d213f8ac5118"}, "073f8dac-115f-4678-923d-c05aef95c68e": {"doc_hash": "95086298c5e83729393e48d3a01de69be652ccb0cce87c2d3a5413b62d44a0c7", "ref_doc_id": "de29d540-51c4-4110-b83e-bea5e13a457f"}, "646d729c-2799-48c0-9f7c-1aa2109b8960": {"doc_hash": "cc07e8ae0ab0d9cdc301204db87ada4184fb6efa7efae198b015e134ea4ecb13", "ref_doc_id": "a5e51cfd-c339-4bd8-97d1-6d45fb549900"}, "b08063b2-e7be-4858-a67a-4ae0e9db0c04": {"doc_hash": "4c3c29260dddb6bfa73494e12a4ae1ea63dd7c3b12eb1657e52cec74e3c81b66", "ref_doc_id": "ae3d92f6-9367-42b1-8176-bcacc65b4e56"}, "********-746b-4734-9313-e839a2d51e4d": {"doc_hash": "a353a7a35addd8b5c7e01934926d0c33b93337401bc2f3fe8f3533f731ea51f5", "ref_doc_id": "08dc8b9e-dcb0-4f0a-a80b-1d7f0bf608e9"}, "47508fb5-80ef-4480-bf77-e6f000947568": {"doc_hash": "0ab9622ebb7f978e00851a7c329e2337ac9935bbf6c4e688a2a8b88b304a6334", "ref_doc_id": "9dc886c9-beca-434b-a9cf-ecf4996d395d"}, "3f65a99c-788c-4015-9349-9c5c5b3d5e58": {"doc_hash": "a8986d8d95970402beb17d2ff2c413949926923508ae05cc26a00badb9dedd10", "ref_doc_id": "d8094c2e-cc0d-4ebb-826b-d3ef6c09faa8"}, "443a2e8c-038e-4c6f-a773-42ee7e485325": {"doc_hash": "6a5907e069bcc416fe382af8f3f42a9b706a566d9b626cf5ca16fd4881ae0e4d", "ref_doc_id": "dc255705-c6cf-4bea-bdac-3a7fda860d16"}, "c8d3a80e-43cb-46f5-88cb-1489298595b3": {"doc_hash": "cc72d71f09827b9aa6fe3bbafb3817c568b27d20f421d95306ed48190c7e874b", "ref_doc_id": "a8c110ab-466a-45d7-b919-00b2d16d37c1"}}, "docstore/ref_doc_info": {"31454265-b315-4152-a5d2-8d257f21f3df": {"node_ids": ["a95f822b-dec8-429e-929f-18486319e22b"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Change Your Email Preferences", "url": "https://meghanag.supporthive.com/kb/article/9-how-to-change-your-email-preferences/", "article_id": 9, "section_id": 2, "last_updated_at": "2025-04-18T03:44:32Z", "has_attachments": false, "attachment_count": 0}}, "9f81e201-2810-44f1-9ee1-e4db1c7eb040": {"node_ids": ["04bc6a19-3f1a-4fe7-ac9e-4eead3cdefae"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password", "url": "https://meghanag.supporthive.com/kb/article/6-how-to-reset-your-password/", "article_id": 6, "section_id": 2, "last_updated_at": "2025-04-18T03:41:50Z", "has_attachments": false, "attachment_count": 0}}, "a5deda65-bad9-40a0-a02c-bc3d15a2dde7": {"node_ids": ["9624e278-aab2-49a9-8130-f6ba26c9d649"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password in HappyFox", "url": "https://meghanag.supporthive.com/kb/article/11-how-to-reset-your-password-in-happyfox/", "article_id": 11, "section_id": 2, "last_updated_at": "2025-04-18T07:53:43Z", "has_attachments": false, "attachment_count": 0}}, "fccb76cf-33ed-4e85-a513-0c1ab07d8e7b": {"node_ids": ["213286ee-501a-491c-a1bf-c2fa8b9c8aa5"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Submit a Ticket for Support", "url": "https://meghanag.supporthive.com/kb/article/8-how-to-submit-a-ticket-for-support/", "article_id": 8, "section_id": 2, "last_updated_at": "2025-04-18T03:43:50Z", "has_attachments": false, "attachment_count": 0}}, "642e9028-7c27-45b7-8932-29cf3190e02d": {"node_ids": ["2cc0f9f0-3236-4ed9-9d3b-6b29f0b4edce"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Update Your Contact Information", "url": "https://meghanag.supporthive.com/kb/article/7-how-to-update-your-contact-information/", "article_id": 7, "section_id": 2, "last_updated_at": "2025-04-18T03:42:44Z", "has_attachments": false, "attachment_count": 0}}, "5954606f-f6c5-47c3-98d0-80cc9fc9f440": {"node_ids": ["d832ddcb-9f26-494a-8be1-5bd296646c39"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Use Two-Factor Authentication (2FA)", "url": "https://meghanag.supporthive.com/kb/article/10-how-to-use-two-factor-authentication-2fa/", "article_id": 10, "section_id": 2, "last_updated_at": "2025-04-18T03:45:13Z", "has_attachments": false, "attachment_count": 0}}, "173c0c05-8637-4ba3-8026-8fe78a0018ce": {"node_ids": ["59c8d5d2-d165-4611-912d-9ff13cfcfdd0"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Tables as image and images", "url": "https://meghanag.supporthive.com/kb/article/16-kb-1-tables-as-image-and-images/", "article_id": 16, "section_id": 2, "last_updated_at": "2025-04-29T06:01:25Z", "has_attachments": true, "attachment_count": 4}}, "8aeff3a3-b020-474c-b9c5-922c81514d98": {"node_ids": ["8a5df7ec-d6e4-4a12-9ce1-f7dcc6bab1e9"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}}, "50ad735a-7533-4c0f-b7d7-40f7f7234675": {"node_ids": ["f889e586-66e7-4b6b-b7e1-5e9cfe6ec0bd"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}}, "5ad3194e-e0cc-4caa-b66f-80a7ae898f96": {"node_ids": ["49ee5980-81ce-43f9-8e10-ea045ad48985"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/37f48d3f-8d70-400d-8ed6-181972e8a8e4/Screen_Shot_2022-07-13_at_12.35.04_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 53841, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}}, "cb73052e-6bdf-4601-8971-f985702aa13c": {"node_ids": ["cccf526c-46b8-4af1-9b2f-1daf9117b7ff"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-\nfiles-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/681a6847-eac3-485e-8ef7-3ab20542f58e/Duralite-100E_Expansion_Kit.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 1615879, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}}, "82924936-53ac-4bc0-9575-9847153c3f8d": {"node_ids": ["954d8f14-61df-46aa-95a7-eb5bef5ad178", "7f99b50c-f044-45b4-a031-02e1731b657f"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}}, "a0785772-393b-40dd-9000-abcd433f2c58": {"node_ids": ["3e1a2b4d-dd39-4488-8214-03c98ae89271"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}}, "90f6f0a4-81c1-453e-93ad-5e3afb8cb944": {"node_ids": ["2c39380f-831c-47fa-a078-ed4895fac52d"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}}, "9dbd0bd9-a3aa-4908-bb04-c6a6f4cc72d4": {"node_ids": ["efe60a7d-0f2d-4c17-b9d5-95c896886e9c", "8a67bfed-30c6-4de2-9b3c-7e58b74f5f92"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 176549, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}}, "6cb44aee-6b88-4ad0-8ecc-5227837fcc12": {"node_ids": ["9011688d-6a57-4e60-9622-bf6496da684a", "3de7d6a8-291c-4377-bac0-d3c9505de23e"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 213826, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}}, "12c93cd7-c76a-459c-87bd-5ec0bf8bc1c8": {"node_ids": ["7abf9b73-c148-4ec3-a83a-6c2062e5d53b", "10494bec-8295-417c-a102-855f66d7292f", "59b5bf04-0429-4edb-a1b4-6945fe683241"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}}, "13a2dadd-93f6-4704-915a-0db2e08c7170": {"node_ids": ["88377197-92fa-4a83-8bbe-7f60087297e5"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 15, "parent_article_title": "KB-1 Text & Table", "attachment_id": "img_15_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}}, "7b0a86ae-f23f-4e14-9705-4413336dd0e0": {"node_ids": ["5735e0f4-87da-4e56-b5d3-020afa1693a1"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 15, "parent_article_title": "KB-1 Text & Table", "attachment_id": "img_15_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://hf-\nfiles-oregon.s3-us-\nwest-2.amazonaws.com/hdpdigitalwatchdog_kb_attachments/2019/09-24/4763eb2b-7c2d-49d6-bfc0-47620cc0f150/image.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 11165, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}}, "dbfe0957-e0fe-4be1-9d78-1e89cb3fbd3b": {"node_ids": ["0aa7c03e-bd4e-46e9-b488-dd01f42be369"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "PDF", "url": "https://meghanag.supporthive.com/kb/article/22-pdf/", "article_id": 22, "section_id": 2, "last_updated_at": "2025-06-16T09:01:30Z", "has_attachments": true, "attachment_count": 1}}, "acfd3404-e36c-4893-8ea1-29e78e2fa37f": {"node_ids": ["5b23067d-24d0-4222-8c31-7175cb4e6729", "42924160-e02f-459e-8d85-bf2d1130080e", "0f5010dc-d1bb-43c5-ad9b-75ea3f0f45ee", "6cb1005f-f6ad-417a-92e3-ea55b48756b1", "7e2a1bcc-50f4-4bef-b09e-c40e8fc826c7", "0e3a0854-1c0a-47c8-9ade-299b015b71ad", "4ec32129-7c18-4b67-a31a-fd9b3350857a", "743176a4-bdc9-43cf-adeb-4efa0348924d", "56e8e050-b5db-4987-99ae-354c33c2e0dc", "89b1412d-99a5-45a2-a474-a69ff348ef3f", "70a1e8e9-c497-4cca-9c77-b80b6db41029", "ab57d733-4bca-4192-b118-a112c10c180d", "0f81384d-7038-49c1-ac5a-a66638b6f261", "f94d4348-5252-4fe8-8517-c54667f90df9", "527a8230-8908-462e-a7be-2725a631c9aa", "8737ca0c-2fa6-4c0e-bc29-33eb09025c5c", "4c0e3361-57e3-41c1-a26d-bfa6086a99f9", "7bf10cf5-8409-403f-b1c2-83be3841c896", "4afb02cc-35fe-4f37-bfc4-26a32bee0ef0", "fa4feccb-61a5-41cb-a482-c72e69e54856", "af05490a-bca2-4b79-be69-a515bdcf4d83", "99103616-545e-4890-9e77-2a033aebdc6b", "a950df32-e82e-47b6-a3b5-822129b40abd", "ef91e41c-2978-4f08-a067-401bb98200eb", "8272c857-048a-447b-91b3-a7cb20b00acd", "530c03ec-ed23-449d-b409-1fd5c2ddfd0d", "4653dbba-baf0-4169-8695-cb9322b901f7", "36876a93-2937-4094-a829-f4c9e50a068d", "d352348a-364a-4e5e-a2cb-c44d7580d6f0"], "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}}, "01833d1b-bcae-4be6-af5e-722d9ba3703a": {"node_ids": ["828a63b2-6f36-4328-9adb-c4ebb5bce407"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample article", "url": "https://meghanag.supporthive.com/kb/article/12-sample-article/", "article_id": 12, "section_id": 2, "last_updated_at": "2025-04-22T05:44:42Z", "has_attachments": false, "attachment_count": 0}}, "64bfae43-527d-453f-a18d-03aa6c5d088c": {"node_ids": ["d7cf8378-6f8c-4082-8f9d-73477246ae63"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample docx", "url": "https://meghanag.supporthive.com/kb/article/17-sample-docx/", "article_id": 17, "section_id": 2, "last_updated_at": "2025-05-12T06:18:44Z", "has_attachments": true, "attachment_count": 1}}, "97278268-8d8f-4fc7-ae7a-9b77163f0f07": {"node_ids": ["e8a17a3e-b5d4-49cd-87ee-cdd6d37427af"], "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 17, "parent_article_title": "sample docx", "attachment_id": 1, "attachment_name": "sample_kb_article.docx", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-12/69860b5a-91bb-4339-bf21-5b9c647d9053/sample_kb_article.docx", "mime_type": "application/octet-stream", "file_extension": ".docx", "file_size": 37001, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}}, "8809b4bd-2bd2-4efd-acb7-c26fc9cff0e7": {"node_ids": ["b00e2cc1-0ea9-4563-b228-a8bfe9dbb63f"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample image", "url": "https://meghanag.supporthive.com/kb/article/19-sample-image/", "article_id": 19, "section_id": 2, "last_updated_at": "2025-05-14T11:23:38Z", "has_attachments": true, "attachment_count": 1}}, "285904d3-a667-4e6b-9282-1d5a26a91093": {"node_ids": ["1489556f-32ab-41b3-9c81-bf0516c36c99"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 19, "parent_article_title": "sample image", "attachment_id": "img_19_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-14/89f0213e-8ddf-415e-8bce-1f32bdbe8c43/Screenshot_2025-05-14_at_4.52.20PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 625662, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}}, "8f54c51d-023a-4fc8-a57e-62210797a7a3": {"node_ids": ["c21bbb19-da47-4594-bde1-cba69b6b2b4f"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "segment", "url": "https://meghanag.supporthive.com/kb/article/20-segment/", "article_id": 20, "section_id": 2, "last_updated_at": "2025-05-15T15:07:55Z", "has_attachments": true, "attachment_count": 1}}, "3178530f-ad9f-4b8b-a466-d213f8ac5118": {"node_ids": ["718ca858-f9d3-4177-9eb2-cdf93e783643"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 20, "parent_article_title": "segment", "attachment_id": "img_20_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-15/9970e608-34ec-4d3e-8230-a0be6f858b31/Screenshot_2025-05-15_at_8.37.11PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 94007, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}}, "de29d540-51c4-4110-b83e-bea5e13a457f": {"node_ids": ["073f8dac-115f-4678-923d-c05aef95c68e"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article", "url": "https://meghanag.supporthive.com/kb/article/1-test-article/", "article_id": 1, "section_id": 2, "last_updated_at": "2025-04-08T16:05:41Z", "has_attachments": false, "attachment_count": 0}}, "a5e51cfd-c339-4bd8-97d1-6d45fb549900": {"node_ids": ["646d729c-2799-48c0-9f7c-1aa2109b8960"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 2", "url": "https://meghanag.supporthive.com/kb/article/2-test-article-2/", "article_id": 2, "section_id": 2, "last_updated_at": "2025-04-09T10:10:39Z", "has_attachments": false, "attachment_count": 0}}, "ae3d92f6-9367-42b1-8176-bcacc65b4e56": {"node_ids": ["b08063b2-e7be-4858-a67a-4ae0e9db0c04"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 3", "url": "https://meghanag.supporthive.com/kb/article/3-test-article-3/", "article_id": 3, "section_id": 3, "last_updated_at": "2025-04-16T06:34:30Z", "has_attachments": false, "attachment_count": 0}}, "08dc8b9e-dcb0-4f0a-a80b-1d7f0bf608e9": {"node_ids": ["********-746b-4734-9313-e839a2d51e4d"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 4", "url": "https://meghanag.supporthive.com/kb/article/4-test-article-4/", "article_id": 4, "section_id": 2, "last_updated_at": "2025-04-16T06:36:18Z", "has_attachments": false, "attachment_count": 0}}, "9dc886c9-beca-434b-a9cf-ecf4996d395d": {"node_ids": ["47508fb5-80ef-4480-bf77-e6f000947568"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 5", "url": "https://meghanag.supporthive.com/kb/article/5-test-article-5/", "article_id": 5, "section_id": 2, "last_updated_at": "2025-04-16T07:44:33Z", "has_attachments": false, "attachment_count": 0}}, "d8094c2e-cc0d-4ebb-826b-d3ef6c09faa8": {"node_ids": ["3f65a99c-788c-4015-9349-9c5c5b3d5e58"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "test image", "url": "https://meghanag.supporthive.com/kb/article/18-test-image/", "article_id": 18, "section_id": 2, "last_updated_at": "2025-05-13T04:34:16Z", "has_attachments": true, "attachment_count": 1}}, "dc255705-c6cf-4bea-bdac-3a7fda860d16": {"node_ids": ["443a2e8c-038e-4c6f-a773-42ee7e485325"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 18, "parent_article_title": "test image", "attachment_id": "img_18_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-13/156de1d0-0a3e-4fbe-b6f1-fc888c203515/Screenshot_2025-05-13_at_10.03.18AM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 266051, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}}, "a8c110ab-466a-45d7-b919-00b2d16d37c1": {"node_ids": ["c8d3a80e-43cb-46f5-88cb-1489298595b3"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "url sample", "url": "https://meghanag.supporthive.com/kb/article/21-url-sample/", "article_id": 21, "section_id": 2, "last_updated_at": "2025-05-16T04:09:12Z", "has_attachments": false, "attachment_count": 0}}}, "docstore/data": {"a95f822b-dec8-429e-929f-18486319e22b": {"__data__": {"id_": "a95f822b-dec8-429e-929f-18486319e22b", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Change Your Email Preferences", "url": "https://meghanag.supporthive.com/kb/article/9-how-to-change-your-email-preferences/", "article_id": 9, "section_id": 2, "last_updated_at": "2025-04-18T03:44:32Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "31454265-b315-4152-a5d2-8d257f21f3df", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Change Your Email Preferences", "url": "https://meghanag.supporthive.com/kb/article/9-how-to-change-your-email-preferences/", "article_id": 9, "section_id": 2, "last_updated_at": "2025-04-18T03:44:32Z", "has_attachments": false, "attachment_count": 0}, "hash": "fed439483c0cc24384562934cb28505168d786fa1acbc5769afd1c0e12835bcd", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "To customize your email preferences, follow these steps:\n\n  1. Log in to your account.\n\n  2. Navigate to \"Settings\" in the top-right corner.\n\n  3. Click on \"Email Preferences.\"\n\n  4. Choose which types of emails you’d like to receive (e.g., system alerts, updates, newsletters).\n\n  5. Save your changes.\n\n**Troubleshooting:**\n\n  * If you no longer want to receive certain types of emails, make sure to unsubscribe or adjust your email preferences.\n\n  * If emails are being marked as spam, whitelist our email address in your email client.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 538, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "04bc6a19-3f1a-4fe7-ac9e-4eead3cdefae": {"__data__": {"id_": "04bc6a19-3f1a-4fe7-ac9e-4eead3cdefae", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password", "url": "https://meghanag.supporthive.com/kb/article/6-how-to-reset-your-password/", "article_id": 6, "section_id": 2, "last_updated_at": "2025-04-18T03:41:50Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "9f81e201-2810-44f1-9ee1-e4db1c7eb040", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password", "url": "https://meghanag.supporthive.com/kb/article/6-how-to-reset-your-password/", "article_id": 6, "section_id": 2, "last_updated_at": "2025-04-18T03:41:50Z", "has_attachments": false, "attachment_count": 0}, "hash": "8a5494c441ece48633a53b75daf0b05311652c44f689c4ba9733268764f59dcc", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "If you’ve forgotten your password or wish to change it for security reasons,\nfollow these steps to reset your password:\n\n  1. Go to the login page.\n\n  2. Click on the \"Forgot Password?\" link.\n\n  3. Enter your registered email address.\n\n  4. Check your inbox for a password reset email.\n\n  5. Click on the \"Reset Password\" link in the email.\n\n  6. Enter a new password and confirm it.\n\n  7. Log in with your new password.\n\n**Troubleshooting:**\n\n  * If you do not receive the reset email, check your spam or junk folder.\n\n  * Ensure you’ve entered the correct email address linked to your account.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 595, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "9624e278-aab2-49a9-8130-f6ba26c9d649": {"__data__": {"id_": "9624e278-aab2-49a9-8130-f6ba26c9d649", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password in HappyFox", "url": "https://meghanag.supporthive.com/kb/article/11-how-to-reset-your-password-in-happyfox/", "article_id": 11, "section_id": 2, "last_updated_at": "2025-04-18T07:53:43Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "a5deda65-bad9-40a0-a02c-bc3d15a2dde7", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password in HappyFox", "url": "https://meghanag.supporthive.com/kb/article/11-how-to-reset-your-password-in-happyfox/", "article_id": 11, "section_id": 2, "last_updated_at": "2025-04-18T07:53:43Z", "has_attachments": false, "attachment_count": 0}, "hash": "2a28e58b4932f7c9607679250c65ac717418c470d4199269ada2af86dd087997", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "If you need to reset your password for your HappyFox account, follow these\nsteps:\n\n  1. Go to the login page.\n\n  2. Click on the \"Forgot Password\" link.\n\n  3. Enter your registered email address.\n\n  4. You'll receive an email with a link to reset your password.\n\n  5. Click the link, and you'll be able to set a new password.\n\n**Note:** If you do not receive the email within a few minutes, check your\nspam folder or try again later.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 433, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "213286ee-501a-491c-a1bf-c2fa8b9c8aa5": {"__data__": {"id_": "213286ee-501a-491c-a1bf-c2fa8b9c8aa5", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Submit a Ticket for Support", "url": "https://meghanag.supporthive.com/kb/article/8-how-to-submit-a-ticket-for-support/", "article_id": 8, "section_id": 2, "last_updated_at": "2025-04-18T03:43:50Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "fccb76cf-33ed-4e85-a513-0c1ab07d8e7b", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Submit a Ticket for Support", "url": "https://meghanag.supporthive.com/kb/article/8-how-to-submit-a-ticket-for-support/", "article_id": 8, "section_id": 2, "last_updated_at": "2025-04-18T03:43:50Z", "has_attachments": false, "attachment_count": 0}, "hash": "0793cfd15db579cb4e293e94f2bc8560749057301bd65ac3e7e32502a4da3921", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "If you need further assistance, submitting a support ticket is the best way to\nget help from our team:\n\n  1. Log in to your account.\n\n  2. Go to the \"Support\" section.\n\n  3. Click on \"Submit a Ticket.\"\n\n  4. Select a category for your issue.\n\n  5. Fill out the ticket form with all relevant details.\n\n  6. Click \"Submit\" to send your ticket to our support team.\n\n  7. You will receive a confirmation email with a ticket number.\n\n**Troubleshooting:**\n\n  * If you do not see the ticket form, ensure you are logged in to your account.\n\n  * Make sure to provide as much detail as possible to expedite the resolution process.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 620, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "2cc0f9f0-3236-4ed9-9d3b-6b29f0b4edce": {"__data__": {"id_": "2cc0f9f0-3236-4ed9-9d3b-6b29f0b4edce", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Update Your Contact Information", "url": "https://meghanag.supporthive.com/kb/article/7-how-to-update-your-contact-information/", "article_id": 7, "section_id": 2, "last_updated_at": "2025-04-18T03:42:44Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "642e9028-7c27-45b7-8932-29cf3190e02d", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Update Your Contact Information", "url": "https://meghanag.supporthive.com/kb/article/7-how-to-update-your-contact-information/", "article_id": 7, "section_id": 2, "last_updated_at": "2025-04-18T03:42:44Z", "has_attachments": false, "attachment_count": 0}, "hash": "b0b36b00f4924589f7716b0e660006c6f5a576940cfc53283c232b07727b2660", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "You can update your contact information directly from your account settings:\n\n  1. Log in to your account.\n\n  2. Click on your profile picture in the top-right corner.\n\n  3. Select \"Account Settings.\"\n\n  4. Under \"Personal Information,\" click \"Edit.\"\n\n  5. Update your name, email, and phone number as needed.\n\n  6. Save your changes.\n\n**Troubleshooting:**\n\n  * Ensure the email address is valid and correctly formatted.\n\n  * If you experience issues saving changes, clear your browser cache or try a different browser.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 519, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "d832ddcb-9f26-494a-8be1-5bd296646c39": {"__data__": {"id_": "d832ddcb-9f26-494a-8be1-5bd296646c39", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Use Two-Factor Authentication (2FA)", "url": "https://meghanag.supporthive.com/kb/article/10-how-to-use-two-factor-authentication-2fa/", "article_id": 10, "section_id": 2, "last_updated_at": "2025-04-18T03:45:13Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "5954606f-f6c5-47c3-98d0-80cc9fc9f440", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Use Two-Factor Authentication (2FA)", "url": "https://meghanag.supporthive.com/kb/article/10-how-to-use-two-factor-authentication-2fa/", "article_id": 10, "section_id": 2, "last_updated_at": "2025-04-18T03:45:13Z", "has_attachments": false, "attachment_count": 0}, "hash": "d44a3ea2fc59d7eb8f9fa036a2871a8178290980950207be3df0ad42ab132548", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Two-factor authentication (2FA) adds an extra layer of security to your\naccount. Here’s how to enable it:\n\n  1. Log in to your account.\n\n  2. Go to \"Account Settings.\"\n\n  3. Under \"Security Settings,\" click on \"Enable Two-Factor Authentication.\"\n\n  4. Choose your preferred method (e.g., mobile app, email).\n\n  5. Follow the instructions to link your account to your 2FA method.\n\n  6. Enter the verification code sent to your device to complete the setup.\n\n**Troubleshooting:**\n\n  * If you lose access to your 2FA method, contact support for assistance.\n\n  * Ensure that the device used for 2FA is set up correctly to avoid verification issues.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 644, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "59c8d5d2-d165-4611-912d-9ff13cfcfdd0": {"__data__": {"id_": "59c8d5d2-d165-4611-912d-9ff13cfcfdd0", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Tables as image and images", "url": "https://meghanag.supporthive.com/kb/article/16-kb-1-tables-as-image-and-images/", "article_id": 16, "section_id": 2, "last_updated_at": "2025-04-29T06:01:25Z", "has_attachments": true, "attachment_count": 4}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "173c0c05-8637-4ba3-8026-8fe78a0018ce", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Tables as image and images", "url": "https://meghanag.supporthive.com/kb/article/16-kb-1-tables-as-image-and-images/", "article_id": 16, "section_id": 2, "last_updated_at": "2025-04-29T06:01:25Z", "has_attachments": true, "attachment_count": 4}, "hash": "210ad9ca2605aa5359af49ece2a2e89183f9592a31acbc1859cc1afd5e5f586a", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Duralite-100E Expansion Kit\n\n![print\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg)\n\nMonsoon Gohain\n\nSep 01, 2022\n\n![views\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg) 927\n\n![](https://hf-files-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/37f48d3f-8d70-400d-8ed6-181972e8a8e4/Screen_Shot_2022-07-13_at_12.35.04_PM.png)![](https://hf-\nfiles-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/681a6847-eac3-485e-8ef7-3ab20542f58e/Duralite-100E_Expansion_Kit.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 592, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8a5df7ec-d6e4-4a12-9ce1-f7dcc6bab1e9": {"__data__": {"id_": "8a5df7ec-d6e4-4a12-9ce1-f7dcc6bab1e9", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "8aeff3a3-b020-474c-b9c5-922c81514d98", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "hash": "d1fa6f7372ce8fb79760860e0cf454b837d8276b706a6b2863f4fcef925eb41b", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_1.png\nFile Type: image/png\nURL: https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg\n\nProcessing failed: Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 349, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "f889e586-66e7-4b6b-b7e1-5e9cfe6ec0bd": {"__data__": {"id_": "f889e586-66e7-4b6b-b7e1-5e9cfe6ec0bd", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "50ad735a-7533-4c0f-b7d7-40f7f7234675", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "hash": "84765eaa2bf28ee0ed61f27d804943888b25195481617e0254c86ecfe75183c0", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_2.png\nFile Type: image/png\nURL: https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg\n\nProcessing failed: Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 355, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "49ee5980-81ce-43f9-8e10-ea045ad48985": {"__data__": {"id_": "49ee5980-81ce-43f9-8e10-ea045ad48985", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/37f48d3f-8d70-400d-8ed6-181972e8a8e4/Screen_Shot_2022-07-13_at_12.35.04_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 53841, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "5ad3194e-e0cc-4caa-b66f-80a7ae898f96", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/37f48d3f-8d70-400d-8ed6-181972e8a8e4/Screen_Shot_2022-07-13_at_12.35.04_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 53841, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "12b36b3c8eff916b966f2919a06114f2f92df51ff6869221371588a2b9cee116", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "# Description\n\n!Table[The table contains a numbered list of 7 items describing various components or features:\n1. Handle\n2. Foldable legs\n3. USB ports & LED indicator\n4. DC output port\n5. 2x Extension Cable\n6. Barrel Connector (for connecting to portable power stations)\n7. MC4 Connector (for connecting to portable power stations)]", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 332, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "cccf526c-46b8-4af1-9b2f-1daf9117b7ff": {"__data__": {"id_": "cccf526c-46b8-4af1-9b2f-1daf9117b7ff", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-\nfiles-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/681a6847-eac3-485e-8ef7-3ab20542f58e/Duralite-100E_Expansion_Kit.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 1615879, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "cb73052e-6bdf-4601-8971-f985702aa13c", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-\nfiles-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/681a6847-eac3-485e-8ef7-3ab20542f58e/Duralite-100E_Expansion_Kit.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 1615879, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "6e93c8072234979ca31dd632988a346a9bdee4582290cef922644f73559aee2e", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "!Diagram[The diagram shows a technical illustration of the DuraLite portable solar panel (model GP-DURALITE-100). The main image depicts the solar panel with numbered callouts for key features:\n\n1. Handle: Located on the top left corner of the panel.\n2. Foldable Legs: Two legs that support the panel at an angle.\n3. USB Ports & LED Indicator: Shown in a small inset image at the top right, displaying the DuraLite logo and USB ports.\n4. DC Output Port: Located on the right side of the panel.\n\nBelow the main illustration are three smaller diagrams showing accessories:\n\n5. 2x Extension Cable: A coiled cable with connectors on both ends.\n6. Barrel Connector: A cable with different connectors on each end, labeled as \"for connecting to portable power stations\".\n7. MC4 Connector: Another cable with different connectors, also labeled for connecting to portable power stations.\n\nThe overall layout provides a clear visual guide to the product's components and connection options.]\n\n1. Handle\n2. Foldable Legs\n3. USB Ports & LED Indicator\n4. DC Output Port\n5. 2x Extension Cable\n6. Barrel Connector (for connecting to portable power stations)\n7. MC4 Connector (for connecting to portable power stations)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1203, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "954d8f14-61df-46aa-95a7-eb5bef5ad178": {"__data__": {"id_": "954d8f14-61df-46aa-95a7-eb5bef5ad178", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "82924936-53ac-4bc0-9575-9847153c3f8d", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "hash": "3d8f0fd8446ed2b6f59eff6c9b4f3847538ba4acd8b12c0c51c06a25d48d2b6f", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "7f99b50c-f044-45b4-a031-02e1731b657f", "node_type": "1", "metadata": {}, "hash": "f134e096d4bdf5b4db087bb4babc987598a1a8c457e30d2146add173551b2a14", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Automate Okta Identity Lifecycle Actions for requests in Zendesk\n\n![print\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg)\n\nNov 05, 2021\n\n![views\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg) 2209\n\nBy using HappyFox Workflows, IT admins can automate everyday identity\nlifecycle actions in Okta for user requests from Zendesk.\n\n**List of Okta actions that can be invoked/automated inside Zendesk:**\n\n  * Password resets (including expiring temporary password)\n  * Account unlocks\n  * Resetting any MFA enrollments\n  * Clearing existing user sessions\n  * Account suspensions\n  * Account deactivations.\n\n_[Click here](https://www.happyfox.com/workflows-for-zendesk-support/#book-\ndemo) _to book a 1-on-1 demo with our product experts\n\n**Pre-requisite**\n\nAn active HappyFox Workflows account with Zendesk support and Okta\nsuccessfully integrated.\n\n_Quicklinks:_\n\n  * [Zendesk configuration with HappyFox Workflows](https://support.happyfox.com/kb/article/1002-how-to-configure-happyfox-workflows-for-zendesk-support/)\n  * [Okta configuration with HappyFox Workflows](https://support.happyfox.com/kb/article/1136-configure-okta-integration-with-happyfox-workflows/)\n\n**Automated Workflow Example:**\n\n  1. A User raises an Okta unlock request in Zendesk\n  2. HappyFox Workflows automatically detects an unlock request and validates it.\n  3. HappyFox Workflows initiates the user unlock action in Okta automatically.\n  4. HappyFox Workflows sends an update to the user on the next steps.\n\n!", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1584, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "7f99b50c-f044-45b4-a031-02e1731b657f": {"__data__": {"id_": "7f99b50c-f044-45b4-a031-02e1731b657f", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "82924936-53ac-4bc0-9575-9847153c3f8d", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "hash": "3d8f0fd8446ed2b6f59eff6c9b4f3847538ba4acd8b12c0c51c06a25d48d2b6f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "954d8f14-61df-46aa-95a7-eb5bef5ad178", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "hash": "ac85faaeb48acd761412c4d5b7c89c0dac2316a0863ca23ca7f1d8c3cc98774d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "3. HappyFox Workflows initiates the user unlock action in Okta automatically.\n  4. HappyFox Workflows sends an update to the user on the next steps.\n\n![](https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png)\n\n**Manually Triggering Okta Actions:**\n\n<PERSON><PERSON> can also set up HappyFox Workflows configuration to manually trigger\nOkta actions from the context of a Zendesk Ticket.\n\n![](https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png)\n\nFeedback\n\n4 out of 4 found this helpful", "mimetype": "text/plain", "start_char_idx": 1433, "end_char_idx": 2113, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "3e1a2b4d-dd39-4488-8214-03c98ae89271": {"__data__": {"id_": "3e1a2b4d-dd39-4488-8214-03c98ae89271", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "a0785772-393b-40dd-9000-abcd433f2c58", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "hash": "fec774e6ae9f4cad6c7b360c60b8ee472c47b05b05ef6d18d252c6f0d7d5fca4", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_1.png\nFile Type: image/png\nURL: https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg\n\nProcessing failed: Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 349, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "2c39380f-831c-47fa-a078-ed4895fac52d": {"__data__": {"id_": "2c39380f-831c-47fa-a078-ed4895fac52d", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "90f6f0a4-81c1-453e-93ad-5e3afb8cb944", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "hash": "f12aafbb8e54fcc0beddaed8f2b9df8697209e867fc57335f0f221d18cab303b", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_2.png\nFile Type: image/png\nURL: https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg\n\nProcessing failed: Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 355, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "efe60a7d-0f2d-4c17-b9d5-95c896886e9c": {"__data__": {"id_": "efe60a7d-0f2d-4c17-b9d5-95c896886e9c", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 176549, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "9dbd0bd9-a3aa-4908-bb04-c6a6f4cc72d4", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 176549, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "5fc34357275c386018359f8121500b449f9c515e0234bd5aaa14a92553ce2565", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "8a67bfed-30c6-4de2-9b3c-7e58b74f5f92", "node_type": "1", "metadata": {}, "hash": "c7ff574e4e86c604d82ca9e010cd2ef844d6a0a9b076f747665fa8416f144140", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "# Help! I got locked out of my account\n\n7 minutes ago • <PERSON> <EMAIL> via Adikeshav c (change) • from Zendesk Support\n\n## Public reply Internal note\n\nTo <PERSON> CC\n\n### Conversations\n\nAll (4) Public (1) Internal (3)\n\n#### Workflow Bot (assign)\n3 minutes ago\n\nHey <PERSON>,\n\nThis is an automated response from the priority support team. We have successfully initiated the unlock procedure for your Okta account. Kindly check your email for the next steps!\n\n#### Adikeshav c (assign)\n4 minutes ago\n\nDetected that this is an account unlock account <NAME_EMAIL>. Proceeding to unlock the agent.\n\n#### <PERSON> (assign)\n7 minutes ago\n\nHey! I got Locked out of my Okta account. Could you please unlock my account?\n\n### Apply macro\n\n[Submit as Open]\n\n## Interactions\n\n- Help! I got locked out of my account\n  8 minutes ago • 6 comments\n- My order is not delivered. I had order...\n  Sep 02 05:08 • 2 comments\n- asdoijasdoiajsodijasd\n  Sep 02 04:46 • 2 comments\n- Need help with refund.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1013, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8a67bfed-30c6-4de2-9b3c-7e58b74f5f92": {"__data__": {"id_": "8a67bfed-30c6-4de2-9b3c-7e58b74f5f92", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 176549, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "9dbd0bd9-a3aa-4908-bb04-c6a6f4cc72d4", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 176549, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "5fc34357275c386018359f8121500b449f9c515e0234bd5aaa14a92553ce2565", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "efe60a7d-0f2d-4c17-b9d5-95c896886e9c", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 176549, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "03e4b3711417c9616e77368455d27f5032adc3d99a263d1f933ea7c70865fc16", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "I had order...\n  Sep 02 05:08 • 2 comments\n- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>di<PERSON>d\n  Sep 02 04:46 • 2 comments\n- Need help with refund. headphones ...\n  Sep 02 04:40 • 3 comments\n- <PERSON>\n  Sep 02 04:26 • 2 comments\n- Yandex Protocol\n  Sep 02 04:11 • 2 comments\n- <PERSON><PERSON> kid\n  Sep 02 04:08 • 1 comment\n- <PERSON><PERSON> rabbit\n  Sep 02 04:04 • 1 comment\n- he<PERSON><PERSON> 2\n  Sep 02 04:03 • 1 comment\n- <PERSON><PERSON><PERSON>\n  Sep 02 04:44 • 1 comment\n\n## <PERSON><EMAIL>\n+919123889202\nEastern Time (US & Canada)\nEnglish (United States)\nproduct_a\n\n[Add user notes]\n\n## Apps\n\n- Assist Bot\n- Whatsapp\n- HappyFox Workflows", "mimetype": "text/plain", "start_char_idx": 894, "end_char_idx": 1479, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "9011688d-6a57-4e60-9622-bf6496da684a": {"__data__": {"id_": "9011688d-6a57-4e60-9622-bf6496da684a", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 213826, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "6cb44aee-6b88-4ad0-8ecc-5227837fcc12", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 213826, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "19fbdd0e91c80636d2159a8b4c0f6a73e6a6e20d08d0c931d0cfa26795058233", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "3de7d6a8-291c-4377-bac0-d3c9505de23e", "node_type": "1", "metadata": {}, "hash": "f52449af3f3a802268f47894fa4e7fa1c982f0e23409c0097a3bf5d232acd3f0", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "# Help! I got locked out of my account\n\n## Ticket Details\n\n**Ticket #:** 274\n**Status:** OPEN\n**Created by:** <PERSON>\n**Created:** 23 minutes ago\n**Email:** <EMAIL> via Adikeshav c (change)\n**Source:** Zendesk Support\n\n## Customer Information\n\n**Name:** <PERSON>\n**Email:** <EMAIL>\n**Phone:** +************\n**Time Zone:** Eastern Time (US & Canada)\n**Language:** English (United States)\n**Product:** product_a\n\n## Conversation\n\n### Workflow Bot (19 minutes ago)\n\nHey <PERSON>,\n\nThis is an automated response from the priority support team. We have successfully initiated the unlock procedure for your Okta account. Kindly check your email for the next steps.\n\n## Sidebar Information\n\n### Interactions\n\n1. Help! I got locked out of my account\n   24 minutes ago - 6 comments\n\n2. My order is not delivered. I had order...\n   Sep 02 05:08 - 2 comments\n\n3. as<PERSON>ijasdoiajsodijasd\n   Sep 02 04:46 - 2 comments\n\n4. Need help with refund. headphones ...\n   Sep 02 04:40 - 3 comments\n\n5.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1001, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "3de7d6a8-291c-4377-bac0-d3c9505de23e": {"__data__": {"id_": "3de7d6a8-291c-4377-bac0-d3c9505de23e", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 213826, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "6cb44aee-6b88-4ad0-8ecc-5227837fcc12", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 213826, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "19fbdd0e91c80636d2159a8b4c0f6a73e6a6e20d08d0c931d0cfa26795058233", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "9011688d-6a57-4e60-9622-bf6496da684a", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 213826, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "30155641c86e21feb45cf25c15f9aea11593e75621dd49c9f54a7b89023535f3", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Need help with refund. headphones ...\n   Sep 02 04:40 - 3 comments\n\n5. <PERSON>\n   Sep 02 04:25 - 2 comments\n\n### Apps\n\n#### Assist Bot\n\n#### Whatsapp\n\n#### HappyFox Workflows\n\n- Activate a User\n- Add a User to a Group\n- Assign an App to a User\n- Clear all open sessions for a User\n- Deactivate a User\n- Expire the password for a User\n- Get User properties\n- Reactivate a User\n- Remove a User from a Group\n- Reset password for a User\n- Reset the Login Factors for a User\n\n## Action Buttons\n\n- Apply macro\n- Submit as Open", "mimetype": "text/plain", "start_char_idx": 931, "end_char_idx": 1456, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "7abf9b73-c148-4ec3-a83a-6c2062e5d53b": {"__data__": {"id_": "7abf9b73-c148-4ec3-a83a-6c2062e5d53b", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "12c93cd7-c76a-459c-87bd-5ec0bf8bc1c8", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "hash": "7534a49773494e782ca6357ae21128bd9b4102502f93741a331d577977fd468d", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "10494bec-8295-417c-a102-855f66d7292f", "node_type": "1", "metadata": {}, "hash": "acfd6453f89c628bc19f6949a31a8aa520a37b48688a8ff900926532cd9b072e", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Recorder and RAID Default Login List\n\n![print\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg)\n\n![https://hf-files-oregon.s3-us-\nwest-2.amazonaws.com/hdpdigitalwatchdog_kb_attachments/2019/09-24/4763eb2b-7c2d-49d6-bfc0-47620cc0f150/image.png](https://hf-\nfiles-oregon.s3-us-\nwest-2.amazonaws.com/hdpdigitalwatchdog_kb_attachments/2019/09-24/4763eb2b-7c2d-49d6-bfc0-47620cc0f150/image.png)\n\n# **Recorder and RAID Default Login List**\n\n\\-----------------------------------\n\n**Affected Roles:** Administrator, Owner\n\n**Related Digital Watchdog VMS Apps:** DW Spectrum® IPVMS\n\n**Last Edit:** August 6, 2024\n\n\\-----------------------------------\n\n# **De<PERSON>ult <PERSON>**\n\nThe DW Blackjack® Series and VMAX® Series units are shipped to use the default\nlogin credentials. DW Blackjack® units will typically not require a login to\naccess the OS, but VMAX® DVRs and NVRs do require the user to enter a login\nupon booting to use the unit.\n\nAdditionally, some DW Blackjack® Servers feature the LSI RAID Manager\nsoftware, which allows Administrators to manage and maintain the RAID array of\nthose special units.\n\nThis article will list the default login credentials of the DW Blackjack® and\nVMAX® recording units, as well as the default login to the LSI RAID Manager\nprogram for DW Blackjack® units with RAID support.\n\n****NOTE:** The default login for the DW Spectrum® IPVMS software\n(admin/admin12345) is the same on all DW Blackjack® units.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1474, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "10494bec-8295-417c-a102-855f66d7292f": {"__data__": {"id_": "10494bec-8295-417c-a102-855f66d7292f", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "12c93cd7-c76a-459c-87bd-5ec0bf8bc1c8", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "hash": "7534a49773494e782ca6357ae21128bd9b4102502f93741a331d577977fd468d", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "7abf9b73-c148-4ec3-a83a-6c2062e5d53b", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "hash": "bdcb4c5794e460093265ac30d66a71a84bcffbe7bc09e339f84a3c42fc960b99", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "59b5bf04-0429-4edb-a1b4-6945fe683241", "node_type": "1", "metadata": {}, "hash": "953bec790a5244a0c3353b325dae66da5a7e2aea7181eb05fe3829cf6a9ba033", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "****NOTE:** The default login for the DW Spectrum® IPVMS software\n(admin/admin12345) is the same on all DW Blackjack® units.\n\n# **Supported/Affected Devices:**\n\n  * DW Blackjack® Cube Series\n  * DW Blackjack® E-Rack/P-Rack Series\n  * DW Blackjack® X-Rack Series\n  * DW Blackjack® Intel Xeon Silver Processor 2U Series\n  * DW Blackjack® Tower Series\n  * DW Blackjack® MINI Series\n  * DW Blackjack® NAS Series\n  * VMAX® A1 Plus™ Series\n  * VMAX® IP Plus™ Series\n\n# **Default DW Blackjack Login List**\n\n**Device Series** |  **Default IP Address** |  **Default Unit Login**  \n---|---|---  \nDW Blackjack® Cube |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® E-Rack & P-Rack |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® Tower & Mid-Tower |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® X-Rack |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® Intel Xeon Silver Processor 2U |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® MINI |  DHCP |  admin/admin1234  \nDW Blackjack® NAS |  ************* |  admin/admin1234  \n  \n# ****NOTE:** For DW Blackjack® Servers with Windows, purchased prior to June\n18, 2021, the previously utilized default OS login is “ _admin/admin_ ”.\n\n****NOTE:** For DW Blackjack® Servers with Linux, purchased prior to June 18,\n2021, the previously utilized default OS login is “ _admin/admin_ ”.", "mimetype": "text/plain", "start_char_idx": 1350, "end_char_idx": 2643, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "59b5bf04-0429-4edb-a1b4-6945fe683241": {"__data__": {"id_": "59b5bf04-0429-4edb-a1b4-6945fe683241", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "12c93cd7-c76a-459c-87bd-5ec0bf8bc1c8", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "hash": "7534a49773494e782ca6357ae21128bd9b4102502f93741a331d577977fd468d", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "10494bec-8295-417c-a102-855f66d7292f", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "hash": "dee32cfe73d7c9df921dfa1f0311a51d1b77b68c8716cf64711d6265ad7a2b99", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "****NOTE:** For DW Blackjack® Servers with Linux, purchased prior to June 18,\n2021, the previously utilized default OS login is “ _admin/admin_ ”.\n\n# **Default LSI RAID Manager Login List**\n\n**Device Series** |  **Default RAID Login** |  **Example Login**  \n---|---|---  \nDW Blackjack® P-Rack and E-Rack (Windows) |  <system name>/admin |  _bjer4u120t/admin_  \nDW Blackjack® P-Rack and E-Rack (Ubuntu/Linux) |  root/admin |   \nDW Blackjack® X-Rack |  x-rack/Xrack1234 |   \n  \n# **Default VMAX® Unit Login List**\n\n**Device Series** |  **Default IP Address** |  **Default Unit Login**  \n---|---|---  \nVMAX® A1 Plus and VMAX A1 G4 |  ************* |  admin/<no password>  \nVMAX® IP Plus and VMAX VG4 |  ************* |  admin/<no password>  \nDW Compressor |  ************* |  admin/admin", "mimetype": "text/plain", "start_char_idx": 2497, "end_char_idx": 3281, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "88377197-92fa-4a83-8bbe-7f60087297e5": {"__data__": {"id_": "88377197-92fa-4a83-8bbe-7f60087297e5", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 15, "parent_article_title": "KB-1 Text & Table", "attachment_id": "img_15_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "13a2dadd-93f6-4704-915a-0db2e08c7170", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 15, "parent_article_title": "KB-1 Text & Table", "attachment_id": "img_15_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "section_id": 2, "is_embedded_image": true, "processing_error": "Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image"}, "hash": "afe85a7a80d5774b143cde13a1056a6d342d34b9e83b6e2fdeb8e11c4bcd772c", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_1.png\nFile Type: image/png\nURL: https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg\n\nProcessing failed: Image processing failed for image 0: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: Could not process image", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 349, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "5735e0f4-87da-4e56-b5d3-020afa1693a1": {"__data__": {"id_": "5735e0f4-87da-4e56-b5d3-020afa1693a1", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 15, "parent_article_title": "KB-1 Text & Table", "attachment_id": "img_15_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://hf-\nfiles-oregon.s3-us-\nwest-2.amazonaws.com/hdpdigitalwatchdog_kb_attachments/2019/09-24/4763eb2b-7c2d-49d6-bfc0-47620cc0f150/image.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 11165, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "7b0a86ae-f23f-4e14-9705-4413336dd0e0", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 15, "parent_article_title": "KB-1 Text & Table", "attachment_id": "img_15_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://hf-\nfiles-oregon.s3-us-\nwest-2.amazonaws.com/hdpdigitalwatchdog_kb_attachments/2019/09-24/4763eb2b-7c2d-49d6-bfc0-47620cc0f150/image.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 11165, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "09144b56315d2d2541cb8b4ab0cb4b1177df56e8b6d30edde4c8b1b89221e19f", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "# DIGITAL WATCHDOG\n\n!Image[The image shows a logo for Digital Watchdog. It consists of a stylized, geometric design of the letters \"DW\" in orange, positioned above the text \"DIGITAL WATCHDOG\" in bold, uppercase letters. The logo is centered on a white background, creating a strong contrast with the orange color. The design suggests a modern, technology-focused brand identity, possibly related to security or surveillance.]", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 425, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "0aa7c03e-bd4e-46e9-b488-dd01f42be369": {"__data__": {"id_": "0aa7c03e-bd4e-46e9-b488-dd01f42be369", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "PDF", "url": "https://meghanag.supporthive.com/kb/article/22-pdf/", "article_id": 22, "section_id": 2, "last_updated_at": "2025-06-16T09:01:30Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "dbfe0957-e0fe-4be1-9d78-1e89cb3fbd3b", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "PDF", "url": "https://meghanag.supporthive.com/kb/article/22-pdf/", "article_id": 22, "section_id": 2, "last_updated_at": "2025-06-16T09:01:30Z", "has_attachments": true, "attachment_count": 1}, "hash": "c221f98bf900ef40f44012a543dc67506611ee2b91ecc14f516565cd873755d4", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "attached documents test", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 23, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "5b23067d-24d0-4222-8c31-7175cb4e6729": {"__data__": {"id_": "5b23067d-24d0-4222-8c31-7175cb4e6729", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "42924160-e02f-459e-8d85-bf2d1130080e", "node_type": "1", "metadata": {}, "hash": "fb8f86f4f8ca25c0fdba6dec1d0510531aa8a46f657d6587a6ac102ac925f500", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "# 10AMP PWM SOLAR CONTROLLER\n\n## User Manual\n\nGP-PWM-10-FM (FLUSH MOUNT - LITHIUM COMPATIBLE)\n\n!Image[This image shows the front panel of the 10 AMP PWM Solar Controller (GP-PWM-10-FM) device. The controller has a black rectangular faceplate with a digital LCD display in the center. The display shows '0.0 V' and 'kAh', indicating it can show voltage and ampere-hour readings. There are icons for solar, battery, and load on the display, suggesting it monitors these aspects of a solar power system. Below the display are two buttons labeled A and B, likely for user interaction and settings adjustment. On the left side of the panel is a switch, possibly for power or mode selection. The Go Power! logo and website URL (gpelectric.com) are visible on the right side of the faceplate, branding the device.]\n\nWorldwide Technical Support and Product Information\n\n\n\n#\n\n!Image[This image displays two logos associated with the solar controller product. The top logo is circular and contains the text 'Go Power!' inside, which is the primary brand of the solar controller. Below it is the Dometic logo, featuring the word 'DOMETIC' in a stylized font. This suggests that Go Power! is either a subsidiary or partner brand of Dometic, a well-known company in the mobile living market space.]\n\n1.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1289, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "42924160-e02f-459e-8d85-bf2d1130080e": {"__data__": {"id_": "42924160-e02f-459e-8d85-bf2d1130080e", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "5b23067d-24d0-4222-8c31-7175cb4e6729", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "5bfa81a993f202f499f35f74d23997a103b69f643381c30c7fde49e60bce9fd2", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "0f5010dc-d1bb-43c5-ad9b-75ea3f0f45ee", "node_type": "1", "metadata": {}, "hash": "fbfa8610b5a00e572b7a9db59480a22763d11b6270d7a986b4f2145c1fc47755", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "This suggests that Go Power! is either a subsidiary or partner brand of Dometic, a well-known company in the mobile living market space.]\n\n1. INSTALLATION OVERVIEW..................................................................................................................... 4\n\n   1.1 INTRODUCTION ...............................................................................................................................4\n   1.2 SYSTEM VOLTAGE AND CURRENT.....................................................................................................4\n   1.3 BATTERY TYPE..................................................................................................................................4\n   1.4 LOW VOLTAGE DISCONNECT FUNCTION (USB PORT) .......................................................................4\n   1.5 REGULATORY INFORMATION ...........................................................................................................4\n   1.6 SPECIFICATIONS...............................................................................................................................4\n\n2. WARNINGS ............................................................................................................................................... 5\n\n3. TOOLS AND MATERIALS NEEDED ........................................................................................................ 6\n\n4. CHOOSING A LOCATION ........................................................................................................................ 6\n\n5. INSTALLATION INSTRUCTIONS ............................................................................................................. 6\n\n6. WIRING DIAGRAM .................................................................................................................................. 7\n\n7. OPERATING INSTRUCTIONS .................................................................................................................. 8\n\n   7.1 POWER UP......................................................................................................................................8\n   7.2 SETTING THE BATTERY CHARGING PROFILE .....................................................................................8\n   7.3 BATTERY CHARGING PROFILE CHART ...............................................................................................9\n   7.4 VIEWING THE CONTROLLER DISPLAY INFORMATION........................................................................9\n   7.5 RESETTING THE AMPERE HOURS CHARGED ...................................................................................11\n   7.6 ERRORS .........................................................................................................................................11\n\n8. DISPLAY SYMBOLS................................................................................................................................. 12\n\n9. USB CHARGING...................................................................................................................................... 13\n\n10. FREQUENTLY ASKED QUESTIONS (FAQS) .......................................................................................... 14\n\n11.", "mimetype": "text/plain", "start_char_idx": 1148, "end_char_idx": 4428, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "0f5010dc-d1bb-43c5-ad9b-75ea3f0f45ee": {"__data__": {"id_": "0f5010dc-d1bb-43c5-ad9b-75ea3f0f45ee", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "42924160-e02f-459e-8d85-bf2d1130080e", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "827f4e7ad86c18c0eac8b5c836b4a4fe143074a7b34aae4f3fa5e7a503b950e6", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "6cb1005f-f6ad-417a-92e3-ea55b48756b1", "node_type": "1", "metadata": {}, "hash": "87f7f19f4798444c3170e489936d1047f5ff16ec27028e3c566987799079b033", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "DISPLAY SYMBOLS................................................................................................................................. 12\n\n9. USB CHARGING...................................................................................................................................... 13\n\n10. FREQUENTLY ASKED QUESTIONS (FAQS) .......................................................................................... 14\n\n11. TROUBLESHOOTING PROBLEMS......................................................................................................... 14\n\n    11.1 PROBLEMS WITH THE DISPLAY......................................................................................................15\n    11.2 PROBLEMS WITH VOLTAGE ...........................................................................................................15\n    11.3 PROBLEMS WITH CURRENT...........................................................................................................16\n\n12. LIMITED WARRANTY ............................................................................................................................. 17\n\n    12.1 REPAIR AND RETURN INFORMATION.............................................................................................17\n\n13. INSTALLATION TEMPLATE .................................................................................................................... 17\n\n | [ 3]\n\n# 1.\n\n## 1.1 INTRODUCTION\n\nA Solar Controller (or Charge Controller / Regulator) is an essential component of your photovoltaic solar system. The Controller maintains the life of the battery by protecting it from overcharging. When your battery has reached a 100% state of charge, the Controller prevents overcharging by limiting the current flowing into the batteries from your solar array.\n\nThe GP-PWM-10-FM uses Pulse Width Modulation (PWM) technology and a unique four stage charging system that includes an optional equalize setting to charge and protect your battery bank. The GP-PWM-10-FM features an LCD digital display that shows the charge current of the solar array, battery voltage and battery state of charge.", "mimetype": "text/plain", "start_char_idx": 3989, "end_char_idx": 6148, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "6cb1005f-f6ad-417a-92e3-ea55b48756b1": {"__data__": {"id_": "6cb1005f-f6ad-417a-92e3-ea55b48756b1", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "0f5010dc-d1bb-43c5-ad9b-75ea3f0f45ee", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "e16523dd6c4075a247e2d821ba56a466f8b909ced561095aad025459f1215c43", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "7e2a1bcc-50f4-4bef-b09e-c40e8fc826c7", "node_type": "1", "metadata": {}, "hash": "8f0f95d34a6b67bfc390084d9edf5655a5555f140159c03e29a20cc38458a25d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "The GP-PWM-10-FM features an LCD digital display that shows the charge current of the solar array, battery voltage and battery state of charge.\n\n## 1.2 SYSTEM VOLTAGE AND CURRENT\n\nThe GP-PWM-10-FM is intended for use at 12 VDC nominal system voltage and is rated for a maximum continuous DC input current of 12.5A and input voltage of 35VDC.\n\nPer the National Electric Code (NEC) article 690.7 and 690.8, PV module nameplate ratings at Standard Test Conditions (STC) must be multiplied by required values (typically 1.25 for both voltage and current) to obtain the true voltage and continuous current available from the module.\n\nApplying the NEC factors, the maximum allowable nameplate PV Panel rated Isc is 10A (10A x 1.25 = 12.5A), and the maximum voltage, Voc is 28VDC (28VDC x 1.25 = 35VDC).\n\nThe voltage and current ratings of all equipment connected to PV panels must be capable of accepting the voltage and current levels available from PV panels installed in the field.\n\n## 1.3 BATTERY TYPE\n\nThe GP-PWM-10-FM is suitable for use with lead acid and lithium batteries (vented, GEL, LiFePO4 (LFP) or AGM type).\n\n## 1.4 LOW VOLTAGE DISCONNECT FUNCTION (USB PORT)\n\nTo protect the battery against over-discharge this function automatically switches off the USB output port when battery voltage is lower than 11.0 VDC. As soon as the battery reaches a voltage of 12.8 VDC the USB output port is switched on again.", "mimetype": "text/plain", "start_char_idx": 6005, "end_char_idx": 7420, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "7e2a1bcc-50f4-4bef-b09e-c40e8fc826c7": {"__data__": {"id_": "7e2a1bcc-50f4-4bef-b09e-c40e8fc826c7", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "6cb1005f-f6ad-417a-92e3-ea55b48756b1", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "69dc5ddd3666b3a290912ea035fa4270a217f79b6d5a4e72aabfd0c13af7dcb8", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "0e3a0854-1c0a-47c8-9ade-299b015b71ad", "node_type": "1", "metadata": {}, "hash": "5faf48b5997dd0ca87f021804ca521c546ac4c59bd49c86d3876ea1ef00833b1", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "As soon as the battery reaches a voltage of 12.8 VDC the USB output port is switched on again.\n\n## 1.5 REGULATORY INFORMATION\n\n!Image[This image shows two regulatory compliance symbols. The CE (Conformité Européenne) mark on the left indicates compliance with European Union standards. The RoHS (Restriction of Hazardous Substances) symbol on the right indicates compliance with the EU directive restricting the use of certain hazardous substances in electrical and electronic equipment. These symbols are relevant to the GP-PWM-10-FM Solar Controller discussed in the document, confirming its adherence to European safety and environmental standards.]\n\n[ 4] |\n\n#\n\n## 1.6 SPECIFICATIONS\n\n!Table[This comprehensive table provides detailed specifications for the GP-PWM-10-FM Solar Controller. Key specifications include: Nominal System Voltage of 12 VDC, Maximum Solar Continuous DC Charge Current Input of 12.5 ADC, Maximum Solar DC Input Voltage of 35 VDC, and support for various battery types including Lead Acid and Lithium. The controller features a 4-stage charging system with specific voltage levels for Bulk/Absorption (14.1/14.4/14.4VDC), Float (13.7V, 14.0V for LFP), and Equalization (14.9V). It includes temperature compensation, USB charging capability, and various protection features. The table also lists physical specifications such as dimensions (149 x 98 x 32 mm) and weight (260 g), as well as environmental operating conditions and warranty information.]\n\n | [ 5]\n\n# 2.", "mimetype": "text/plain", "start_char_idx": 7326, "end_char_idx": 8817, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "0e3a0854-1c0a-47c8-9ade-299b015b71ad": {"__data__": {"id_": "0e3a0854-1c0a-47c8-9ade-299b015b71ad", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "7e2a1bcc-50f4-4bef-b09e-c40e8fc826c7", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "ade4d99f768478a9b62c2caae1c769d96b1752831523144628513c40d7e14aa4", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "4ec32129-7c18-4b67-a31a-fd9b3350857a", "node_type": "1", "metadata": {}, "hash": "f7fb2764cbf0ec50e7c795d9b61da1590d8c6e7ef834d6aefa4e29069e5fc75e", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "The table also lists physical specifications such as dimensions (149 x 98 x 32 mm) and weight (260 g), as well as environmental operating conditions and warranty information.]\n\n | [ 5]\n\n# 2.\n\n!Image[The image shows the Dometic company logo, which is a stylized oval shape containing the text \"Go Power!\". This logo appears at the beginning of the warnings section, indicating that the safety information provided is associated with Dometic's Go Power! brand of solar controllers.]\n\n!Image[This image depicts a warning symbol showing a lightning bolt inside a triangle. It indicates an electrical hazard and is used to emphasize the importance of disconnecting all power sources before installation or maintenance of the solar controller.] **Disconnect all power sources**\n\nElectricity can be very dangerous. Installation should be performed only by a licensed electrician or qualified personnel.\n\n!Image[The image shows a warning symbol featuring a battery with a lightning bolt. This symbol is used to highlight the safety precautions related to battery handling and the potential dangers associated with hydrogen gas production during battery charging.] **Battery and wiring safety**\n\nObserve all safety precautions of the battery manufacturer when handling or working around batteries. When charging, batteries produce hydrogen gas, which is highly explosive.\n\n!Image[This warning symbol depicts a hand being struck by lightning, indicating the danger of electrical shock. It emphasizes the importance of proper wiring connections and the need to ensure all connections are tight and secure to prevent sparks and heat generation.] **Wiring connections**\n\nEnsure all connections are tight and secure. Loose connections may generate sparks and heat. Be sure to check connections one week after installation to ensure they are still tight.\n\n!Image[The image shows a safety symbol of a person wearing safety goggles.", "mimetype": "text/plain", "start_char_idx": 8627, "end_char_idx": 10542, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4ec32129-7c18-4b67-a31a-fd9b3350857a": {"__data__": {"id_": "4ec32129-7c18-4b67-a31a-fd9b3350857a", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "0e3a0854-1c0a-47c8-9ade-299b015b71ad", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "69ba0ef9d57acc62166ce6c9d1238a9590c1f5101cd852d3f6f72d179ba32309", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "743176a4-bdc9-43cf-adeb-4efa0348924d", "node_type": "1", "metadata": {}, "hash": "846991bca63e46872af0bf61ca4569c2ffb41608930dda7c4c8c9d49ad239041", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Loose connections may generate sparks and heat. Be sure to check connections one week after installation to ensure they are still tight.\n\n!Image[The image shows a safety symbol of a person wearing safety goggles. This icon emphasizes the need for eye protection during installation and when working with the solar controller system, as part of the overall safety precautions.] **Work safely**\n\nWear protective eye wear and appropriate clothing during installation. Use extreme caution when working with electricity and when handling and working around batteries. Use properly insulated tools only.\n\n!Image[This warning symbol shows an exclamation mark inside a triangle, indicating general caution or warning. In the context of the document, it's used to emphasize the importance of observing correct polarity at all times when connecting the solar controller.] **Observe correct polarity at all times**\n\nReverse polarity of the battery terminals will cause the controller to give a warning tone. Reverse connection of the array will not cause an alarm but the controller will not function. Failure to correct this fault could damage the controller.\n\n!Image[The image depicts a warning symbol showing flames, indicating a fire hazard or high temperature warning. In the context of the document, this symbol is used to emphasize the importance of not exceeding the GP-PWM-10-FM's amp current and max voltage ratings to prevent potential fire hazards.] **Do not exceed the GP-PWM-10-FM Amp current and max voltage ratings**\n\nThe maximum current of the solar system is the sum of parallel-connected PV module--rated short circuit Currents (Isc) multiplied by 1.25. The resulting system current is not to exceed 12.5A. If your solar system exceeds this value, contact your dealer for a suitable controller alternative.", "mimetype": "text/plain", "start_char_idx": 10330, "end_char_idx": 12144, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "743176a4-bdc9-43cf-adeb-4efa0348924d": {"__data__": {"id_": "743176a4-bdc9-43cf-adeb-4efa0348924d", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "4ec32129-7c18-4b67-a31a-fd9b3350857a", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "816c1bbd216981b344ca30f2bbef39b4c68413be9ac000adc41076b5f94033a3", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "56e8e050-b5db-4987-99ae-354c33c2e0dc", "node_type": "1", "metadata": {}, "hash": "735a87f750477fc7cefec55361243c4d3d862986d71a680b3f5a8f4d26716c59", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "The resulting system current is not to exceed 12.5A. If your solar system exceeds this value, contact your dealer for a suitable controller alternative.\n\n**Do not exceed the GP-PWM-10-FM max voltage ratings**\n\nThe maximum voltage of the array is the sum of the PV module--rated open-circuit voltage of the series connected modules multiplied by 1.25 (or by a value from NEC 690.7 provided in Table 690.7 A). The resulting voltage is not to exceed 35V. If your solar system exceeds this value, contact your dealer for a suitable controller alternative.\n\n[ 6] |\n\n# 3. TOOL AND MATERIALS NEEDED\n\n- Flathead Screwdriver (for wire terminals)\n- Philips Screwdriver (for mounting screws)\n\n> **Note**: If the GP-PWM-10-FM Controller was purchased with a Go Power! Solar Power Kit, then UV resistant wire is included. For instructions regarding the Go Power! Solar Power Kit installation, please refer to the Installation Guide provided with the Kit.\n\n# 4. CHOOSING A LOCATION\n\nThe GP-PWM-10-FM is designed to be mounted flush against a wall, out of the way but easily visible.\n\nThe GP-PWM-10-FM should be:\n\n- Mounted as close to the battery as possible\n- Mounted on a vertical surface to optimize cooling of the unit\n- Indoors, protected from the weather\n\nIn an RV, the most common controller location is above the refrigerator. The wire from the solar array most commonly enters the RV through the fridge vent on the roof or by using the Go Power! Cable Entry Plate (sold separately) that allows installers to run wires through any part of the roof.", "mimetype": "text/plain", "start_char_idx": 11992, "end_char_idx": 13534, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "56e8e050-b5db-4987-99ae-354c33c2e0dc": {"__data__": {"id_": "56e8e050-b5db-4987-99ae-354c33c2e0dc", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "743176a4-bdc9-43cf-adeb-4efa0348924d", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "ffd862ffa8c0a7c5260ddfb98c3dab4d0c6085945e640a0f3c8405c9a17b4c3e", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "89b1412d-99a5-45a2-a474-a69ff348ef3f", "node_type": "1", "metadata": {}, "hash": "2abab7d1ac03e6de0f2e0ebf75c4574b72d1d6bfc4e6b2166e0391303aabe3e1", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "The wire from the solar array most commonly enters the RV through the fridge vent on the roof or by using the Go Power! Cable Entry Plate (sold separately) that allows installers to run wires through any part of the roof. PV connections should connect directly to the controller. Positive and negative battery connections must connect directly from the controller to the batteries. Use of a positive or negative distribution bus is allowed between the controller and battery as long as it is properly sized, electrically safe and an adequate wire size is maintained.\n\n# 5. INSTALLATION INSTRUCTIONS\n\n1. Prepare for mounting. Use the template provided on  17 to mark the four mounting holes and the cutting line for flush mounting your controller.\n\n2. Complete the installation of the solar modules. If this GP-PWM-10-FM was purchased as part of a Go Power! Solar Power Kit, follow the Installation Guide provided. Otherwise, follow manufacturer's instructions for solar module mounting and wiring.\n\n3. Select wire type and gauge. If this GP-PWM-10-FM was purchased as part of a Go Power! Solar Power Kit, appropriate wire type, gauge, and length is provided. Please continue to Section 6, \"Operating Instructions.\" If the GP-PWM-10-FM was purchased separately, follow the instructions included here.\n\nWire type is recommended to be a stranded copper UV-resistant wire. Wire fatigue and the likelihood of a loose connection are greatly reduced in stranded wire compared to solid wire. Wire gauge should be able to sustain rated current and minimize voltage drop.\n\nWire Strip Length\nStrip wires to a length of approximately 3/8 in (9 mm, as per strip gauge).", "mimetype": "text/plain", "start_char_idx": 13313, "end_char_idx": 14969, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "89b1412d-99a5-45a2-a474-a69ff348ef3f": {"__data__": {"id_": "89b1412d-99a5-45a2-a474-a69ff348ef3f", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "56e8e050-b5db-4987-99ae-354c33c2e0dc", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "5ada284cbd1cd53e4e319e19198ff49c00c427869d99c0297914cdeb42ba0a9f", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "70a1e8e9-c497-4cca-9c77-b80b6db41029", "node_type": "1", "metadata": {}, "hash": "67b2997d9210b872e05e6d77f856b3b3f00442551a281975fcda0243332bdc6a", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Wire gauge should be able to sustain rated current and minimize voltage drop.\n\nWire Strip Length\nStrip wires to a length of approximately 3/8 in (9 mm, as per strip gauge).\n\nSuggested Minimum Wire Gauge\n(Cable length 25 ft. max. from solar array to battery bank)\n\n!Table[The table shows suggested minimum wire gauge for different solar module wattages, specifically for the GP-PWM-10-FM controller installation. It recommends #10 Wire Gauge for all listed solar module wattages: 80W, 100W, 160W, 170W, and 190W. This uniform recommendation simplifies wiring choices for various solar panel configurations up to 190W.](Suggested Minimum Wire Gauge)\n\nIMPORTANT: Identify the polarity (positive and negative) on the cable used for the battery and solar module. Use colored wires or mark the wire ends with tags. Although the GP-PWM-10-FM is protected, a reverse polarity contact may damage the unit.\n\nWiring the GP-PWM-10-FM. Wire the GP-PWM-10-FM according to the wiring schematic in Section 6. Run wires from the solar array and the batteries to the location of the GP-PWM-10-FM. Keep the solar array covered with an opaque material until all wiring is completed.\n\n# 6. WIRING DIAGRAMS\n\n**IMPORTANT**: All wiring must be in accordance to National Electrical Code, ANSI/NFPA 70. Always use appropriate circuit protection on any conductor attached to a battery.\n\n4. Connect the battery wiring to the controller first and then connect the battery wiring to the battery.\n\n5.", "mimetype": "text/plain", "start_char_idx": 14797, "end_char_idx": 16266, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "70a1e8e9-c497-4cca-9c77-b80b6db41029": {"__data__": {"id_": "70a1e8e9-c497-4cca-9c77-b80b6db41029", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "89b1412d-99a5-45a2-a474-a69ff348ef3f", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "7237ddc8bd32b7678ea148cc9db28f27344b98f2f5e2807367da8bc6e6d820c0", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "ab57d733-4bca-4192-b118-a112c10c180d", "node_type": "1", "metadata": {}, "hash": "97779db706f5461692c590459deba7305ce430c80202736a999a629d1d7e641e", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Always use appropriate circuit protection on any conductor attached to a battery.\n\n4. Connect the battery wiring to the controller first and then connect the battery wiring to the battery.\n\n5. Torque all terminal screws per the following:\n\n!Table[A table titled \"Stranded Copper 90°C Wire\" specifies the rated torque for different wire sizes used with the GP-PWM-10-FM controller. It shows that for 14 AWG, 12 AWG, and 10 AWG wire sizes, the rated torque is consistently 20 in-lbs. This information is critical for proper installation and secure connections.]\n\nWith battery power attached, the controller should power up and display information. Connect the solar wiring to the controller and remove the opaque material from the solar array. The negative solar array and battery wiring must be connected directly to the controller for proper operation. Do not connect the negative solar array or negative battery controller wiring to the chassis of the vehicle.\n\n6. Mounting the GP-PWM-10-FM. Mount the GP-PWM-10-FM to the wall using the included four mounting screws.\n\n**IMPORTANT**: You must set the battery type on the GP-PWM-10-FM before you begin to use the controller (follow steps in Section 7). The default battery setting is for AGM/LiFePO4 batteries.\n\nCongratulations, your GP-PWM-10-FM should now be operational. If the battery power is low and the solar array is producing power, your battery should begin to charge.\n\n7. Re-torque: After 30 days of operation, re-torque all terminal screws to ensure the wires are properly secured to the controller.", "mimetype": "text/plain", "start_char_idx": 16074, "end_char_idx": 17635, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "ab57d733-4bca-4192-b118-a112c10c180d": {"__data__": {"id_": "ab57d733-4bca-4192-b118-a112c10c180d", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "70a1e8e9-c497-4cca-9c77-b80b6db41029", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "987f6e8bba169fe798f23cb46e872caf436812131561e3cbfef4174d6c1cc2e3", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "0f81384d-7038-49c1-ac5a-a66638b6f261", "node_type": "1", "metadata": {}, "hash": "ac6ed531969fa71b5673cdca265009d77d634fe4beef14f78dfb521cf3b44404", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "7. Re-torque: After 30 days of operation, re-torque all terminal screws to ensure the wires are properly secured to the controller.\n\n!Image[A warning symbol (triangle with exclamation mark) followed by text that reads: \"WARNING: This unit is not provided with a GFDI device. This charge controller must be used with an external GFDI device as required by Article 690 of the National Electric Code for the installation location.\" This warning is important for the safe installation and operation of the GP-PWM-10-FM solar charge controller.]\n\n# 6. WIRING DIAGRAMS\n\n**IMPORTANT**: This diagram is valid only for version 1.5 and newer. Version 1.4 and older have different terminal locations.\n\nThe GP-PWM-10-FM Maximum 12.5A rating is based on a 10 amp total maximum short circuit current rating (Isc) from the parallel solar module nameplate ratings. The National Electric Code specifies the PV equipment/system rating to be 125% of the maximum Isc from the PV module nameplate ratings (1.25 times 10 = 12.5A). Use the wiring diagram (below) to connect your battery to the battery terminals on the solar controller. First, connect the battery to the controller, and then connect the solar panel to the controller\n\n**Note**: The fuse or breaker used should be no larger than 15 amps.\n\n**Note**: The controller will not work unless there is a battery connected to the battery terminals with at least 9V.", "mimetype": "text/plain", "start_char_idx": 17504, "end_char_idx": 18903, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "0f81384d-7038-49c1-ac5a-a66638b6f261": {"__data__": {"id_": "0f81384d-7038-49c1-ac5a-a66638b6f261", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "ab57d733-4bca-4192-b118-a112c10c180d", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "85fbe36ac1f7618e60ca725f5c52ec769462f0ad72cbe9fd19ba7026f8d91fed", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "f94d4348-5252-4fe8-8517-c54667f90df9", "node_type": "1", "metadata": {}, "hash": "75e4d62ae6f82c068783996c9f6580f55aaab423e956791eec1165006fee0a48", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "**Note**: The controller will not work unless there is a battery connected to the battery terminals with at least 9V.\n\n!Image[A warning symbol (triangle with exclamation mark) followed by text that reads: \"WARNING: When the photovoltaic (solar) array is exposed to light, it supplies a dc voltage to this equipment\". This warning highlights the constant voltage supply from solar panels when exposed to light, which is crucial for safe handling during installation and maintenance.]\n\n[ 8] |\n\n# 7. OPERATING INSTRUCTIONS\n\n!Wiring_Diagram[A wiring diagram illustrating the connections for the GP-PWM-10-FM solar charge controller. It shows a battery bank connected to the controller through a fuse (rated no larger than 15 amps), and a solar array directly connected to the controller. The controller has four clearly labeled terminals: BATTERY +, BATTERY -, SOLAR +, and SOLAR -. This diagram emphasizes the importance of correct wiring and the use of a fuse for battery protection.]\n\n## 7.1 SYSTEM VOLTAGE AND CURRENT\n\nWhen the GP-PWM-10-FM is connected to the battery, the controller will go into Power Up mode.\n\nIcons Displayed: All segments of the numerical display; backlight blinks. Depending on the battery voltage when the GP-PWM-10-FM Power Up occurs, the controller may do a Boost Charge or quickly go into Float Charge. The Charging Profile selected will commence the following day after a Power Up (refer to the Charging Profile Chart on  11 for more details).\n\n## 7.2 SETTING THE BATTERY CHARGING PROFILE\n\n!Device_Interface[A diagram showing the interface of the GP-PWM-10-FM solar controller.", "mimetype": "text/plain", "start_char_idx": 18786, "end_char_idx": 20391, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "f94d4348-5252-4fe8-8517-c54667f90df9": {"__data__": {"id_": "f94d4348-5252-4fe8-8517-c54667f90df9", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "0f81384d-7038-49c1-ac5a-a66638b6f261", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "8d37f1a165260bb300e70eb5e9263b7b05543d338d0f979362325c60ff7a8904", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "527a8230-8908-462e-a7be-2725a631c9aa", "node_type": "1", "metadata": {}, "hash": "c3a50ec810d6c766e432590e981abab39331bebe4cfc691148a00b0aa36a2a4c", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "## 7.2 SETTING THE BATTERY CHARGING PROFILE\n\n!Device_Interface[A diagram showing the interface of the GP-PWM-10-FM solar controller. It displays a screen with \"13.8v\" indicating the current voltage reading. Below the screen are two buttons labeled A and B, used for navigating the controller's settings. The device is identified as a \"10 AMP PWM SOLAR CONTROLLER\" at the top. A USB port icon is also visible, suggesting data connectivity or charging capabilities. The Go Power! logo is prominently displayed, branding the device.]\n\nTo select the battery charging profile, press and hold the B Button. This will cause the current battery type to flash.\n\nThen, press the B Button to toggle through the profile options: Sealed/ Gel, AGM/LiFePO4 or Flooded.\n\nTo confirm the battery profile, press and hold the A Button for 3 seconds.\n\nNon-volatile memory: Any settings made on the GP-PWM-10-FM will be saved even when the power has been disconnected from the controller.\n\nRefer to the Battery Charge Profile Chart below for details on each profile.\n\n## 7.2 SETTING THE BATTERY CHARGING PROFILE\n\n | [ 9]\n\n#\n\n## 7.3 BATTERY CHARGING PROFILE CHART\n\n!Table[This table presents battery charging profiles for different battery types: SEALED/GEL, AGM, FLOODED, and LFP (LiFePO4). It provides voltage settings for Float Charge, Bulk/Absorption Charge, and Equalization Charge at 25°C. The table helps users select the appropriate charging profile based on their battery manufacturer's recommendations.", "mimetype": "text/plain", "start_char_idx": 20259, "end_char_idx": 21748, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "527a8230-8908-462e-a7be-2725a631c9aa": {"__data__": {"id_": "527a8230-8908-462e-a7be-2725a631c9aa", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "f94d4348-5252-4fe8-8517-c54667f90df9", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "6e0da5200e4d7c0f9aa16ccb8075c1672a523c2d3db384ee738b85b811bb6753", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "8737ca0c-2fa6-4c0e-bc29-33eb09025c5c", "node_type": "1", "metadata": {}, "hash": "823f4f896a0f67f21891a275744d40b75801bede86240a56a17859f870f0f6bf", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "It provides voltage settings for Float Charge, Bulk/Absorption Charge, and Equalization Charge at 25°C. The table helps users select the appropriate charging profile based on their battery manufacturer's recommendations. It's part of the GP-PWM-10-FM solar controller's operating instructions, specifically under section 7.3 BATTERY CHARGING PROFILE CHART.]\n\nIf a charging cycle is unable to complete in a single day, it will continue the following day. The terms SEALED/GEL, AGM, FLOODED and LFP are generic battery designations. Choose the charging profile that works best with your battery manufacturer's recommendations.\n\n> **Note**: If PV power is insufficient or too many loads are drawing power from the battery, the controller will not be able to charge the battery to the target charging voltage.\n\nAuto Equalize: The GP-PWM-10-FM has an automatic equalize feature that will charge and recondition your batteries at least once a month at a higher voltage to ensure that any excess sulfation is removed.\n\n> **Note**: This mode should not be entered unless you are using a flooded battery.\n\n## 7.4 VIEWING THE CONTROLLER DISPLAY INFORMATION\n\nTo toggle between Battery Voltage, PV Charging Current, Battery State of Charge (SOC), and ampere hours charged since last reset, press the B Button.\n\n!Diagram[This diagram shows the display of a 10 AMP PWM SOLAR CONTROLLER, specifically the GP-PWM-10-FM model. The display indicates a battery voltage of 13.8V. It illustrates how to use the B Button to toggle between different display modes, starting with the battery voltage. This is part of a series of diagrams demonstrating the controller's display information viewing process.]", "mimetype": "text/plain", "start_char_idx": 21528, "end_char_idx": 23210, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8737ca0c-2fa6-4c0e-bc29-33eb09025c5c": {"__data__": {"id_": "8737ca0c-2fa6-4c0e-bc29-33eb09025c5c", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "527a8230-8908-462e-a7be-2725a631c9aa", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "d736846cfc7c88c1823560fad14672ebe1e591590223935a293d787e60967d45", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "4c0e3361-57e3-41c1-a26d-bfa6086a99f9", "node_type": "1", "metadata": {}, "hash": "316fc3c983d55f9bad138045384a33e5965d3e338788f9e926a7294eb14bd549", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "It illustrates how to use the B Button to toggle between different display modes, starting with the battery voltage. This is part of a series of diagrams demonstrating the controller's display information viewing process.]\n\n[ 10] |\n\n#\n\n!Diagram[This diagram depicts the 10 AMP PWM SOLAR CONTROLLER display showing the PV charging current of 7.6A. It's the second in the series demonstrating how to use the B Button to cycle through display modes. The diagram emphasizes the ampere symbol (A) and the battery state of charge icon.]\n\nPush the B Button to show the PV charging current.\n\nIcons Displayed: Ampere Symbol (A), Battery SOC\n\n!Diagram[This diagram shows the 10 AMP PWM SOLAR CONTROLLER display indicating a 90% battery state of charge. It's the third in the series demonstrating the use of the B Button to cycle through display modes. The percent symbol (%) is highlighted, and the diagram notes that 100% will only be displayed after a Boost or Equalize charge completes.]\n\nPush the B Button to show the battery state of charge (shown as a percentage).\n\nIcons Displayed: Battery SOC, Percent Symbol (%)\n\nA value of 100% will only be displayed after a Boost or Equalize charge completes.\n\n!Diagram[This diagram illustrates the 10 AMP PWM SOLAR CONTROLLER display showing 45.3 ampere hours (Ah) charged since the last reset. It's the fourth and final diagram in the series demonstrating how to use the B Button to cycle through display modes. The diagram emphasizes the Ah symbol and mentions that kAh (kiloamp hours) may also be displayed for larger values.]\n\nPush the B Button to show the number of amp hours charged since the last reset.", "mimetype": "text/plain", "start_char_idx": 22988, "end_char_idx": 24634, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4c0e3361-57e3-41c1-a26d-bfa6086a99f9": {"__data__": {"id_": "4c0e3361-57e3-41c1-a26d-bfa6086a99f9", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "8737ca0c-2fa6-4c0e-bc29-33eb09025c5c", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "1e1587c4faf131719b3ef8a4467924868714ad2c339a9c292b60295c70337f08", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "7bf10cf5-8409-403f-b1c2-83be3841c896", "node_type": "1", "metadata": {}, "hash": "6f7c952a5f4ec48fd7bf499b32299433fc4f94dab30273c0e5df30afd1f90b9f", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "The diagram emphasizes the Ah symbol and mentions that kAh (kiloamp hours) may also be displayed for larger values.]\n\nPush the B Button to show the number of amp hours charged since the last reset.\n\nIcons Displayed: Amp hours charged, Amp hour symbol (Ah) or kiloamp hour symbol (kAh)\n\n | [ 11]\n\n#\n\n## 7.5 RESETTING THE AMPERE HOURS CHARGED\n\n!Diagram[This diagram shows the 10 AMP PWM SOLAR CONTROLLER display after resetting the ampere hours charged counter to 0.0 Ah. It illustrates the process of resetting the counter by pressing and holding the A Button for 6 seconds while in the ampere hours charged display mode.]\n\nTo reset the count of ampere hours charged, toggle to the ampere hours charged. Press and hold the A Button for 6 seconds to reset the counter to zero.\n\n## 7.6 ERRORS\n\n### OVER VOLTAGE\n\n!Diagram[This diagram depicts the 10 AMP PWM SOLAR CONTROLLER display during an over voltage error condition. All icons on the display are lit up, and the screen shows '8.8.8 kAh' to indicate that all segments are active. This visual representation corresponds to the controller's behavior when battery voltage exceeds 15.5V, at which point it stops operating until the error is cleared.]\n\nIf the GP-PWM-10-FM experiences a battery over voltage (15.5V), the controller will stop operating, and the display will begin to flash with all icons. The controller will resume operating when the error is cleared.", "mimetype": "text/plain", "start_char_idx": 24437, "end_char_idx": 25851, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "7bf10cf5-8409-403f-b1c2-83be3841c896": {"__data__": {"id_": "7bf10cf5-8409-403f-b1c2-83be3841c896", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "4c0e3361-57e3-41c1-a26d-bfa6086a99f9", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9903ab6763660d4728c0851b72fcdfb45b8382f4f6e36227a984edf1473d6641", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "4afb02cc-35fe-4f37-bfc4-26a32bee0ef0", "node_type": "1", "metadata": {}, "hash": "c4f86d18d97edb71957559194e5e41509e5d22cd8baa621cf8478ad894123474", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "If the GP-PWM-10-FM experiences a battery over voltage (15.5V), the controller will stop operating, and the display will begin to flash with all icons. The controller will resume operating when the error is cleared.\n\nIcons Displayed: All symbols\n\n### LOW VOLTAGE\n\n!Diagram[This diagram shows the 10 AMP PWM SOLAR CONTROLLER display during a low voltage condition. The screen displays a battery icon with the text 'LOW' beneath it, indicating that the battery voltage has reached 11 volts. This visual corresponds to the controller's low voltage warning, which occurs before the controller stops operating at voltages below 9 volts.]\n\nIf the battery voltage reaches 11 volts, the battery SOC symbol will show the text \"LOW\" beneath it. The controller will continue operating in this condition and will only stop operating if the voltage drops below 9 volts.\n\nIcons Displayed: Battery SOC Symbol, LOW\n\n[ 12] |\n\n# 8.\n\n## SYMBOLS\n\n!Table[A comprehensive table showing display symbols and their corresponding indicators for the GP-PWM-10-FM solar charge controller. It includes symbols for Day Time: PV Charge Current (sun icon), Night Time (moon icon), Battery Voltage (battery icon with 'V'), Battery State of Charge (battery icon with percentage), and battery types: Sealed/Gel, AGM/LFP, and Flooded. Each symbol is represented by an icon in the left column, with its meaning in the right column, providing users with a quick reference for interpreting the controller's display.]\n\n## OTHER SYMBOLS\n\n!Table[A table showing additional symbols and their meanings for the GP-PWM-10-FM controller.", "mimetype": "text/plain", "start_char_idx": 25636, "end_char_idx": 27226, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4afb02cc-35fe-4f37-bfc4-26a32bee0ef0": {"__data__": {"id_": "4afb02cc-35fe-4f37-bfc4-26a32bee0ef0", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "7bf10cf5-8409-403f-b1c2-83be3841c896", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "94d666c047687ad93e26484a8836ad782a4ec082f0862f47f4ad70f7b3235bdd", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "fa4feccb-61a5-41cb-a482-c72e69e54856", "node_type": "1", "metadata": {}, "hash": "057bd99e2955aac50ca9cd29cb1f13d66d92f7667f440afb9c51471b6668cced", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "## OTHER SYMBOLS\n\n!Table[A table showing additional symbols and their meanings for the GP-PWM-10-FM controller. It includes a USB symbol indicating 'USB charger ON (when charger is OFF, no symbol will show)', 'LOW' text indicating 'Battery voltage is lower than 11.0V', and 'Whole display will start to blink' indicating 'Battery voltage > 15.5V'. These symbols provide important status information about USB charging and battery voltage conditions.]\n\n | [ 13]\n\n#\n\n## BATTERY STATE OF CHARGE\n\n!Table[A detailed table titled 'BATTERY STATE OF CHARGE' for the GP-PWM-10-FM controller. It has two columns: 'SYMBOLS' and 'BATTERY VOLTAGE'. The SYMBOLS column shows 5 battery icons with varying levels of charge, from full to empty. The BATTERY VOLTAGE column provides corresponding voltage ranges or conditions for each symbol, such as '>13.0V' for a full battery and '<11.5V' for an empty battery. Additionally, it includes percentage indicators (100%, 90%, and 0%) with their respective voltage conditions. The table also provides an equation for calculating State of Charge (SOC): SOC(%) = (Battery Voltage - 11.0V) x 100 / (13.0V - 11.0V).]\n\n## USB CHARGING\n\nThe GP-PWM-10-FM offers a standard USB connector for delivering 5.0 VDC to small mobile appliances such as cell phones, tablets or small music players. This charging port is capable of supplying up to 1500 mA of current.\n\nRemove the rubber cover of the USB terminal to access the terminal.", "mimetype": "text/plain", "start_char_idx": 27115, "end_char_idx": 28563, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "fa4feccb-61a5-41cb-a482-c72e69e54856": {"__data__": {"id_": "fa4feccb-61a5-41cb-a482-c72e69e54856", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "4afb02cc-35fe-4f37-bfc4-26a32bee0ef0", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "d5558bafa118d95349c0326191e9fe8929bc314725d4023e90fc81ec36f47575", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "af05490a-bca2-4b79-be69-a515bdcf4d83", "node_type": "1", "metadata": {}, "hash": "2cb6be6798d9e54ccfc4607f6144a089b6ad7523a20b87339cb15e80f4abd279", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "This charging port is capable of supplying up to 1500 mA of current.\n\nRemove the rubber cover of the USB terminal to access the terminal.\n\nThe USB charging port is always active when the USB symbol appears on the display.\n\nThe controller disables the USB charger automatically if the battery voltage drops below 11.0 VDC. If there is enough current from the PV panel/array available to charge the Battery to above 12.8 VDC, the USB terminal will be enabled again.\n\n!Image[A warning symbol (yellow triangle with exclamation mark) indicating a cautionary message about USB charging connections. This symbol is used to emphasize the importance of proper USB device connection to prevent damage or malfunction.] WARNING: Do not connect the charging device anywhere else! USB-Negative contact is connected to battery negative.\n\n[ 14] |\n\n# 10. FREQUENTLY ASKED QUESTIONS\n\nBefore a problem is suspected with the system, read this section. There are numerous events that may appear as problems but are in fact perfectly normal. Please visit  for the most up-to-date FAQs.\n\n**It seems like my flooded batteries are losing water over time.**\n\nFlooded batteries may need to have distilled water added periodically to replace fluid loss during charging. Excessive water loss during a short period of time indicates the possibility of overcharging or aging batteries.\n\n**When charging, my flooded batteries are emitting gas.**\n\nDuring charging, hydrogen gas is generated within the battery. The gas bubbles stir the battery acid, allowing it to receive a fuller state of charge.\nImportant: Ensure batteries are in a well-ventilated space.\n\n**My voltmeter shows a different reading than the GP-PWM-10-FM display.", "mimetype": "text/plain", "start_char_idx": 28426, "end_char_idx": 30124, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "af05490a-bca2-4b79-be69-a515bdcf4d83": {"__data__": {"id_": "af05490a-bca2-4b79-be69-a515bdcf4d83", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "fa4feccb-61a5-41cb-a482-c72e69e54856", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "18ba13030d48db1a0c2b36fdd8f3144e0a1a9f7ab8597e862bb73194a7a58be1", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "99103616-545e-4890-9e77-2a033aebdc6b", "node_type": "1", "metadata": {}, "hash": "3472d72599e4920f58774cd5fef615f971a900c41d430076d332f658eee1fde9", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Important: Ensure batteries are in a well-ventilated space.\n\n**My voltmeter shows a different reading than the GP-PWM-10-FM display.**\n\nThe meter value on the GP-PWM-10-FM display is an approximate reading intended for indication purposes only. There is an approximate 0.1 volt inherent error present that may be accentuated when compared with readings from another voltmeter.\n\nThere may be a slight difference between the battery voltage displayed on the GP-PWM-10-FM display and the battery voltage measured at the battery terminals. When troubleshooting using a voltmeter, check both the battery voltage at the GP-PWM-10-FM controller terminals and battery voltage at the battery terminals. If a difference of more than 0.5 volts is noted, this indicates a large voltage drop possibly caused by loose connections, long wire runs, small wire gauge, faulty wiring, a faulty voltmeter, or all the above. Consult the Suggested Minimum Wire Gauge chart in Section 5 for wiring suggestions and check all connections.\n\n**What causes a warning signal and when are the warnings triggered?**\n\n!Table[A table in the FAQ section detailing warning signals for the GP-PWM-10-FM controller. It has four columns: CONNECTION, WARNING, NOTES, and LCD. The table shows warning signals for battery and PV reverse polarity. For both cases, the WARNING is 'POL' displayed on the LCD and a constant audible alarm. For PV reverse polarity, there's a NOTE that the battery must be connected with correct polarity for the warning to function. The LCD column confirms 'POL' display for both cases. This table provides important troubleshooting information for users experiencing connection issues.]", "mimetype": "text/plain", "start_char_idx": 29992, "end_char_idx": 31666, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "99103616-545e-4890-9e77-2a033aebdc6b": {"__data__": {"id_": "99103616-545e-4890-9e77-2a033aebdc6b", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "af05490a-bca2-4b79-be69-a515bdcf4d83", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "08d04f1abd1e0dd027f02cee8e73be16e9bd5b6810f6029366914cb718e001bf", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "a950df32-e82e-47b6-a3b5-822129b40abd", "node_type": "1", "metadata": {}, "hash": "75fa7e11759337b4d44882c738a9ba44d8d3e3e11fbb6a98fbe891b6b8089922", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "For PV reverse polarity, there's a NOTE that the battery must be connected with correct polarity for the warning to function. The LCD column confirms 'POL' display for both cases. This table provides important troubleshooting information for users experiencing connection issues.]\n\n**Why does the battery SOC% never reach 100%?**\n\nA 100% value will only appear after a 2 hour Boost or Equalize charge has completed. The charge voltage must be maintained for an extended period of time to replenish the energy in the battery bank back to its rated capacity.\n\nIf the charge voltage cannot be maintained continuously, then the actual time it takes to complete Boost or Equalize charging may take much longer than 2 hours, even more than 1 day.\n\nIf loads are consuming more power than the solar panels can supply, then the battery bank cannot be charged to 100%.\n\n!Image[Logo of Go Power!, a company associated with solar power and battery systems. The logo appears at the bottom of page 15, suggesting it's the manufacturer of the GP-PWM-10-FM solar charge controller discussed in the document.]\n!Image[Logo of Dometic, another company likely associated with power systems or RV/marine equipment. The logo appears alongside the Go Power! logo, suggesting a partnership or association between the two companies in relation to the GP-PWM-10-FM product.]\n\n | [ 15]\n\n# 11.\n\n## How to Read this Section\n\n is split into three sub-sections, grouped by symptoms involving key components. Components considered irrelevant in a diagnosis are denoted 'Not Applicable' (N/A). A multimeter or voltmeter may be required for some procedures listed.", "mimetype": "text/plain", "start_char_idx": 31386, "end_char_idx": 33016, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "a950df32-e82e-47b6-a3b5-822129b40abd": {"__data__": {"id_": "a950df32-e82e-47b6-a3b5-822129b40abd", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "99103616-545e-4890-9e77-2a033aebdc6b", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "18125259d646d4ff9da81e99c926aeaa9ac7bf4a0379b0fcb19b16e5e4ac78c5", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "ef91e41c-2978-4f08-a067-401bb98200eb", "node_type": "1", "metadata": {}, "hash": "c96507f4e145dfa3ccea2bea6682fc9e8df720ddcc30b0821823e1ac0505e0ad", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Components considered irrelevant in a diagnosis are denoted 'Not Applicable' (N/A). A multimeter or voltmeter may be required for some procedures listed.\n\n> It is imperative all electrical precautions stated in the Warning Section and outlined in the Installation Section are followed. Even if it appears the system is not functioning, it should be treated as a fully functioning system generating live power.\n\n## 11.1 ERRORS\n\n### Display Reading: Blank\nTime of Day: Daytime/Nighttime\n\n**Possible Causes:**\nBattery or fuse connection and/or solar array connection\n(Daytime only) or battery or fuse connection (Nighttime only).\n\n**How to tell:**\n1. Check the voltage at the controller battery terminals with a voltmeter and compare with a voltage reading at the battery terminals.\n2. If there is no voltage reading at the controller battery terminals, the problem is in the wiring between the battery and the controller. If the battery voltage is lower than 6 volts the controller will not function.\n3. For the solar array, repeat steps 1 and 2 substituting all battery terminals with solar array terminals.\n\n**Remedy:**\nCheck all connections from the controller to the battery including checking for correct wire polarity. Check that all connections are clean, tight, and secure. Ensure the battery voltage is above 6 volts.\n\n### Display Reading: Nighttime\nTime of Day: Daytime\n\n**Possible Causes:**\nPanel is covered by something; PV panel is too dirty to supply a high enough voltage to charge the battery; PV panel is not connected.\n\n**Remedy:**\nCheck the panel and to ensure it is not obscured. Clean the panel if it is dirty. Check that PV cables are connected to the controller.", "mimetype": "text/plain", "start_char_idx": 32863, "end_char_idx": 34546, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "ef91e41c-2978-4f08-a067-401bb98200eb": {"__data__": {"id_": "ef91e41c-2978-4f08-a067-401bb98200eb", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "a950df32-e82e-47b6-a3b5-822129b40abd", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "2996433c3e697e2f89095750aaa861d2904ec85b7962227374f8ff8ed4e8aa2e", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "8272c857-048a-447b-91b3-a7cb20b00acd", "node_type": "1", "metadata": {}, "hash": "edf125fe2bb92201b97094f486cfef1cca3f8498a8e111994d65528e484f24ea", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "**Remedy:**\nCheck the panel and to ensure it is not obscured. Clean the panel if it is dirty. Check that PV cables are connected to the controller.\n\n## 11.2 PROBLEMS WITH VOLTAGE\n\n### Voltage Reading: Inaccurate\nTime of Day: Daytime/Nighttime\n\n**Possible Causes:**\nExcessive voltage drop from batteries to controller due to loose connections, small wire gauge or both.\n\n**How to tell:**\n1. Check the voltage at the controller battery terminals with a voltmeter and compare with the voltage reading at the battery terminals.\n2. If there is a voltage discrepancy of more than 0.5 V, there is an excessive voltage drop.\n\n**Remedy:**\nCheck all connections from the controller to the battery including checking for correct wire polarity. Check that all connections are clean, tight, and secure. Shorten the distance from the controller to battery or obtain larger gauge wire. It is also possible to double up the existing gauge wire (i.e. two wire runs) to simulate a larger gauge wire.\n\n!Image[The image shows the Dometic company logo, which appears at the top of the page. This logo is consistently present across all three pages of the troubleshooting section, indicating it's likely a header element. The logo consists of the stylized word 'DOMETIC' in capital letters, with a curved line above it and the text 'Go Power!' inside an oval shape.]\n\n#\n\n## 11.3 PROBLEMS WITH CURRENT\n\n### Current Reading: 0 A\nTime of Day: Daytime, clear sunny skies\n\n**Possible Cause:**\nCurrent is being limited below 1 Amp as per normal operation or poor connection between solar array and controller.", "mimetype": "text/plain", "start_char_idx": 34399, "end_char_idx": 35980, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8272c857-048a-447b-91b3-a7cb20b00acd": {"__data__": {"id_": "8272c857-048a-447b-91b3-a7cb20b00acd", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "ef91e41c-2978-4f08-a067-401bb98200eb", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "fe86fb0771ef0410920686bf23d5d472bab5896247efd5aae0d38090c33981e3", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "530c03ec-ed23-449d-b409-1fd5c2ddfd0d", "node_type": "1", "metadata": {}, "hash": "4010f851a210eb722e50b06e23c5a5610b575b58f141cbeabfe788e374732247", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "**How to tell:**\n1. The State of Charge (SOC) screen is close to 100% and the Sun and Battery icon are present with an arrow between.\n2. With the solar array in sunlight, check the voltage at the controller solar array terminals with a voltmeter.\n3. If there is no reading at the controller solar array terminals, the problem is somewhere in the wiring from the solar array to the controller.\n\n**Remedy:**\nCheck all connections from the controller to the array including checking for correct wire polarity. Check that all connections are clean, tight, and secure. Continue with the solutions below for additional help on low current readings.\n\n### Current Reading: Less than expected\nTime of Day: Daytime, clear sunny skies\n\n**Possible Causes:**\n1. Current is being limited below 1 Amp as per normal operation.\n2. Incorrect series/parallel configuration and/or wiring connections and/or wire gauge.\n3. Dirty or shaded module or lack of sun.\n4. Blown diode in solar module when two or more modules are connected in parallel.\n\n**How to tell:**\n1. Battery State of Charge screen is close to 100% and the Sun and Battery icon are present with an arrow in between.\n2. Check that the modules and batteries are configured correctly. Check all wiring connections.\n3. Modules look dirty, overhead object is shading modules or it is an overcast day in which a shadow cannot be cast.\n\n> **Note:** Avoid any shading no matter how small. An object as small as a broomstick held across the solar module may cause the power output to be reduced. Overcast days may also cut the power output of the module\n\n4.", "mimetype": "text/plain", "start_char_idx": 35982, "end_char_idx": 37574, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "530c03ec-ed23-449d-b409-1fd5c2ddfd0d": {"__data__": {"id_": "530c03ec-ed23-449d-b409-1fd5c2ddfd0d", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "8272c857-048a-447b-91b3-a7cb20b00acd", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "a87c3cb40c5578c44076c584cc3561e9a6cb013b409db5a481cd2790f95f8954", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "4653dbba-baf0-4169-8695-cb9322b901f7", "node_type": "1", "metadata": {}, "hash": "4413264b0ae5cc3372a5b79c5c22b428388a32d1b4f0469bb2de22d2a528e5e7", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "An object as small as a broomstick held across the solar module may cause the power output to be reduced. Overcast days may also cut the power output of the module\n\n4. Disconnect one or both array wires from the controller. Take a voltage reading between the positive and negative array wire. A single 12 volt module should have an open circuit voltage between 17 and 22 volts. If you have more than one solar module, you will need to conduct this test between the positive and negative terminals of each module junction box with either the positive or the negative wires disconnected from the terminal.\n\n**Remedy:**\n1. Reconnect in correct configuration. Tighten all connections. Check wire gauge and length of wire run. Refer to Suggested Minimum Wire Gauge in Section 5.\n\n2. Clean modules, clear obstruction or wait for conditions to clear.\n\n3. If the open circuit voltage of a non-connected 12 volt module is lower than the manufacturer's specifications, the module may be faulty. Check for blown diodes in the solar module junction box, which may be shorting the power output of module.\n\n!Image[The image displays the Dometic logo in the top right corner of the page. The logo design is consistent with the previous page, featuring the stylized word 'DOMETIC' with a curved line above it and the text 'Go Power!' inside an oval shape. This logo serves as a header element for the troubleshooting section.]\n\n#\n\n## 11.4 CONTROLLER FLASHING\n\n**Possible Cause:**\nThis behavior is usually the controller dealing with a very high C or voltage rate (Above 15.5 volts).", "mimetype": "text/plain", "start_char_idx": 37407, "end_char_idx": 38973, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4653dbba-baf0-4169-8695-cb9322b901f7": {"__data__": {"id_": "4653dbba-baf0-4169-8695-cb9322b901f7", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "530c03ec-ed23-449d-b409-1fd5c2ddfd0d", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "da28631130a3a6513f7e2e001c5e62040e233f008c7cce77c43d7d059a871fd0", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "36876a93-2937-4094-a829-f4c9e50a068d", "node_type": "1", "metadata": {}, "hash": "b78b9f952eec477f033fa5bc241c200a01c51d919d9da94e05885c32da1fea67", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "#\n\n## 11.4 CONTROLLER FLASHING\n\n**Possible Cause:**\nThis behavior is usually the controller dealing with a very high C or voltage rate (Above 15.5 volts). Even though the controller can handle up to 30A, if the battery capacity is too small for the panel input current. The voltage shoots up too high, too quickly, tripping the high voltage flashing. Solution increase battery capacity.\n\nCan also be caused by an unregulated converter or alternator, in the system that is putting current to the batteries at the same time.\n\n**Remedy:**\nThe solution here is to unplug shore power and reset the controller, which can be done in two ways:\n\n1. **Soft Reset** - This is done by holding down all 4 buttons on the front of the controller for 15 seconds. If this does not work or you do not have a 4-button controller, a hard reset is required.\n1. **Hard Reset** - Remove all 4 wires from the back of the controller for 15-20 minutes, then reconnect the wires. Determine if this clears the error state.\n\nIf the problem was \"fixed,\" then it was because the user started using loads which divert some of the input current because the panels became dusty or shaded, or because there was less sunlight.\n\n!Image[The image shows the Dometic company logo, which is identical to the logos on the previous two pages. It consists of the stylized word 'DOMETIC' in capital letters, with a curved oval shape above containing the text 'Go Power!'. The logo is positioned at the top of the page, serving as a header element for the troubleshooting section.]\n\n# 12. LIMITED WARRANTY\n\nGo Power!", "mimetype": "text/plain", "start_char_idx": 38819, "end_char_idx": 40389, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "36876a93-2937-4094-a829-f4c9e50a068d": {"__data__": {"id_": "36876a93-2937-4094-a829-f4c9e50a068d", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "4653dbba-baf0-4169-8695-cb9322b901f7", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "99b37d651cef36e8a5050628fee9402075204d51962bc4106d830a4a69cd7b9f", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "d352348a-364a-4e5e-a2cb-c44d7580d6f0", "node_type": "1", "metadata": {}, "hash": "f14c9d68595fae53fbda09ddd291b7ffea0c64627e3134994b0b9d14a4077050", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "The logo is positioned at the top of the page, serving as a header element for the troubleshooting section.]\n\n# 12. LIMITED WARRANTY\n\nGo Power! warrants the GP-PWM-10 for a period of five (5) years from the date of shipment from its factory. This warranty is valid against defects in materials and workmanship for the five(5) year warranty period. It is not valid against defects resulting from, but not limited to:\n\n- Misuse and/or abuse, neglect or accident\n- Exceeding the unit's design limits\n- Improper installation, including, but not limited to, improper environmental protection and improper hook-up\n- Acts of God, including lightning, floods, earthquakes, fire, and high winds\n- Damage in handling, including damage encountered during shipment\n\nThis warranty shall be considered void if the warranted product is in any way opened or altered. The warranty will be void if any eyelet, rivets, or other fasteners used to seal the unit are removed or altered, or if the unit's serial number is in any way removed, altered, replaced, defaced, or rendered illegible.\n\n## 12.1 REPAIR AND RETURN INFORMATION\n\nVisit www. to read the \"frequently asked questions\" section of our website to troubleshoot the problem. If trouble persists:\n\n1. Fill out our online Contact Us form or Live Chat with us\n2. Email techsupport@\n3. Return defective product to place of purchase\n\n!Image[The image shows the logo of Go Power!, the manufacturer of the GP-PWM-10 product described in the warranty section. The logo appears at the bottom of the warranty information page, indicating its significance as the company brand.]", "mimetype": "text/plain", "start_char_idx": 40246, "end_char_idx": 41852, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "d352348a-364a-4e5e-a2cb-c44d7580d6f0": {"__data__": {"id_": "d352348a-364a-4e5e-a2cb-c44d7580d6f0", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "acfd3404-e36c-4893-8ea1-29e78e2fa37f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "9a1e75180a0a10845708dcc2f446fd775296edab18edf13dbccd22afcef2302f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "36876a93-2937-4094-a829-f4c9e50a068d", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "d22611244d1e8534d80994efc8329f524bc1993b849bf005eac69a0dd1e0688a", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "The logo appears at the bottom of the warranty information page, indicating its significance as the company brand.]\n!Image[The image displays the logo of Dometic, which is associated with Go Power! as indicated by their joint appearance in the document footer. This suggests a partnership or ownership relationship between the two companies.]\n\n | [page 19]\n\n!Image[This image combines the logos of Go Power! and Dometic side by side. The Go Power! logo on the left features a stylized 'GP' in a circular design, while the Dometic logo on the right displays 'DOMETIC' in capital letters with a distinctive arrow-like symbol above it. This dual branding appears at the bottom of the document, serving as an official seal or endorsement.]\n\n© 2021 Go Power!\n\nWorldwide Technical Support and Product Information gpelectric.com\nGo Power! | Dometic\n201-710 Redbrick Street Victoria, BC, V8T 5J3\nTel: **************\n\nManual_GP-PWM-10-FM", "mimetype": "text/plain", "start_char_idx": 41737, "end_char_idx": 42665, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "828a63b2-6f36-4328-9adb-c4ebb5bce407": {"__data__": {"id_": "828a63b2-6f36-4328-9adb-c4ebb5bce407", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample article", "url": "https://meghanag.supporthive.com/kb/article/12-sample-article/", "article_id": 12, "section_id": 2, "last_updated_at": "2025-04-22T05:44:42Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "01833d1b-bcae-4be6-af5e-722d9ba3703a", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample article", "url": "https://meghanag.supporthive.com/kb/article/12-sample-article/", "article_id": 12, "section_id": 2, "last_updated_at": "2025-04-22T05:44:42Z", "has_attachments": false, "attachment_count": 0}, "hash": "f1313277034e8b093b35b3352fa6f57c45a1829a90009999587d67527e82c0ae", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Understanding Your Billing Cycle\n\nYour billing cycle is determined by your signup date and plan:\n\n\\- Monthly plans renew every 30 days.  \n\\- Annual plans renew once every 12 months.  \n\\- You’ll be billed automatically unless auto-renew is turned off.  \n\\- Receipts are emailed after each successful payment.\n\nTo view your billing history, go to Settings > Billing > History.  \n\nHow to Delete Your Account\n\nWarning: Deleting your account is permanent.\n\nSteps to delete your account:\n\n1\\. Log in and go to Settings > Account.  \n2\\. <PERSON><PERSON> down and click on \"Delete My Account\".  \n3\\. Enter your password to confirm the action.  \n4\\. You will receive a confirmation email. Click the link to complete\ndeletion.\n\nYour data will be permanently removed within 7 days. This action is\nirreversible.  \n\nHow to change email address\n\nTo update your registered email address:\n\n1\\. Log in to your account.  \n2\\. Navigate to Settings > Profile.  \n3\\. Click \"Edit\" next to your email address.  \n4\\. Enter the new email and confirm.  \n5\\. A verification email will be sent to the new address.  \n6\\. Click the verification link to complete the update.\n\nNote: You must verify the new address before it takes effect.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1196, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "d7cf8378-6f8c-4082-8f9d-73477246ae63": {"__data__": {"id_": "d7cf8378-6f8c-4082-8f9d-73477246ae63", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample docx", "url": "https://meghanag.supporthive.com/kb/article/17-sample-docx/", "article_id": 17, "section_id": 2, "last_updated_at": "2025-05-12T06:18:44Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "64bfae43-527d-453f-a18d-03aa6c5d088c", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample docx", "url": "https://meghanag.supporthive.com/kb/article/17-sample-docx/", "article_id": 17, "section_id": 2, "last_updated_at": "2025-05-12T06:18:44Z", "has_attachments": true, "attachment_count": 1}, "hash": "94daa94105e4847e0d84debabad77a2032e2b63fe25745cd0ec4a35de5556ae9", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "test docx", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 9, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "e8a17a3e-b5d4-49cd-87ee-cdd6d37427af": {"__data__": {"id_": "e8a17a3e-b5d4-49cd-87ee-cdd6d37427af", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 17, "parent_article_title": "sample docx", "attachment_id": 1, "attachment_name": "sample_kb_article.docx", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-12/69860b5a-91bb-4339-bf21-5b9c647d9053/sample_kb_article.docx", "mime_type": "application/octet-stream", "file_extension": ".docx", "file_size": 37001, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "97278268-8d8f-4fc7-ae7a-9b77163f0f07", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 17, "parent_article_title": "sample docx", "attachment_id": 1, "attachment_name": "sample_kb_article.docx", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-12/69860b5a-91bb-4339-bf21-5b9c647d9053/sample_kb_article.docx", "mime_type": "application/octet-stream", "file_extension": ".docx", "file_size": 37001, "section_id": 2, "is_embedded_image": false, "parsed_with_pdf_reader": true}, "hash": "db7d581f477a2bc6df40c80c33308ce401eebefc4e70ceee2abb42579aa5261e", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Attachment: sample_kb_article.docx\nFile Type: application/octet-stream\nSize: 37001 bytes\n\nContent parsing not supported for this file type.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 139, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "b00e2cc1-0ea9-4563-b228-a8bfe9dbb63f": {"__data__": {"id_": "b00e2cc1-0ea9-4563-b228-a8bfe9dbb63f", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample image", "url": "https://meghanag.supporthive.com/kb/article/19-sample-image/", "article_id": 19, "section_id": 2, "last_updated_at": "2025-05-14T11:23:38Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "8809b4bd-2bd2-4efd-acb7-c26fc9cff0e7", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample image", "url": "https://meghanag.supporthive.com/kb/article/19-sample-image/", "article_id": 19, "section_id": 2, "last_updated_at": "2025-05-14T11:23:38Z", "has_attachments": true, "attachment_count": 1}, "hash": "f75323da85e9730f572c96f760cd00697eaa56b143fbbf748f3b7d2d0f3c474b", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "![](https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-14/89f0213e-8ddf-415e-8bce-1f32bdbe8c43/Screenshot_2025-05-14_at_4.52.20PM.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 158, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "1489556f-32ab-41b3-9c81-bf0516c36c99": {"__data__": {"id_": "1489556f-32ab-41b3-9c81-bf0516c36c99", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 19, "parent_article_title": "sample image", "attachment_id": "img_19_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-14/89f0213e-8ddf-415e-8bce-1f32bdbe8c43/Screenshot_2025-05-14_at_4.52.20PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 625662, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "285904d3-a667-4e6b-9282-1d5a26a91093", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 19, "parent_article_title": "sample image", "attachment_id": "img_19_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-14/89f0213e-8ddf-415e-8bce-1f32bdbe8c43/Screenshot_2025-05-14_at_4.52.20PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 625662, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "57020a0d5b3c5ff7cfbd32e43e13e2b828d913a3ac2b843ebf17dd2e2f7b4420", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "# Guides and Tutorials > Audiences\n\n# Getting Started with Segments\n\nRead this page in English Copy Article URL\n\nYour audience contains a lot of information about your contacts, like when they were added, where they live, and how they interact with your marketing. You can use this information to filter contacts into segments, and then target them with email, postcard, or ad campaigns.\n\nIn this article, you'll learn how segments work in Mailchimp.\n\n## Definitions\n\n- Segment\n  We use the term segment in two ways: as a verb and a noun. To segment is to take the action of filtering and dividing similar contacts. The resulting list of contacts is also called a segment.\n\n- Subscribed contact\n  Someone who has opted in to receive your email marketing campaigns.\n\n- Unsubscribed contact\n  Someone who was opted in to receive your email marketing campaigns, but isn't currently.\n\n- Non-subscribed contact\n  Someone who has interacted with your online store, but hasn't opted in to receive your email marketing campaigns.\n\n- Cleaned contact", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1040, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "c21bbb19-da47-4594-bde1-cba69b6b2b4f": {"__data__": {"id_": "c21bbb19-da47-4594-bde1-cba69b6b2b4f", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "segment", "url": "https://meghanag.supporthive.com/kb/article/20-segment/", "article_id": 20, "section_id": 2, "last_updated_at": "2025-05-15T15:07:55Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "8f54c51d-023a-4fc8-a57e-62210797a7a3", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "segment", "url": "https://meghanag.supporthive.com/kb/article/20-segment/", "article_id": 20, "section_id": 2, "last_updated_at": "2025-05-15T15:07:55Z", "has_attachments": true, "attachment_count": 1}, "hash": "ed3f62cbe935a18eef58b651a4711eb818d9dcae7596c9f67608233353d93e3b", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "![](https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-15/9970e608-34ec-4d3e-8230-a0be6f858b31/Screenshot_2025-05-15_at_8.37.11PM.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 158, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "718ca858-f9d3-4177-9eb2-cdf93e783643": {"__data__": {"id_": "718ca858-f9d3-4177-9eb2-cdf93e783643", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 20, "parent_article_title": "segment", "attachment_id": "img_20_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-15/9970e608-34ec-4d3e-8230-a0be6f858b31/Screenshot_2025-05-15_at_8.37.11PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 94007, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "3178530f-ad9f-4b8b-a466-d213f8ac5118", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 20, "parent_article_title": "segment", "attachment_id": "img_20_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-15/9970e608-34ec-4d3e-8230-a0be6f858b31/Screenshot_2025-05-15_at_8.37.11PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 94007, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "4fbdd043bcdf1692d04d1e24c64b3b4fad1e25532021280d87e6778f44a21c12", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "# Getting Started with Segment\n\n1. Create a Segment account at segment.com\n2. Log in and go to the Dashboard\n3. Click on 'Add Source' to connect your app\n4. Choose your platform (iOS, Android, Web, etc.)\n5. Copy the generated write key\n6. Install the Segment SDK and initialize with the key\n7. Verify events in the Segment debugger", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 331, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "073f8dac-115f-4678-923d-c05aef95c68e": {"__data__": {"id_": "073f8dac-115f-4678-923d-c05aef95c68e", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article", "url": "https://meghanag.supporthive.com/kb/article/1-test-article/", "article_id": 1, "section_id": 2, "last_updated_at": "2025-04-08T16:05:41Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "de29d540-51c4-4110-b83e-bea5e13a457f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article", "url": "https://meghanag.supporthive.com/kb/article/1-test-article/", "article_id": 1, "section_id": 2, "last_updated_at": "2025-04-08T16:05:41Z", "has_attachments": false, "attachment_count": 0}, "hash": "ffa5c5d8d8cec1e1f4458b09a64779bd868870ac763e29aaadc2796130dfbd8d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "This is a sample knowledge base article to test the HappyFox API reader.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 72, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "646d729c-2799-48c0-9f7c-1aa2109b8960": {"__data__": {"id_": "646d729c-2799-48c0-9f7c-1aa2109b8960", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 2", "url": "https://meghanag.supporthive.com/kb/article/2-test-article-2/", "article_id": 2, "section_id": 2, "last_updated_at": "2025-04-09T10:10:39Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "a5e51cfd-c339-4bd8-97d1-6d45fb549900", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 2", "url": "https://meghanag.supporthive.com/kb/article/2-test-article-2/", "article_id": 2, "section_id": 2, "last_updated_at": "2025-04-09T10:10:39Z", "has_attachments": false, "attachment_count": 0}, "hash": "7494f46e4dc66339eca63b3f344828bb960c709fab60fee43a26b072773fb5ea", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "`This is another test article to verify HappyFox reader updates.`", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 65, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "b08063b2-e7be-4858-a67a-4ae0e9db0c04": {"__data__": {"id_": "b08063b2-e7be-4858-a67a-4ae0e9db0c04", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 3", "url": "https://meghanag.supporthive.com/kb/article/3-test-article-3/", "article_id": 3, "section_id": 3, "last_updated_at": "2025-04-16T06:34:30Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "ae3d92f6-9367-42b1-8176-bcacc65b4e56", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 3", "url": "https://meghanag.supporthive.com/kb/article/3-test-article-3/", "article_id": 3, "section_id": 3, "last_updated_at": "2025-04-16T06:34:30Z", "has_attachments": false, "attachment_count": 0}, "hash": "7de16eccb9a8b2cbc6f52743973366a2c2a4750f7755c5bafe60a7c0a0ef9174", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Having trouble logging into your account? Here are some common causes and\nquick fixes:\n\n1\\. Incorrect Username or Password****  \nDouble-check for typos. Passwords are case-sensitive.\n\n2\\. B<PERSON><PERSON> Cache and Cookies****  \nClear your browser's cache and cookies and try logging in again.\n\n3\\. Two-Factor Authentication Issues****  \nIf you're not receiving the OTP, check your spam folder or ensure your\nregistered phone number is correct.\n\n4\\. Account Lockouts  \nAfter multiple failed attempts, your account might be temporarily locked. Wait\n15 minutes before trying again.\n\nIf you're still having trouble, please contact support or reset your password\nusing the \"Forgot Password\" link.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 683, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "********-746b-4734-9313-e839a2d51e4d": {"__data__": {"id_": "********-746b-4734-9313-e839a2d51e4d", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 4", "url": "https://meghanag.supporthive.com/kb/article/4-test-article-4/", "article_id": 4, "section_id": 2, "last_updated_at": "2025-04-16T06:36:18Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "08dc8b9e-dcb0-4f0a-a80b-1d7f0bf608e9", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 4", "url": "https://meghanag.supporthive.com/kb/article/4-test-article-4/", "article_id": 4, "section_id": 2, "last_updated_at": "2025-04-16T06:36:18Z", "has_attachments": false, "attachment_count": 0}, "hash": "f346b26dd871456feb7dba1fa4d576fdb2bbcff9fed3ac65eb84b798f05cb4ff", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Title:\n\nContent:\n\nIf you've forgotten the answers to your security questions or wish to update\nthem, follow the steps below:\n\n1\\. Log into your account using your username and password.  \n2\\. Navigate to Settings > Security Settings.  \n3\\. Click on \"Reset Security Questions\".  \n4\\. Enter your current password for verification.  \n5\\. Choose new questions from the dropdown and provide new answers.  \n6\\. Click Save to update.\n\nNote: If you're unable to access your account, please click on “Need Help?” on\nthe login screen and select \"Contact Support\" to verify your identity and\nrequest a manual reset.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 604, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "47508fb5-80ef-4480-bf77-e6f000947568": {"__data__": {"id_": "47508fb5-80ef-4480-bf77-e6f000947568", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 5", "url": "https://meghanag.supporthive.com/kb/article/5-test-article-5/", "article_id": 5, "section_id": 2, "last_updated_at": "2025-04-16T07:44:33Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "9dc886c9-beca-434b-a9cf-ecf4996d395d", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 5", "url": "https://meghanag.supporthive.com/kb/article/5-test-article-5/", "article_id": 5, "section_id": 2, "last_updated_at": "2025-04-16T07:44:33Z", "has_attachments": false, "attachment_count": 0}, "hash": "f22a7bbbdca0ad17de472cf27b3944f7471c6fc1da5cd3f00bed67a1938fa369", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Updating your profile ensures your contact information and preferences stay\ncurrent.  \n  \nSteps to update your profile:\n\n1\\. Log in to your Helpdesk account.  \n2\\. Click on your profile icon in the top-right corner.  \n3\\. Select “My Profile” from the dropdown menu.  \n4\\. Update the following fields as needed:  \n\\- Full Name  \n\\- Email Address  \n\\- Phone Number  \n\\- Preferred Language  \n5\\. Click “Save Changes” at the bottom of the page.\n\nNotes:  \n\\- If you're unable to update certain fields (like email), contact support for\nhelp.  \n\\- Your changes may take a few minutes to reflect across the system.\n\nFor privacy reasons, make sure your profile does not contain sensitive or\nincorrect information.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 704, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "3f65a99c-788c-4015-9349-9c5c5b3d5e58": {"__data__": {"id_": "3f65a99c-788c-4015-9349-9c5c5b3d5e58", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "test image", "url": "https://meghanag.supporthive.com/kb/article/18-test-image/", "article_id": 18, "section_id": 2, "last_updated_at": "2025-05-13T04:34:16Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "d8094c2e-cc0d-4ebb-826b-d3ef6c09faa8", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "test image", "url": "https://meghanag.supporthive.com/kb/article/18-test-image/", "article_id": 18, "section_id": 2, "last_updated_at": "2025-05-13T04:34:16Z", "has_attachments": true, "attachment_count": 1}, "hash": "9f3ae6b5402b9047fc4eac154bf9ebf7ed5fa61b2281644279125ccbdb614b98", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "![](https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-13/156de1d0-0a3e-4fbe-b6f1-fc888c203515/Screenshot_2025-05-13_at_10.03.18AM.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 159, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "443a2e8c-038e-4c6f-a773-42ee7e485325": {"__data__": {"id_": "443a2e8c-038e-4c6f-a773-42ee7e485325", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 18, "parent_article_title": "test image", "attachment_id": "img_18_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-13/156de1d0-0a3e-4fbe-b6f1-fc888c203515/Screenshot_2025-05-13_at_10.03.18AM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 266051, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "dc255705-c6cf-4bea-bdac-3a7fda860d16", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 18, "parent_article_title": "test image", "attachment_id": "img_18_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-13/156de1d0-0a3e-4fbe-b6f1-fc888c203515/Screenshot_2025-05-13_at_10.03.18AM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 266051, "section_id": 2, "is_embedded_image": true, "parsed_with_pdf_reader": true}, "hash": "0d697121bf222c9c77a13ee04bcab9162c1f7dd09448d1f088f753a2639ae0ba", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "# Setting up your online store\n\nAfter you've set up Shopify, most of the work is done.\n\n- Your online store will automatically use your Shopify settings for checkout and order fulfillment.\n- Your products will automatically appear on your online store.\n- Your online store is automatically assigned a unique myshopify domain website address. This looks like your-store-name.myshopify.com and it's based on the store name that you entered when you signed up.\n\nThere are a few steps you should follow before launching to make sure your online store is ready for customers.\n\n## Grow your business\n\nIf you need help setting up or customizing your online store, then you can hire a Shopify expert.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 692, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "c8d3a80e-43cb-46f5-88cb-1489298595b3": {"__data__": {"id_": "c8d3a80e-43cb-46f5-88cb-1489298595b3", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "url sample", "url": "https://meghanag.supporthive.com/kb/article/21-url-sample/", "article_id": 21, "section_id": 2, "last_updated_at": "2025-05-16T04:09:12Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "a8c110ab-466a-45d7-b919-00b2d16d37c1", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "url sample", "url": "https://meghanag.supporthive.com/kb/article/21-url-sample/", "article_id": 21, "section_id": 2, "last_updated_at": "2025-05-16T04:09:12Z", "has_attachments": false, "attachment_count": 0}, "hash": "c7c848b616e153c8035355ea56b0a60c223b05bdeccd47754d93f9bd2a2ca173", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "<http://example.com>", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 20, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}}}