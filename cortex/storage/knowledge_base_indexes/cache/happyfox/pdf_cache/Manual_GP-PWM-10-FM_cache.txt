-----layout_info_image_1__start-----
LAYOUT ANALYSIS:
- Headings: [No visible headings in the image]
- Section Context: [Unable to determine from the image]
- Content Role: [Unable to determine from the image]
- Layout Structure: The image appears to be a blank white page with no visible content or structure.
- Elements: [No visible elements in the image]
- Visual Elements:
  * Charts: [None visible]
  * Diagrams: [None visible]
  * Tables: [None visible]
  * Images: [None visible]
- Text Formatting: [No visible text in the image]
- Visual Hierarchy: [No visible hierarchy or organization in the blank image]
- Context Summary: The image appears to be a blank white page with no content. It may be a placeholder or an error in document rendering.
-----layout_info_image_1__end-----
-----image_1__start-----
{"image_index": 1, "markdown_content": "", "images_metadata": [], "tables_metadata": [], "diagrams_metadata": [], "processing_time": 8.753369569778442}
-----image_1__end-----
-----layout_info_image_0__start-----
LAYOUT ANALYSIS:
- Headings: 
  1. 10AMP PWM SOLAR CONTROLLER (Main heading)
  2. User Manual (Subheading)

- Section Context: Cover page/title page of user manual

- Content Role: Introduction and product identification

- Layout Structure: Single column layout with centered text and image

- Elements: 
  - Text blocks (title, subtitle, product code)
  - Image (product photo)
  - Logo (Go Power! and Dometic)
  - Copyright and contact information

- Visual Elements:
  * Charts: None
  * Diagrams: None
  * Tables: None
  * Images: 1 product photo of the solar controller

- Text Formatting: 
  - Large bold text for main heading
  - Smaller text for subheading and product code
  - Small text for copyright and contact information
  - Centered alignment for most text elements

- Visual Hierarchy: 
  1. Large main heading draws attention first
  2. Product image is the central focus
  3. Subheading and product details provide additional context
  4. Company logos and contact information at the bottom

- Context Summary: This page serves as the cover of a user manual for a 10AMP PWM Solar Controller. It provides basic product identification, an image of the device, and manufacturer information for Go Power! and Dometic.
-----layout_info_image_0__end-----
-----layout_info_image_3__start-----
LAYOUT ANALYSIS:
- Headings: 
  1. INSTALLATION OVERVIEW (Main heading)
  1.1 INTRODUCTION (Subheading)
  1.2 SYSTEM VOLTAGE AND CURRENT (Subheading)
  1.3 BATTERY TYPE (Subheading)
  1.4 LOW VOLTAGE DISCONNECT FUNCTION (USB PORT) (Subheading)
  1.5 REGULATORY INFORMATION (Subheading)

- Section Context: Installation Overview

- Content Role: Introduction and technical specifications for a solar controller device

- Layout Structure: Single-column layout with clear heading hierarchy

- Elements: 
  - Text blocks
  - Company logo
  - Regulatory compliance symbols (CE and RoHS)

- Visual Elements:
  * Charts: None
  * Diagrams: None
  * Tables: None
  * Images: Company logo (Go Power!)

- Text Formatting: 
  - Dense paragraphs under each subheading
  - Consistent font size for body text
  - Larger, bold font for headings and subheadings

- Visual Hierarchy: 
  - Clear distinction between main heading, subheadings, and body text
  - Logical flow from introduction to specific technical details
  - Regulatory symbols placed at the bottom for visual separation

- Context Summary: This page provides an installation overview for a Dometic solar controller (GP-PWM-10-FM model). It covers introduction, system specifications, battery compatibility, and regulatory compliance information for the device.
-----layout_info_image_3__end-----
-----layout_info_image_2__start-----
LAYOUT ANALYSIS:
- Headings: 
  Level 1: CONTENTS
  Level 2: 1. INSTALLATION OVERVIEW, 2. WARNINGS, 3. TOOLS AND MATERIALS NEEDED, 4. CHOOSING A LOCATION, 5. INSTALLATION INSTRUCTIONS, 6. WIRING DIAGRAM, 7. OPERATING INSTRUCTIONS, 8. DISPLAY SYMBOLS, 9. USB CHARGING, 10. FREQUENTLY ASKED QUESTIONS (FAQS), 11. TROUBLESHOOTING PROBLEMS, 12. LIMITED WARRANTY, 13. INSTALLATION TEMPLATE
  Level 3: (Subheadings under main sections, e.g. 1.1 INTRODUCTION, 1.2 SYSTEM VOLTAGE AND CURRENT, etc.)

- Section Context: Table of Contents

- Content Role: Navigation and overview of document structure

- Layout Structure: Single column layout for main content, with indented subheadings

- Elements: Text blocks (headings and page numbers)

- Visual Elements:
  * Charts: None
  * Diagrams: None
  * Tables: Table of contents
  * Images: Company logo (Dometic)

- Text Formatting: Hierarchical formatting with different font sizes and styles for main headings and subheadings. Page numbers aligned to the right.

- Visual Hierarchy: Clear distinction between main sections (bold, larger font) and subsections (smaller font, indented). Page numbers provide additional organization.

- Context Summary: This page presents the table of contents for a technical manual, likely for a Dometic product. It outlines the document's structure, covering installation, operation, troubleshooting, and warranty information across 13 main sections.
-----layout_info_image_2__end-----
-----layout_info_image_4__start-----
LAYOUT ANALYSIS:
- Headings: 
  1. INSTALLATION OVERVIEW (main heading)
  2. 1.6 SPECIFICATIONS (subheading)

- Section Context: Installation Overview, Specifications section

- Content Role: Technical specifications and product details

- Layout Structure: Single column layout with a table structure for specifications

- Elements: 
  - Text (headings and table content)
  - Table (specifications)
  - Logos (Go Power! and Dometic)

- Visual Elements:
  * Charts: None
  * Diagrams: None
  * Tables: 1 (specifications table)
  * Images: 2 (company logos)

- Text Formatting: 
  - Sparse text density
  - Primarily tabular format
  - Bold text for table headers and some content
  - Mixture of left-aligned and centered text in table cells

- Visual Hierarchy:
  - Large bold main heading
  - Smaller bold subheading
  - Table with clear distinction between header row and content rows
  - Company logos placed in top right corner

- Context Summary: This page provides detailed technical specifications for a product, likely a battery or power management system. It includes electrical characteristics, physical dimensions, and operational parameters, presented in a structured table format for easy reference.
-----layout_info_image_4__end-----
-----image_0__start-----
{"image_index": 0, "markdown_content": "# 10AMP PWM SOLAR CONTROLLER\n\n## User Manual\n\nGP-PWM-10-FM (FLUSH MOUNT - LITHIUM COMPATIBLE)\n\n{{850bdf6b00434d80b02b4d60d34e6dbe}}\n\n\u00a9 2021 Go Power!\n\nWorldwide Technical Support and Product Information gpelectric.com\nGo Power! | Dometic\n201-710 Redbrick Street Victoria, BC, V8T 5J3\nTel: **************\n\nManual_GP-PWM-10-FM", "images_metadata": [{"image_id": "850bdf6b00434d80b02b4d60d34e6dbe", "summary": "A photo of the 10 AMP PWM Solar Controller device. It has a black rectangular faceplate with a digital LCD display in the center showing \"0.0 V\" and \"kAh\". There are icons for solar, battery, and load on the display. The device has two buttons labeled A and B below the display, and a switch on the left side. The Go Power! logo and website URL are visible on the right side of the faceplate.", "actual_reference": "", "is_complete": true, "page_num": 0, "cross_page_context": null}], "tables_metadata": [], "diagrams_metadata": [], "processing_time": 22.29444909095764}
-----image_0__end-----
-----image_2__start-----
{"image_index": 2, "markdown_content": "# CONTENTS\n\n{{7930945cf7d8410db0f18ea67199aa50}}\n\n1. INSTALLATION OVERVIEW..................................................................................................................... 4\n\n   1.1 INTRODUCTION ...............................................................................................................................4\n   1.2 SYSTEM VOLTAGE AND CURRENT.....................................................................................................4\n   1.3 BATTERY TYPE..................................................................................................................................4\n   1.4 LOW VOLTAGE DISCONNECT FUNCTION (USB PORT) .......................................................................4\n   1.5 REGULATORY INFORMATION ...........................................................................................................4\n   1.6 SPECIFICATIONS...............................................................................................................................4\n\n2. WARNINGS ............................................................................................................................................... 5\n\n3. TOOLS AND MATERIALS NEEDED ........................................................................................................ 6\n\n4. CHOOSING A LOCATION ........................................................................................................................ 6\n\n5. INSTALLATION INSTRUCTIONS ............................................................................................................. 6\n\n6. WIRING DIAGRAM .................................................................................................................................. 7\n\n7. OPERATING INSTRUCTIONS .................................................................................................................. 8\n\n   7.1 POWER UP......................................................................................................................................8\n   7.2 SETTING THE BATTERY CHARGING PROFILE .....................................................................................8\n   7.3 BATTERY CHARGING PROFILE CHART ...............................................................................................9\n   7.4 VIEWING THE CONTROLLER DISPLAY INFORMATION........................................................................9\n   7.5 RESETTING THE AMPERE HOURS CHARGED ...................................................................................11\n   7.6 ERRORS .........................................................................................................................................11\n\n8. DISPLAY SYMBOLS................................................................................................................................. 12\n\n9. USB CHARGING...................................................................................................................................... 13\n\n10. FREQUENTLY ASKED QUESTIONS (FAQS) .......................................................................................... 14\n\n11. TROUBLESHOOTING PROBLEMS......................................................................................................... 14\n\n    11.1 PROBLEMS WITH THE DISPLAY......................................................................................................15\n    11.2 PROBLEMS WITH VOLTAGE ...........................................................................................................15\n    11.3 PROBLEMS WITH CURRENT...........................................................................................................16\n\n12. LIMITED WARRANTY ............................................................................................................................. 17\n\n    12.1 REPAIR AND RETURN INFORMATION.............................................................................................17\n\n13. INSTALLATION TEMPLATE .................................................................................................................... 17\n\ngpelectric.com | [page 3]", "images_metadata": [{"image_id": "7930945cf7d8410db0f18ea67199aa50", "summary": "The image shows two logos. On the top right, there's a circular logo with the text \"Go Power!\" inside. Below it is the Dometic logo, which consists of the word \"DOMETIC\" in a stylized font.", "actual_reference": "", "is_complete": true, "page_num": 2, "cross_page_context": null}], "tables_metadata": [], "diagrams_metadata": [], "processing_time": 30.001296520233154}
-----image_2__end-----
-----layout_info_image_5__start-----
LAYOUT ANALYSIS:

- Headings: 
  Level 1: 2. WARNINGS

- Section Context: Warnings section of a manual or guide

- Content Role: Safety information and precautions

- Layout Structure: Single column layout with multiple warning boxes

- Elements: 
  * Text blocks
  * Warning icons/symbols
  * Company logo

- Visual Elements:
  * Charts: None
  * Diagrams: None
  * Tables: None
  * Images: 6 warning symbols/icons, 1 company logo

- Text Formatting: 
  * Short text blocks for each warning
  * Bold text for warning titles
  * Regular text for descriptions
  * Consistent formatting across warning boxes

- Visual Hierarchy: 
  * Main heading at the top
  * Warning boxes of equal importance arranged vertically
  * Each box contains an icon on the left and text on the right
  * Consistent spacing between elements

- Context Summary: This page presents important safety warnings for a Dometic product, likely an electrical or battery-operated device. It provides crucial information on handling electricity, batteries, and general safety precautions during installation and use.
-----layout_info_image_5__end-----
-----image_3__start-----
{"image_index": 3, "markdown_content": "# 1. INSTALLATION OVERVIEW\n\n## 1.1 INTRODUCTION\n\nA Solar Controller (or Charge Controller / Regulator) is an essential component of your photovoltaic solar system. The Controller maintains the life of the battery by protecting it from overcharging. When your battery has reached a 100% state of charge, the Controller prevents overcharging by limiting the current flowing into the batteries from your solar array.\n\nThe GP-PWM-10-FM uses Pulse Width Modulation (PWM) technology and a unique four stage charging system that includes an optional equalize setting to charge and protect your battery bank. The GP-PWM-10-FM features an LCD digital display that shows the charge current of the solar array, battery voltage and battery state of charge.\n\n## 1.2 SYSTEM VOLTAGE AND CURRENT\n\nThe GP-PWM-10-FM is intended for use at 12 VDC nominal system voltage and is rated for a maximum continuous DC input current of 12.5A and input voltage of 35VDC.\n\nPer the National Electric Code (NEC) article 690.7 and 690.8, PV module nameplate ratings at Standard Test Conditions (STC) must be multiplied by required values (typically 1.25 for both voltage and current) to obtain the true voltage and continuous current available from the module.\n\nApplying the NEC factors, the maximum allowable nameplate PV Panel rated Isc is 10A (10A x 1.25 = 12.5A), and the maximum voltage, Voc is 28VDC (28VDC x 1.25 = 35VDC).\n\nThe voltage and current ratings of all equipment connected to PV panels must be capable of accepting the voltage and current levels available from PV panels installed in the field.\n\n## 1.3 BATTERY TYPE\n\nThe GP-PWM-10-FM is suitable for use with lead acid and lithium batteries (vented, GEL, LiFePO4 (LFP) or AGM type).\n\n## 1.4 LOW VOLTAGE DISCONNECT FUNCTION (USB PORT)\n\nTo protect the battery against over-discharge this function automatically switches off the USB output port when battery voltage is lower than 11.0 VDC. As soon as the battery reaches a voltage of 12.8 VDC the USB output port is switched on again.\n\n## 1.5 REGULATORY INFORMATION\n\n{{b127aaf5568a42a2ab87f0378de9392d}}\n\n[page 4] | gpelectric.com", "images_metadata": [{"image_id": "b127aaf5568a42a2ab87f0378de9392d", "summary": "Two regulatory compliance symbols are shown. On the left is the CE (Conformit\u00e9 Europ\u00e9enne) mark, indicating compliance with European Union standards. On the right is the RoHS (Restriction of Hazardous Substances) symbol, indicating compliance with the EU directive restricting the use of certain hazardous substances in electrical and electronic equipment.", "actual_reference": "", "is_complete": true, "page_num": 3, "cross_page_context": null}], "tables_metadata": [], "diagrams_metadata": [], "processing_time": 34.39104747772217}
-----image_3__end-----
-----layout_info_image_6__start-----
LAYOUT ANALYSIS:

- Headings: 
  1. TOOL AND MATERIALS NEEDED (Level 1)
  2. CHOOSING A LOCATION (Level 1)
  3. INSTALLATION INSTRUCTIONS (Level 1)

- Section Context: Installation guide for GP-PWM-10-FM Controller

- Content Role: Instructional content providing details on tools, location selection, and installation steps

- Layout Structure: Single-column layout with clear section breaks

- Elements: Text blocks, bulleted lists, numbered lists, note box

- Visual Elements:
  * Charts: None
  * Diagrams: None
  * Tables: One table for Suggested Minimum Wire Gauge
  * Images: Company logos (Go Power! and DOMETIC)

- Text Formatting: 
  - Mixture of paragraphs and bulleted/numbered lists
  - Bold text used for emphasis and subheadings
  - Compact, information-dense formatting

- Visual Hierarchy: 
  - Clear main headings in large, bold font
  - Subheadings and important information in bold
  - Use of a boxed "Note" section for important information
  - Numbered lists for step-by-step instructions

- Context Summary: This page is part of an installation manual for a GP-PWM-10-FM Controller, likely for solar power systems in RVs. It covers required tools, location selection criteria, and detailed installation instructions including wiring specifications.
-----layout_info_image_6__end-----
-----image_4__start-----
{"image_index": 4, "markdown_content": "# INSTALLATION OVERVIEW\n\n## 1.6 SPECIFICATIONS\n\n{{b467a98cb2af44169a8f265dacf977c3}}\n\ngpelectric.com | [page 5]", "images_metadata": [{"image_id": "2694ae4435be4b55929618ff0e8a411f", "summary": "Two company logos are visible in the top right corner of the document. The first logo is for 'Go Power!' and the second logo is for 'DOMETIC'.", "actual_reference": "", "is_complete": true, "page_num": 4, "cross_page_context": null}], "tables_metadata": [{"table_id": "b467a98cb2af44169a8f265dacf977c3", "summary": "The table contains detailed specifications for a power management system. It has two columns: 'DESCRIPTION' and 'VALUE'. The table includes information such as:\n\n- Nominal System Voltage: 12 VDC\n- Range of Battery Input Voltage: 9 - 15.5 VDC\n- Maximum Solar Continuous DC Charge Current Input: 12.5 ADC\n- Charging Output DC Voltage Range: 9 - 14.9 VDC\n- Maximum Solar DC Input Voltage: 35 VDC\n- Maximum Series Fuse or Circuit Breaker Solar/Battery: 15 A\n- Operating Consumption (Display backlight on): 15mA\n- Operating Consumption (Display backlight off): 6 mA\n- Battery Types Supported: Vented & Sealed Lead Acid (GEL, AGM, Flooded, etc.), Lithium (LiFePO4)\n- Bulk/Absorption Voltage: 14.1/14.4/14.4VDC (25\u00b0C / 77\u00b0F), 30min / Day or 2hr if battery voltage < 12.3 VDC\n- Float Voltage: 13.7V (25\u00b0C / 77\u00b0F), 14.0V(LFP)\n- Equalization Voltage: 14.9V (25\u00b0C / 77\u00b0F), 2h / 28 Day or if battery voltage < 12.1 VDC\n- Temperature Compensation: - 24mV/\u00b0C / -13V/\u00b0F\n- USB charger: 5V, 1500mA\n- Low Voltage Disconnect (USB): 11.0 VDC, Reconnects once battery reaches 12.8 VDC\n- Operating Temperature: - 40 to 85\u00b0C / - 40 to 185\u00b0F\n- Display Operating Temperature: - 10 to 55\u00b0C / 14 to 131\u00b0F\n- Humidity: 99% N.C\n- Protection: Battery Reverse Polarity, Solar Array Reverse Polarity, Over Temperature, PV Short Circuit, Over Current\n\nAdditional information:\n- Dimensions (H x W x D): 149 x 98 x 32 mm, 5.87 x 3.86 x 1.26 in\n- Weight: 260 g / 9.2 oz\n- Maximum Wire Gauge: #4 AWG\n- Warranty: 5 years\n\nFeatures:\n- PWM Charging\n- 4 Battery Charging Profiles\n- 4-Stage Charging\n- Monthly Equalize Option\n- Displays Charging Current, Battery Voltage, Battery State of Charge, and Amp Hours Charged Since Last Reset\n- Reverse Polarity Protected\n- Temperature Compensated\n- RoHS Compliant, Environmentally Safe\n- Accepts up to 160 Watts of Solar at 12 Volts", "actual_reference": "", "is_complete": true, "page_num": 4, "cross_page_context": null}], "diagrams_metadata": [], "processing_time": 36.86371374130249}
-----image_4__end-----
-----layout_info_image_7__start-----
LAYOUT ANALYSIS:

- Headings: 
  Level 1: 6. WIRING DIAGRAMS
  Level 2: IMPORTANT (appears twice)
  Level 3: WARNING (appears twice)

- Section Context: Wiring Diagrams section of a manual or guide

- Content Role: Instructional content providing guidelines and warnings for wiring

- Layout Structure: Single column layout with occasional boxed sections for tables and warnings

- Elements: Text blocks, table, warning symbols, numbered list items

- Visual Elements:
  * Charts: None
  * Diagrams: None
  * Tables: 1 (Stranded Copper 90°C Wire table)
  * Images: 2 warning symbols (triangles with exclamation marks)

- Text Formatting: 
  - Mixture of paragraph text and numbered list items
  - Bold text used for emphasis on important points
  - Compact text density with clear spacing between sections

- Visual Hierarchy: 
  - Main heading at the top
  - Subheadings in bold
  - Numbered list for step-by-step instructions
  - Warning messages highlighted with symbols and boxed text
  - Table used to present data clearly

- Context Summary: This page provides detailed instructions and warnings for wiring a GP-PWM-10-FM controller, including connection procedures, torque specifications, and important safety notices. It's part of a technical manual for installing and operating electrical equipment.
-----layout_info_image_7__end-----
-----image_5__start-----
{"image_index": 5, "markdown_content": "# 2. WARNINGS\n\n{{a16da45ac9c74e0bb84581f03c56492a}}\n\n{{d50830e7cfe24b7e9988e6c3e08ff933}} **Disconnect all power sources**\n\nElectricity can be very dangerous. Installation should be performed only by a licensed electrician or qualified personnel.\n\n{{ea94cc0989a74433b0249ca4fd2883d2}} **Battery and wiring safety**\n\nObserve all safety precautions of the battery manufacturer when handling or working around batteries. When charging, batteries produce hydrogen gas, which is highly explosive.\n\n{{7b4b52cefc0447fd90ed8bb5a21ef443}} **Wiring connections**\n\nEnsure all connections are tight and secure. Loose connections may generate sparks and heat. Be sure to check connections one week after installation to ensure they are still tight.\n\n{{d74710c4dd3942e4b80129900b165ebb}} **Work safely**\n\nWear protective eye wear and appropriate clothing during installation. Use extreme caution when working with electricity and when handling and working around batteries. Use properly insulated tools only.\n\n{{d33fa9e0219d4511a6014400f3a393e3}} **Observe correct polarity at all times**\n\nReverse polarity of the battery terminals will cause the controller to give a warning tone. Reverse connection of the array will not cause an alarm but the controller will not function. Failure to correct this fault could damage the controller.\n\n{{91775d3ca90e450c965af56b7c032c05}} **Do not exceed the GP-PWM-10-FM Amp current and max voltage ratings**\n\nThe maximum current of the solar system is the sum of parallel-connected PV module--rated short circuit Currents (Isc) multiplied by 1.25. The resulting system current is not to exceed 12.5A. If your solar system exceeds this value, contact your dealer for a suitable controller alternative.\n\n**Do not exceed the GP-PWM-10-FM max voltage ratings**\n\nThe maximum voltage of the array is the sum of the PV module--rated open-circuit voltage of the series connected modules multiplied by 1.25 (or by a value from NEC 690.7 provided in Table 690.7 A). The resulting voltage is not to exceed 35V. If your solar system exceeds this value, contact your dealer for a suitable controller alternative.\n\n[page 6] | gpelectric.com", "images_metadata": [{"image_id": "a16da45ac9c74e0bb84581f03c56492a", "summary": "The Dometic company logo, which appears to be a stylized oval shape with the text \"Go Power!\" inside.", "actual_reference": "", "is_complete": true, "page_num": 5, "cross_page_context": null}, {"image_id": "d50830e7cfe24b7e9988e6c3e08ff933", "summary": "A warning symbol depicting a lightning bolt inside a triangle, indicating electrical hazard.", "actual_reference": "", "is_complete": true, "page_num": 5, "cross_page_context": null}, {"image_id": "ea94cc0989a74433b0249ca4fd2883d2", "summary": "A warning symbol showing a battery with a lightning bolt, indicating battery and electrical hazards.", "actual_reference": "", "is_complete": true, "page_num": 5, "cross_page_context": null}, {"image_id": "7b4b52cefc0447fd90ed8bb5a21ef443", "summary": "A warning symbol depicting a hand being struck by lightning, indicating the danger of electrical shock.", "actual_reference": "", "is_complete": true, "page_num": 5, "cross_page_context": null}, {"image_id": "d74710c4dd3942e4b80129900b165ebb", "summary": "A safety symbol showing a person wearing safety goggles, indicating the need for eye protection.", "actual_reference": "", "is_complete": true, "page_num": 5, "cross_page_context": null}, {"image_id": "d33fa9e0219d4511a6014400f3a393e3", "summary": "A warning symbol showing an exclamation mark inside a triangle, indicating general caution or warning.", "actual_reference": "", "is_complete": true, "page_num": 5, "cross_page_context": null}, {"image_id": "91775d3ca90e450c965af56b7c032c05", "summary": "A warning symbol depicting flames, indicating fire hazard or high temperature warning.", "actual_reference": "", "is_complete": true, "page_num": 5, "cross_page_context": null}], "tables_metadata": [], "diagrams_metadata": [], "processing_time": 32.14826059341431}
-----image_5__end-----
-----layout_info_image_8__start-----
LAYOUT ANALYSIS:
- Headings: 
  Level 1: 7. OPERATING INSTRUCTIONS
  Level 2: 7.1 SYSTEM VOLTAGE AND CURRENT
  Level 2: 7.2 SETTING THE BATTERY CHARGING PROFILE

- Section Context: Operating Instructions for a Dometic power system

- Content Role: Main instructional content providing operational details

- Layout Structure: Single column layout with diagrams and text blocks

- Elements: 
  * Text blocks
  * Wiring diagram
  * Device interface diagram
  * Company logos (Dometic, Go Power!)

- Visual Elements:
  * Charts: None
  * Diagrams: 
    - Wiring diagram showing battery bank, fuse, and solar array connections
    - Device interface diagram of a solar controller
  * Tables: None
  * Images: Company logos

- Text Formatting: 
  - Sparse text density
  - Short paragraphs
  - Numbered subsections
  - Bold text for emphasis

- Visual Hierarchy: 
  - Large bold heading at the top
  - Subheadings in bold
  - Diagrams placed prominently between text sections
  - Important information highlighted in bold within paragraphs

- Context Summary: This page provides operating instructions for a Dometic power system, focusing on system voltage and current, and setting the battery charging profile. It includes wiring diagrams and interface instructions for the solar controller.
-----layout_info_image_8__end-----
-----layout_info_image_9__start-----
LAYOUT ANALYSIS:

- Headings: 
  1. OPERATING INSTRUCTIONS (main heading)
  2. 7.3 BATTERY CHARGING PROFILE CHART (subheading)
  3. 7.4 VIEWING THE CONTROLLER DISPLAY INFORMATION (subheading)

- Section Context: Operating Instructions for a battery charging system

- Content Role: Instructional content providing technical specifications and user guidance

- Layout Structure: Single column layout with tables and diagrams

- Elements: 
  - Text blocks
  - Tables
  - Diagrams
  - Company logos

- Visual Elements:
  * Charts: Battery charging profile chart
  * Diagrams: Solar controller display diagram
  * Tables: Battery charging profile table
  * Images: Company logos (Go Power!, DOMETIC)

- Text Formatting: 
  - Sparse text density
  - Short paragraphs
  - Bulleted notes
  - Technical specifications in table format

- Visual Hierarchy: 
  - Large bold headings
  - Subheadings in smaller font
  - Important information highlighted in tables and diagrams
  - Notes separated in bordered boxes

- Context Summary: This page provides operating instructions for a battery charging system, including a detailed charging profile chart for different battery types and instructions on viewing the controller display information. It appears to be part of a technical manual for a solar power or battery management system.
-----layout_info_image_9__end-----
-----image_6__start-----
{"image_index": 6, "markdown_content": "# 3. TOOL AND MATERIALS NEEDED\n\n- Flathead Screwdriver (for wire terminals)\n- Philips Screwdriver (for mounting screws)\n\n> **Note**: If the GP-PWM-10-FM Controller was purchased with a Go Power! Solar Power Kit, then UV resistant wire is included. For instructions regarding the Go Power! Solar Power Kit installation, please refer to the Installation Guide provided with the Kit.\n\n# 4. CHOOSING A LOCATION\n\nThe GP-PWM-10-FM is designed to be mounted flush against a wall, out of the way but easily visible.\n\nThe GP-PWM-10-FM should be:\n\n- Mounted as close to the battery as possible\n- Mounted on a vertical surface to optimize cooling of the unit\n- Indoors, protected from the weather\n\nIn an RV, the most common controller location is above the refrigerator. The wire from the solar array most commonly enters the RV through the fridge vent on the roof or by using the Go Power! Cable Entry Plate (sold separately) that allows installers to run wires through any part of the roof. PV connections should connect directly to the controller. Positive and negative battery connections must connect directly from the controller to the batteries. Use of a positive or negative distribution bus is allowed between the controller and battery as long as it is properly sized, electrically safe and an adequate wire size is maintained.\n\n# 5. INSTALLATION INSTRUCTIONS\n\n1. Prepare for mounting. Use the template provided on page 17 to mark the four mounting holes and the cutting line for flush mounting your controller.\n\n2. Complete the installation of the solar modules. If this GP-PWM-10-FM was purchased as part of a Go Power! Solar Power Kit, follow the Installation Guide provided. Otherwise, follow manufacturer's instructions for solar module mounting and wiring.\n\n3. Select wire type and gauge. If this GP-PWM-10-FM was purchased as part of a Go Power! Solar Power Kit, appropriate wire type, gauge, and length is provided. Please continue to Section 6, \"Operating Instructions.\" If the GP-PWM-10-FM was purchased separately, follow the instructions included here.\n\nWire type is recommended to be a stranded copper UV-resistant wire. Wire fatigue and the likelihood of a loose connection are greatly reduced in stranded wire compared to solid wire. Wire gauge should be able to sustain rated current and minimize voltage drop.\n\nWire Strip Length\nStrip wires to a length of approximately 3/8 in (9 mm, as per strip gauge).\n\nSuggested Minimum Wire Gauge\n(Cable length 25 ft. max. from solar array to battery bank)\n\n{{c014e294a04f4bae8622532e17641e63}}\n\nIMPORTANT: Identify the polarity (positive and negative) on the cable used for the battery and solar module. Use colored wires or mark the wire ends with tags. Although the GP-PWM-10-FM is protected, a reverse polarity contact may damage the unit.\n\nWiring the GP-PWM-10-FM. Wire the GP-PWM-10-FM according to the wiring schematic in Section 6. Run wires from the solar array and the batteries to the location of the GP-PWM-10-FM. Keep the solar array covered with an opaque material until all wiring is completed.", "images_metadata": [{"image_id": "4b03a0e5311f4c17b7446fb5c45bf3af", "summary": "Two company logos are visible at the top right corner of the image. One is for 'Go Power!' and the other is for 'DOMETIC'.", "actual_reference": "", "is_complete": true, "page_num": 6, "cross_page_context": null}], "tables_metadata": [{"table_id": "c014e294a04f4bae8622532e17641e63", "summary": "The table shows suggested minimum wire gauge for different solar module wattages. It has two columns: 'Solar Module' and '#10 Wire Gauge'. The rows are as follows:\n80 Watt Solar Module: #10 Wire Gauge\n100 Watt Solar Module: #10 Wire Gauge\n160 Watt Solar Module: #10 Wire Gauge\n170 Watt Solar Module: #10 Wire Gauge\n190 Watt Solar Module: #10 Wire Gauge", "actual_reference": "Suggested Minimum Wire Gauge", "is_complete": true, "page_num": 6, "cross_page_context": null}], "diagrams_metadata": [], "processing_time": 41.202064990997314}
-----image_6__end-----
-----image_7__start-----
{"image_index": 7, "markdown_content": "# 6. WIRING DIAGRAMS\n\n**IMPORTANT**: All wiring must be in accordance to National Electrical Code, ANSI/NFPA 70. Always use appropriate circuit protection on any conductor attached to a battery.\n\n4. Connect the battery wiring to the controller first and then connect the battery wiring to the battery.\n\n5. Torque all terminal screws per the following:\n\n{{bed79aaed46845b2ac4102d077421453}}\n\nWith battery power attached, the controller should power up and display information. Connect the solar wiring to the controller and remove the opaque material from the solar array. The negative solar array and battery wiring must be connected directly to the controller for proper operation. Do not connect the negative solar array or negative battery controller wiring to the chassis of the vehicle.\n\n6. Mounting the GP-PWM-10-FM. Mount the GP-PWM-10-FM to the wall using the included four mounting screws.\n\n**IMPORTANT**: You must set the battery type on the GP-PWM-10-FM before you begin to use the controller (follow steps in Section 7). The default battery setting is for AGM/LiFePO4 batteries.\n\nCongratulations, your GP-PWM-10-FM should now be operational. If the battery power is low and the solar array is producing power, your battery should begin to charge.\n\n7. Re-torque: After 30 days of operation, re-torque all terminal screws to ensure the wires are properly secured to the controller.\n\n{{fcae861c77c74033badaf8d727a7efa5}}\n\n# 6. WIRING DIAGRAMS\n\n**IMPORTANT**: This diagram is valid only for version 1.5 and newer. Version 1.4 and older have different terminal locations.\n\nThe GP-PWM-10-FM Maximum 12.5A rating is based on a 10 amp total maximum short circuit current rating (Isc) from the parallel solar module nameplate ratings. The National Electric Code specifies the PV equipment/system rating to be 125% of the maximum Isc from the PV module nameplate ratings (1.25 times 10 = 12.5A). Use the wiring diagram (below) to connect your battery to the battery terminals on the solar controller. First, connect the battery to the controller, and then connect the solar panel to the controller\n\n**Note**: The fuse or breaker used should be no larger than 15 amps.\n\n**Note**: The controller will not work unless there is a battery connected to the battery terminals with at least 9V.\n\n{{ceb5c3397e434e31aff9e7ff1c315fd9}}\n\n[page 8] | gpelectric.com", "images_metadata": [{"image_id": "fcae861c77c74033badaf8d727a7efa5", "summary": "A warning symbol (triangle with exclamation mark) followed by text that reads: \"WARNING: This unit is not provided with a GFDI device. This charge controller must be used with an external GFDI device as required by Article 690 of the National Electric Code for the installation location.\"", "actual_reference": "", "is_complete": true, "page_num": 7, "cross_page_context": null}, {"image_id": "ceb5c3397e434e31aff9e7ff1c315fd9", "summary": "A warning symbol (triangle with exclamation mark) followed by text that reads: \"WARNING: When the photovoltaic (solar) array is exposed to light, it supplies a dc voltage to this equipment\"", "actual_reference": "", "is_complete": true, "page_num": 7, "cross_page_context": null}], "tables_metadata": [{"table_id": "bed79aaed46845b2ac4102d077421453", "summary": "A table titled \"Stranded Copper 90\u00b0C Wire\" with two columns: \"Wire Size AWG\" and \"Rated Torque (in-lbs)\". The table contains three rows of data: 14 AWG - 20 in-lbs, 12 AWG - 20 in-lbs, 10 AWG - 20 in-lbs.", "actual_reference": "", "is_complete": true, "page_num": 7, "cross_page_context": null}], "diagrams_metadata": [], "processing_time": 37.44861030578613}
-----image_7__end-----
-----image_8__start-----
{"image_index": 8, "markdown_content": "# 7. OPERATING INSTRUCTIONS\n\n{{46547e6ee2d245a8911eb45730caf1a5}}\n\n## 7.1 SYSTEM VOLTAGE AND CURRENT\n\nWhen the GP-PWM-10-FM is connected to the battery, the controller will go into Power Up mode.\n\nIcons Displayed: All segments of the numerical display; backlight blinks. Depending on the battery voltage when the GP-PWM-10-FM Power Up occurs, the controller may do a Boost Charge or quickly go into Float Charge. The Charging Profile selected will commence the following day after a Power Up (refer to the Charging Profile Chart on page 11 for more details).\n\n## 7.2 SETTING THE BATTERY CHARGING PROFILE\n\n{{3fc4224064ab40edb6df1edd445c3cd0}}\n\nTo select the battery charging profile, press and hold the B Button. This will cause the current battery type to flash.\n\nThen, press the B Button to toggle through the profile options: Sealed/ Gel, AGM/LiFePO4 or Flooded.\n\nTo confirm the battery profile, press and hold the A Button for 3 seconds.\n\nNon-volatile memory: Any settings made on the GP-PWM-10-FM will be saved even when the power has been disconnected from the controller.\n\nRefer to the Battery Charge Profile Chart below for details on each profile.\n\n## 7.2 SETTING THE BATTERY CHARGING PROFILE\n\ngpelectric.com | [page 9]", "images_metadata": [], "tables_metadata": [], "diagrams_metadata": [{"diagram_id": "46547e6ee2d245a8911eb45730caf1a5", "diagram_type": "wiring_diagram", "summary": "A wiring diagram showing the connections between a battery bank, fuse, solar array, and a solar controller. The battery bank is connected to the controller through a fuse. The solar array is directly connected to the controller. The controller has four terminals labeled BATTERY +, BATTERY -, SOLAR +, and SOLAR -.", "actual_reference": "", "is_complete": true, "page_num": 8, "cross_page_context": null}, {"diagram_id": "3fc4224064ab40edb6df1edd445c3cd0", "diagram_type": "device_interface", "summary": "A diagram of a solar controller interface. It shows a display screen with \"13.8v\" displayed, indicating voltage. There are two buttons labeled A and B below the screen. The device is labeled as \"10 AMP PWM SOLAR CONTROLLER\" at the top. It also includes a USB port icon and a Go Power! logo.", "actual_reference": "", "is_complete": true, "page_num": 8, "cross_page_context": null}], "processing_time": 27.***************}
-----image_8__end-----
-----layout_info_image_10__start-----
LAYOUT ANALYSIS:

- Headings: 
  Level 1: OPERATING INSTRUCTIONS

- Section Context: Operating Instructions for a solar controller device

- Content Role: Main instructional content explaining how to use the device

- Layout Structure: Single column layout with three separate diagrams vertically aligned

- Elements: 
  - Text blocks
  - 3 Diagrams of solar controller display
  - Company logos (Dometic, Go Power!)

- Visual Elements:
  * Charts: None
  * Diagrams: 3 solar controller display diagrams showing different screens
  * Tables: None
  * Images: Company logos

- Text Formatting: 
  - Sparse text, mostly short explanatory captions
  - Bulleted instructions under each diagram

- Visual Hierarchy: 
  - Large main heading at top
  - Three equally-sized diagrams as focal points
  - Explanatory text beneath each diagram
  - Page number and website at bottom

- Context Summary: This page provides operating instructions for a Dometic 10 AMP PWM solar controller. It shows three different display screens and explains how to interpret the information shown on each screen using the device's B button.
-----layout_info_image_10__end-----
-----layout_info_image_11__start-----
LAYOUT ANALYSIS:
- Headings: 
  Level 1: OPERATING INSTRUCTIONS
  Level 2: 7.5 RESETTING THE AMPERE HOURS CHARGED
  Level 2: 7.6 ERRORS
  Level 3: OVER VOLTAGE
  Level 3: LOW VOLTAGE

- Section Context: Operating Instructions for a Dometic solar controller

- Content Role: Instructional content providing details on resetting and error handling

- Layout Structure: Single column layout with multiple subsections

- Elements: 
  - Text blocks
  - 3 diagrams of solar controller displays
  - Page number

- Visual Elements:
  * Charts: None
  * Diagrams: 3 solar controller display diagrams
  * Tables: None
  * Images: Dometic logo

- Text Formatting: 
  - Sparse text density
  - Short paragraphs under each diagram
  - Numbered sections and subsections

- Visual Hierarchy: 
  - Main heading at top
  - Subheadings for each section
  - Diagrams prominently displayed with explanatory text below

- Context Summary: This page provides operating instructions for a Dometic solar controller, specifically focusing on resetting ampere hours charged and handling error conditions like over voltage and low voltage. The content is primarily instructional, using diagrams to illustrate the controller's display in different scenarios.
-----layout_info_image_11__end-----
-----layout_info_image_12__start-----
LAYOUT ANALYSIS:
- Headings: 
  1. 8. DISPLAY SYMBOLS (Main heading)
  2. SYMBOLS (Subheading)
  3. OTHER SYMBOLS (Subheading)

- Section Context: Display Symbols section of a manual or guide

- Content Role: Reference information for understanding display symbols

- Layout Structure: Single column layout with two tables

- Elements: 
  * Text blocks
  * Two tables
  * Symbols/icons

- Visual Elements:
  * Charts: None
  * Diagrams: None
  * Tables: 
    - Table 1: Symbols and their indicators
    - Table 2: Other symbols and their meanings
  * Images: Various symbols and icons within the tables

- Text Formatting: 
  - Sparse text, primarily in table format
  - Bold text used for headings and table headers
  - Regular text for descriptions

- Visual Hierarchy:
  - Main heading at the top
  - Two distinct tables separated by subheadings
  - Symbols aligned in left column, descriptions in right column
  - Clear separation between different types of symbols

- Context Summary: This page provides a reference guide for display symbols used in a Dometic product, likely an electrical device or battery system. It explains the meaning of various icons that may appear on the product's display, including indicators for time, battery status, and charging conditions.
-----layout_info_image_12__end-----
-----image_9__start-----
{"image_index": 9, "markdown_content": "# OPERATING INSTRUCTIONS\n\n## 7.3 BATTERY CHARGING PROFILE CHART\n\n{{485130aaa5b148c084b3ed5edfc1f3e4}}\n\nIf a charging cycle is unable to complete in a single day, it will continue the following day. The terms SEALED/GEL, AGM, FLOODED and LFP are generic battery designations. Choose the charging profile that works best with your battery manufacturer's recommendations.\n\n> **Note**: If PV power is insufficient or too many loads are drawing power from the battery, the controller will not be able to charge the battery to the target charging voltage.\n\nAuto Equalize: The GP-PWM-10-FM has an automatic equalize feature that will charge and recondition your batteries at least once a month at a higher voltage to ensure that any excess sulfation is removed.\n\n> **Note**: This mode should not be entered unless you are using a flooded battery.\n\n## 7.4 VIEWING THE CONTROLLER DISPLAY INFORMATION\n\nTo toggle between Battery Voltage, PV Charging Current, Battery State of Charge (SOC), and ampere hours charged since last reset, press the B Button.\n\n{{ac1a71d9ae4f4b05b2b7463a15a2e55f}}\n\n[page 10] | gpelectric.com", "images_metadata": [], "tables_metadata": [{"table_id": "485130aaa5b148c084b3ed5edfc1f3e4", "summary": "The table shows battery charging profiles for different battery types (SEALED/GEL, AGM, FLOODED, LFP). It includes Float Charge, Bulk/Absorption Charge, and Equalization Charge voltages at 25\u00b0C for each battery type. It also includes Absorption Charge voltage for LiFePO4 batteries and Float Charge voltage for LiFePO4 batteries.", "actual_reference": "", "is_complete": true, "page_num": 9, "cross_page_context": null}], "diagrams_metadata": [{"diagram_id": "ac1a71d9ae4f4b05b2b7463a15a2e55f", "diagram_type": "diagram", "summary": "The diagram shows a 10 AMP PWM SOLAR CONTROLLER display. It features a digital screen showing \"13.8v\" and two buttons labeled A and B. The B button is highlighted with a hand icon, indicating it should be pressed. The diagram also includes text explaining to push the B Button to show the battery voltage, and that the Icons Displayed are Battery SOC and Volt Symbol (V).", "actual_reference": "", "is_complete": true, "page_num": 9, "cross_page_context": null}], "processing_time": 30.11226987838745}
-----image_9__end-----
-----image_12__start-----
{"image_index": 12, "markdown_content": "# 8. DISPLAY SYMBOLS\n\n## SYMBOLS\n\n{{1d1ec91ebbd241a289185bb8b897f927}}\n\n## OTHER SYMBOLS\n\n{{82ddadb5d3e64d6aaf19ee94e743271c}}\n\ngpelectric.com | [page 13]", "images_metadata": [], "tables_metadata": [{"table_id": "1d1ec91ebbd241a289185bb8b897f927", "summary": "A table showing display symbols and their corresponding indicators. It includes symbols for Day Time: PV Charge Current, Night Time, Battery Voltage, Battery State of Charge, Sealed/Gel, AGM/LFP, and Flooded. Each symbol is represented by an icon in the left column, with its meaning in the right column.", "actual_reference": "", "is_complete": true, "page_num": 12, "cross_page_context": null}, {"table_id": "82ddadb5d3e64d6aaf19ee94e743271c", "summary": "A table showing additional symbols and their meanings. It includes a USB symbol indicating 'USB charger ON (when charger is OFF, no symbol will show)', 'LOW' indicating 'Battery voltage is lower than 11.0V', and 'Whole display will start to blink' indicating 'Battery voltage > 15.5V'.", "actual_reference": "", "is_complete": true, "page_num": 12, "cross_page_context": null}], "diagrams_metadata": [], "processing_time": 20.55580186843872}
-----image_12__end-----
-----image_10__start-----
{"image_index": 10, "markdown_content": "# OPERATING INSTRUCTIONS\n\n{{a88a71f80fae46e482d4df6fb392b242}}\n\nPush the B Button to show the PV charging current.\n\nIcons Displayed: Ampere Symbol (A), Battery SOC\n\n{{6de4033f6e0e41e0b2d4dd8000db1e6c}}\n\nPush the B Button to show the battery state of charge (shown as a percentage).\n\nIcons Displayed: Battery SOC, Percent Symbol (%)\n\nA value of 100% will only be displayed after a Boost or Equalize charge completes.\n\n{{87e2e5da62fe41f8b4660d20ff1ebca9}}\n\nPush the B Button to show the number of amp hours charged since the last reset.\n\nIcons Displayed: Amp hours charged, Amp hour symbol (Ah) or kiloamp hour symbol (kAh)\n\ngpelectric.com | [page 11]", "images_metadata": [], "tables_metadata": [], "diagrams_metadata": [{"diagram_id": "a88a71f80fae46e482d4df6fb392b242", "diagram_type": "diagram", "summary": "A diagram of a 10 AMP PWM SOLAR CONTROLLER display. It shows a digital screen displaying '7.6' with an 'A' (Ampere) symbol. There are two buttons labeled 'A' and 'B' below the screen, with a hand icon pressing the 'B' button. The Go Power! logo is visible on the controller.", "actual_reference": "", "is_complete": true, "page_num": 10, "cross_page_context": null}, {"diagram_id": "6de4033f6e0e41e0b2d4dd8000db1e6c", "diagram_type": "diagram", "summary": "A diagram of a 10 AMP PWM SOLAR CONTROLLER display. It shows a digital screen displaying '90' with a '%' (percent) symbol. There are two buttons labeled 'A' and 'B' below the screen, with a hand icon pressing the 'B' button. The Go Power! logo is visible on the controller.", "actual_reference": "", "is_complete": true, "page_num": 10, "cross_page_context": null}, {"diagram_id": "87e2e5da62fe41f8b4660d20ff1ebca9", "diagram_type": "diagram", "summary": "A diagram of a 10 AMP PWM SOLAR CONTROLLER display. It shows a digital screen displaying '45.3' with an 'Ah' (Amp hour) symbol. There are two buttons labeled 'A' and 'B' below the screen, with a hand icon pressing the 'B' button. The Go Power! logo is visible on the controller.", "actual_reference": "", "is_complete": true, "page_num": 10, "cross_page_context": null}], "processing_time": 24.123166799545288}
-----image_10__end-----
-----layout_info_image_13__start-----
LAYOUT ANALYSIS:

- Headings: 
  1. DISPLAY SYMBOLS (main heading)
  2. BATTERY STATE OF CHARGE (subheading)
  3. USB CHARGING (subheading)

- Section Context: Display Symbols and USB Charging section of a manual

- Content Role: Informational/instructional content explaining battery symbols and USB charging functionality

- Layout Structure: Single column layout with a table in the middle and text sections above and below

- Elements: 
  - Text blocks
  - Table (Battery State of Charge)
  - Icons/symbols (battery charge indicators)
  - Warning symbol

- Visual Elements:
  * Charts: None
  * Diagrams: None
  * Tables: Battery State of Charge table
  * Images: Battery icons, warning symbol

- Text Formatting: 
  - Sparse text with clear paragraph breaks
  - Bulleted list in USB charging section
  - Equation for SOC (State of Charge) calculation

- Visual Hierarchy: 
  - Large bold headings for main sections
  - Table with clear divisions between rows and columns
  - Visual battery icons to represent charge levels
  - Warning symbol to draw attention to important information

- Context Summary: This page provides information on how to interpret battery charge symbols on a Dometic device display and explains the USB charging capabilities of the GP-PWM-10-FM model. It's likely part of a user manual or technical guide for a portable power-related product.
-----layout_info_image_13__end-----
-----image_11__start-----
{"image_index": 11, "markdown_content": "# OPERATING INSTRUCTIONS\n\n## 7.5 RESETTING THE AMPERE HOURS CHARGED\n\n{{f8337a77a13e42869c64707ec35380da}}\n\nTo reset the count of ampere hours charged, toggle to the ampere hours charged. Press and hold the A Button for 6 seconds to reset the counter to zero.\n\n## 7.6 ERRORS\n\n### OVER VOLTAGE\n\n{{4a1c3cf77c74404599865eab10282296}}\n\nIf the GP-PWM-10-FM experiences a battery over voltage (15.5V), the controller will stop operating, and the display will begin to flash with all icons. The controller will resume operating when the error is cleared.\n\nIcons Displayed: All symbols\n\n### LOW VOLTAGE\n\n{{1b87d64ace1b4e9d88722d79588c7672}}\n\nIf the battery voltage reaches 11 volts, the battery SOC symbol will show the text \"LOW\" beneath it. The controller will continue operating in this condition and will only stop operating if the voltage drops below 9 volts.\n\nIcons Displayed: Battery SOC Symbol, LOW\n\n[page 12] | gpelectric.com", "images_metadata": [], "tables_metadata": [], "diagrams_metadata": [{"diagram_id": "f8337a77a13e42869c64707ec35380da", "diagram_type": "diagram", "summary": "A diagram of a 10 AMP PWM SOLAR CONTROLLER display. It shows a digital screen displaying \"0.0 Ah\". Below the screen are two buttons labeled A and B, with a finger icon pointing to button A.", "actual_reference": "", "is_complete": true, "page_num": 11, "cross_page_context": null}, {"diagram_id": "4a1c3cf77c74404599865eab10282296", "diagram_type": "diagram", "summary": "A diagram of a 10 AMP PWM SOLAR CONTROLLER display. It shows a digital screen displaying \"8.8.8 kAh\" with various icons lit up, including a sun, battery, and charging symbols. Below the screen are two buttons labeled A and B.", "actual_reference": "", "is_complete": true, "page_num": 11, "cross_page_context": null}, {"diagram_id": "1b87d64ace1b4e9d88722d79588c7672", "diagram_type": "diagram", "summary": "A diagram of a 10 AMP PWM SOLAR CONTROLLER display. It shows a digital screen with a battery icon and the text \"LOW\" visible. Below the screen are two buttons labeled A and B.", "actual_reference": "", "is_complete": true, "page_num": 11, "cross_page_context": null}], "processing_time": 26.6909236907959}
-----image_11__end-----
-----layout_info_image_14__start-----
LAYOUT ANALYSIS:
- Headings: 
  Level 1: 10. FREQUENTLY ASKED QUESTIONS

- Section Context: Frequently Asked Questions section of a manual or guide

- Content Role: Providing answers to common user questions and troubleshooting information

- Layout Structure: Single column layout with a table at the bottom

- Elements: 
  * Text blocks
  * Table (1)
  * Company logos (2)

- Visual Elements:
  * Charts: None
  * Diagrams: None
  * Tables: 1 warning/connection table
  * Images: 2 company logos (Go Power! and Dometic)

- Text Formatting: 
  * Mostly paragraph text with varying density
  * Bold text used for question headings
  * Some italicized text for emphasis
  * Bulleted list in one answer

- Visual Hierarchy: 
  * Large bold heading at the top
  * Questions in bold serve as subheadings
  * Answers in regular text below each question
  * Table at the bottom for structured information

- Context Summary: This page contains frequently asked questions about a battery system, likely for an RV or marine application. It covers topics such as battery maintenance, charging issues, and system behavior, providing technical support to users.
-----layout_info_image_14__end-----
-----layout_info_image_15__start-----
LAYOUT ANALYSIS:
- Headings: 
  Level 1: 11. TROUBLESHOOTING PROBLEMS
  Level 2: 11.1 ERRORS
  Level 2: 11.2 PROBLEMS WITH VOLTAGE

- Section Context: Troubleshooting section of a manual

- Content Role: Main content - providing troubleshooting instructions

- Layout Structure: Single column layout with clear section breaks

- Elements: Text blocks, Note box

- Visual Elements:
  * Charts: None
  * Diagrams: None
  * Tables: None
  * Images: Company logo (Dometic) at the top

- Text Formatting: 
  - Dense text in paragraphs
  - Bulleted lists for steps and possible causes
  - Bold text for emphasis on key terms and headings
  - Italicized text for additional notes

- Visual Hierarchy: 
  - Clear heading structure with numbered sections
  - Bolded subheadings for different issues (e.g., "Display Reading: Blank")
  - Indented paragraphs for detailed instructions
  - Note box for important information

- Context Summary: This page is from a troubleshooting guide for a Dometic product, likely an electrical system or device. It provides detailed instructions for diagnosing and resolving issues related to errors and voltage problems.
-----layout_info_image_15__end-----
-----layout_info_image_16__start-----
LAYOUT ANALYSIS:

- Headings: 
  1. TROUBLESHOOTING PROBLEMS (Main heading)
  2. 11.3 PROBLEMS WITH CURRENT (Subheading)

- Section Context: Troubleshooting section of a manual or guide

- Content Role: Main content providing troubleshooting information for specific problems

- Layout Structure: Single-column layout with clear section breaks

- Elements: Text blocks, note box

- Visual Elements:
  * Charts: None
  * Diagrams: None
  * Tables: None
  * Images: Company logo (Dometic) in top right corner

- Text Formatting: 
  - Dense text in paragraph format
  - Bulleted lists for steps and possible causes
  - Bold text for subheadings and important terms
  - Italicized text for specific conditions (e.g., "Current Reading: 0 A")
  - Note box with highlighted information

- Visual Hierarchy: 
  - Clear distinction between main heading, subheading, and body text
  - Use of white space to separate different problem scenarios
  - Bold text and bulleted lists to emphasize key points

- Context Summary: This page is from a troubleshooting guide for a Dometic product, specifically addressing problems with current. It provides detailed steps for diagnosing and resolving issues related to solar array and controller connections, offering solutions for different scenarios such as no current or less than expected current.
-----layout_info_image_16__end-----
-----image_13__start-----
{"image_index": 13, "markdown_content": "# DISPLAY SYMBOLS\n\n## BATTERY STATE OF CHARGE\n\n{{c1b95f5f0b2f45fdbcc436e05b5ab582}}\n\n## USB CHARGING\n\nThe GP-PWM-10-FM offers a standard USB connector for delivering 5.0 VDC to small mobile appliances such as cell phones, tablets or small music players. This charging port is capable of supplying up to 1500 mA of current.\n\nRemove the rubber cover of the USB terminal to access the terminal.\n\nThe USB charging port is always active when the USB symbol appears on the display.\n\nThe controller disables the USB charger automatically if the battery voltage drops below 11.0 VDC. If there is enough current from the PV panel/array available to charge the Battery to above 12.8 VDC, the USB terminal will be enabled again.\n\n{{83754eeb90d1405c82112f56ec04360a}} WARNING: Do not connect the charging device anywhere else! USB-Negative contact is connected to battery negative.\n\n[page 14] | gpelectric.com", "images_metadata": [{"image_id": "83754eeb90d1405c82112f56ec04360a", "summary": "A warning symbol (yellow triangle with exclamation mark) indicating a cautionary message about USB charging connections.", "actual_reference": "", "is_complete": true, "page_num": 13, "cross_page_context": null}], "tables_metadata": [{"table_id": "c1b95f5f0b2f45fdbcc436e05b5ab582", "summary": "A table titled \"BATTERY STATE OF CHARGE\" with two columns: \"SYMBOLS\" and \"BATTERY VOLTAGE\". The SYMBOLS column shows 5 battery icons with varying levels of charge, from full to empty. The BATTERY VOLTAGE column provides corresponding voltage ranges or conditions for each symbol. Additionally, it includes percentage indicators (100%, 90%, and 0%) with their respective voltage conditions, and an equation for calculating State of Charge (SOC).", "actual_reference": "", "is_complete": true, "page_num": 13, "cross_page_context": null}], "diagrams_metadata": [], "processing_time": 25.57955765724182}
-----image_13__end-----
-----layout_info_image_17__start-----
LAYOUT ANALYSIS:

- Headings: 
  1. TROUBLESHOOTING PROBLEMS (Main heading)
  2. 11.4 CONTROLLER FLASHING (Subheading)

- Section Context: Troubleshooting section of a manual or guide

- Content Role: Detailed explanation of a specific troubleshooting issue and its solutions

- Layout Structure: Single-column layout with clear paragraph separation

- Elements: Text blocks, company logo

- Visual Elements:
  * Charts: None
  * Diagrams: None
  * Tables: None
  * Images: Company logo (Dometic)

- Text Formatting: 
  - Moderate text density
  - Clear paragraph breaks
  - Bold text for headings and key terms
  - Numbered and bulleted lists for steps and information

- Visual Hierarchy: 
  - Large bold main heading at the top
  - Subheading in smaller bold font
  - Body text in standard font
  - Key terms (Possible Cause, Remedy) in bold to separate sections

- Context Summary: This page provides troubleshooting information for a controller flashing issue in what appears to be a Dometic product manual. It details the possible causes of the problem and provides step-by-step instructions for resolving the issue.
-----layout_info_image_17__end-----
-----image_15__start-----
{"image_index": 15, "markdown_content": "# 11. TROUBLESHOOTING PROBLEMS\n\n## How to Read this Section\n\nTroubleshooting Problems is split into three sub-sections, grouped by symptoms involving key components. Components considered irrelevant in a diagnosis are denoted 'Not Applicable' (N/A). A multimeter or voltmeter may be required for some procedures listed.\n\n> It is imperative all electrical precautions stated in the Warning Section and outlined in the Installation Section are followed. Even if it appears the system is not functioning, it should be treated as a fully functioning system generating live power.\n\n## 11.1 ERRORS\n\n### Display Reading: Blank\nTime of Day: Daytime/Nighttime\n\n**Possible Causes:**\nBattery or fuse connection and/or solar array connection\n(Daytime only) or battery or fuse connection (Nighttime only).\n\n**How to tell:**\n1. Check the voltage at the controller battery terminals with a voltmeter and compare with a voltage reading at the battery terminals.\n2. If there is no voltage reading at the controller battery terminals, the problem is in the wiring between the battery and the controller. If the battery voltage is lower than 6 volts the controller will not function.\n3. For the solar array, repeat steps 1 and 2 substituting all battery terminals with solar array terminals.\n\n**Remedy:**\nCheck all connections from the controller to the battery including checking for correct wire polarity. Check that all connections are clean, tight, and secure. Ensure the battery voltage is above 6 volts.\n\n### Display Reading: Nighttime\nTime of Day: Daytime\n\n**Possible Causes:**\nPanel is covered by something; PV panel is too dirty to supply a high enough voltage to charge the battery; PV panel is not connected.\n\n**Remedy:**\nCheck the panel and to ensure it is not obscured. Clean the panel if it is dirty. Check that PV cables are connected to the controller.\n\n## 11.2 PROBLEMS WITH VOLTAGE\n\n### Voltage Reading: Inaccurate\nTime of Day: Daytime/Nighttime\n\n**Possible Causes:**\nExcessive voltage drop from batteries to controller due to loose connections, small wire gauge or both.\n\n**How to tell:**\n1. Check the voltage at the controller battery terminals with a voltmeter and compare with the voltage reading at the battery terminals.\n2. If there is a voltage discrepancy of more than 0.5 V, there is an excessive voltage drop.\n\n**Remedy:**\nCheck all connections from the controller to the battery including checking for correct wire polarity. Check that all connections are clean, tight, and secure. Shorten the distance from the controller to battery or obtain larger gauge wire. It is also possible to double up the existing gauge wire (i.e. two wire runs) to simulate a larger gauge wire.\n\n{{b64a669fed394589a18c63363bf43406}}", "images_metadata": [{"image_id": "b64a669fed394589a18c63363bf43406", "summary": "The image shows a logo for Dometic, a company that appears to be the manufacturer of the product this troubleshooting guide is for. The logo is placed at the top of the page.", "actual_reference": "", "is_complete": true, "page_num": 15, "cross_page_context": null}], "tables_metadata": [], "diagrams_metadata": [], "processing_time": 32.683717012405396}
-----image_15__end-----
-----image_14__start-----
{"image_index": 14, "markdown_content": "# 10. FREQUENTLY ASKED QUESTIONS\n\nBefore a problem is suspected with the system, read this section. There are numerous events that may appear as problems but are in fact perfectly normal. Please visit gpelectric.com for the most up-to-date FAQs.\n\n**It seems like my flooded batteries are losing water over time.**\n\nFlooded batteries may need to have distilled water added periodically to replace fluid loss during charging. Excessive water loss during a short period of time indicates the possibility of overcharging or aging batteries.\n\n**When charging, my flooded batteries are emitting gas.**\n\nDuring charging, hydrogen gas is generated within the battery. The gas bubbles stir the battery acid, allowing it to receive a fuller state of charge.\nImportant: Ensure batteries are in a well-ventilated space.\n\n**My voltmeter shows a different reading than the GP-PWM-10-FM display.**\n\nThe meter value on the GP-PWM-10-FM display is an approximate reading intended for indication purposes only. There is an approximate 0.1 volt inherent error present that may be accentuated when compared with readings from another voltmeter.\n\nThere may be a slight difference between the battery voltage displayed on the GP-PWM-10-FM display and the battery voltage measured at the battery terminals. When troubleshooting using a voltmeter, check both the battery voltage at the GP-PWM-10-FM controller terminals and battery voltage at the battery terminals. If a difference of more than 0.5 volts is noted, this indicates a large voltage drop possibly caused by loose connections, long wire runs, small wire gauge, faulty wiring, a faulty voltmeter, or all the above. Consult the Suggested Minimum Wire Gauge chart in Section 5 for wiring suggestions and check all connections.\n\n**What causes a warning signal and when are the warnings triggered?**\n\n{{8628eb726bcf44748375f427899895cf}}\n\n**Why does the battery SOC% never reach 100%?**\n\nA 100% value will only appear after a 2 hour Boost or Equalize charge has completed. The charge voltage must be maintained for an extended period of time to replenish the energy in the battery bank back to its rated capacity.\n\nIf the charge voltage cannot be maintained continuously, then the actual time it takes to complete Boost or Equalize charging may take much longer than 2 hours, even more than 1 day.\n\nIf loads are consuming more power than the solar panels can supply, then the battery bank cannot be charged to 100%.\n\n{{b80e0a41adbd49a7989a56edcc22a69d}}\n{{4ad2362ce1434b5e89c25250f1aa7213}}\n\ngpelectric.com | [page 15]", "images_metadata": [{"image_id": "b80e0a41adbd49a7989a56edcc22a69d", "summary": "Logo of Go Power!, a company likely associated with solar power or battery systems.", "actual_reference": "", "is_complete": true, "page_num": 14, "cross_page_context": null}, {"image_id": "4ad2362ce1434b5e89c25250f1aa7213", "summary": "Logo of Dometic, another company likely associated with power systems or RV/marine equipment.", "actual_reference": "", "is_complete": true, "page_num": 14, "cross_page_context": null}], "tables_metadata": [{"table_id": "8628eb726bcf44748375f427899895cf", "summary": "A table with four columns: CONNECTION, WARNING, NOTES, and LCD. It shows warning signals for battery and PV reverse polarity. For both cases, the WARNING is \"POL\" on LCD and constant audible alarm. For PV reverse polarity, there's a NOTE that the battery must be connected with correct polarity. The LCD column shows \"POL\" for both cases.", "actual_reference": "", "is_complete": true, "page_num": 14, "cross_page_context": null}], "diagrams_metadata": [], "processing_time": 36.143025636672974}
-----image_14__end-----
-----image_17__start-----
{"image_index": 17, "markdown_content": "# TROUBLESHOOTING PROBLEMS\n\n## 11.4 CONTROLLER FLASHING\n\n**Possible Cause:**\nThis behavior is usually the controller dealing with a very high C or voltage rate (Above 15.5 volts). Even though the controller can handle up to 30A, if the battery capacity is too small for the panel input current. The voltage shoots up too high, too quickly, tripping the high voltage flashing. Solution increase battery capacity.\n\nCan also be caused by an unregulated converter or alternator, in the system that is putting current to the batteries at the same time.\n\n**Remedy:**\nThe solution here is to unplug shore power and reset the controller, which can be done in two ways:\n\n1. **Soft Reset** - This is done by holding down all 4 buttons on the front of the controller for 15 seconds. If this does not work or you do not have a 4-button controller, a hard reset is required.\n1. **Hard Reset** - Remove all 4 wires from the back of the controller for 15-20 minutes, then reconnect the wires. Determine if this clears the error state.\n\nIf the problem was \"fixed,\" then it was because the user started using loads which divert some of the input current because the panels became dusty or shaded, or because there was less sunlight.\n\n{{65f26cc8663a44119b2b4b0af88112b0}}", "images_metadata": [{"image_id": "65f26cc8663a44119b2b4b0af88112b0", "summary": "The image shows the Dometic company logo, which appears to be a stylized word \"DOMETIC\" in capital letters. Above the logo is a curved oval shape with the text \"Go Power!\" inside it.", "actual_reference": "", "is_complete": true, "page_num": 17, "cross_page_context": null}], "tables_metadata": [], "diagrams_metadata": [], "processing_time": 23.917908906936646}
-----image_17__end-----
-----image_16__start-----
{"image_index": 16, "markdown_content": "# TROUBLESHOOTING PROBLEMS\n\n## 11.3 PROBLEMS WITH CURRENT\n\n### Current Reading: 0 A\nTime of Day: Daytime, clear sunny skies\n\n**Possible Cause:**\nCurrent is being limited below 1 Amp as per normal operation or poor connection between solar array and controller.\n\n**How to tell:**\n1. The State of Charge (SOC) screen is close to 100% and the Sun and Battery icon are present with an arrow between.\n2. With the solar array in sunlight, check the voltage at the controller solar array terminals with a voltmeter.\n3. If there is no reading at the controller solar array terminals, the problem is somewhere in the wiring from the solar array to the controller.\n\n**Remedy:**\nCheck all connections from the controller to the array including checking for correct wire polarity. Check that all connections are clean, tight, and secure. Continue with the solutions below for additional help on low current readings.\n\n### Current Reading: Less than expected\nTime of Day: Daytime, clear sunny skies\n\n**Possible Causes:**\n1. Current is being limited below 1 Amp as per normal operation.\n2. Incorrect series/parallel configuration and/or wiring connections and/or wire gauge.\n3. Dirty or shaded module or lack of sun.\n4. Blown diode in solar module when two or more modules are connected in parallel.\n\n**How to tell:**\n1. Battery State of Charge screen is close to 100% and the Sun and Battery icon are present with an arrow in between.\n2. Check that the modules and batteries are configured correctly. Check all wiring connections.\n3. Modules look dirty, overhead object is shading modules or it is an overcast day in which a shadow cannot be cast.\n\n> **Note:** Avoid any shading no matter how small. An object as small as a broomstick held across the solar module may cause the power output to be reduced. Overcast days may also cut the power output of the module\n\n4. Disconnect one or both array wires from the controller. Take a voltage reading between the positive and negative array wire. A single 12 volt module should have an open circuit voltage between 17 and 22 volts. If you have more than one solar module, you will need to conduct this test between the positive and negative terminals of each module junction box with either the positive or the negative wires disconnected from the terminal.\n\n**Remedy:**\n1. Reconnect in correct configuration. Tighten all connections. Check wire gauge and length of wire run. Refer to Suggested Minimum Wire Gauge in Section 5.\n\n2. Clean modules, clear obstruction or wait for conditions to clear.\n\n3. If the open circuit voltage of a non-connected 12 volt module is lower than the manufacturer's specifications, the module may be faulty. Check for blown diodes in the solar module junction box, which may be shorting the power output of module.\n\n{{2a071aba0308438692b2546b6acd709f}}", "images_metadata": [{"image_id": "2a071aba0308438692b2546b6acd709f", "summary": "The image shows the Dometic logo in the top right corner. It's a stylized word \"DOMETIC\" with a curved line above it and the text \"Go Power!\" inside an oval shape.", "actual_reference": "", "is_complete": true, "page_num": 16, "cross_page_context": null}], "tables_metadata": [], "diagrams_metadata": [], "processing_time": 36.70495629310608}
-----image_16__end-----
-----layout_info_image_19__start-----
LAYOUT ANALYSIS:
- Headings: No clear headings visible in this image
- Section Context: Unable to determine from this image
- Content Role: Footer/copyright information
- Layout Structure: Single column layout at bottom of page
- Elements: Text block (copyright and contact information)
- Visual Elements:
  * Charts: None visible
  * Diagrams: None visible
  * Tables: None visible
  * Images: One logo image (Go Power! logo)
- Text Formatting: Small, dense text block with company information
- Visual Hierarchy: Logo emphasized visually, followed by text information
- Context Summary: This appears to be the footer of a technical manual or product documentation for Go Power! and Dometic products. It contains copyright information, company contact details, and product identification.
-----layout_info_image_19__end-----
-----layout_info_image_18__start-----
LAYOUT ANALYSIS:
- Headings: 
  Level 1: 12. LIMITED WARRANTY
  Level 2: 12.1 REPAIR AND RETURN INFORMATION

- Section Context: Limited Warranty section of a product manual

- Content Role: Warranty information and repair procedures

- Layout Structure: Single column layout with left-aligned text

- Elements: Text blocks, company logos

- Visual Elements:
  * Charts: None
  * Diagrams: None
  * Tables: None
  * Images: Two company logos (Go Power! and Dometic)

- Text Formatting: 
  - Main heading in large bold font
  - Subheading in bold
  - Body text in regular font
  - Bulleted lists for warranty exclusions and repair steps

- Visual Hierarchy: 
  - Clear distinction between headings and body text
  - Use of white space to separate sections
  - Bulleted lists to highlight key points

- Context Summary: This page provides detailed information about the limited warranty for a GP-PWM-10 product, including what is covered, exclusions, and steps for repair and return. It appears to be part of a larger product manual or guide.
-----layout_info_image_18__end-----
-----image_19__start-----
{"image_index": 19, "markdown_content": "{{d434935fb3514a59a7bed576a381350d}}\n\n\u00a9 2021 Go Power!\n\nWorldwide Technical Support and Product Information gpelectric.com\nGo Power! | Dometic\n201-710 Redbrick Street Victoria, BC, V8T 5J3\nTel: **************\n\nManual_GP-PWM-10-FM", "images_metadata": [{"image_id": "d434935fb3514a59a7bed576a381350d", "summary": "The image shows two logos side by side. On the left is the Go Power! logo, which appears to be a stylized 'GP' in a circular design. On the right is the Dometic logo, which is the word 'DOMETIC' in all capital letters with a distinctive arrow-like symbol above it.", "actual_reference": "", "is_complete": true, "page_num": 19, "cross_page_context": null}], "tables_metadata": [], "diagrams_metadata": [], "processing_time": 14.857047080993652}
-----image_19__end-----
-----image_18__start-----
{"image_index": 18, "markdown_content": "# 12. LIMITED WARRANTY\n\nGo Power! warrants the GP-PWM-10 for a period of five (5) years from the date of shipment from its factory. This warranty is valid against defects in materials and workmanship for the five(5) year warranty period. It is not valid against defects resulting from, but not limited to:\n\n- Misuse and/or abuse, neglect or accident\n- Exceeding the unit's design limits\n- Improper installation, including, but not limited to, improper environmental protection and improper hook-up\n- Acts of God, including lightning, floods, earthquakes, fire, and high winds\n- Damage in handling, including damage encountered during shipment\n\nThis warranty shall be considered void if the warranted product is in any way opened or altered. The warranty will be void if any eyelet, rivets, or other fasteners used to seal the unit are removed or altered, or if the unit's serial number is in any way removed, altered, replaced, defaced, or rendered illegible.\n\n## 12.1 REPAIR AND RETURN INFORMATION\n\nVisit www.gpelectric.com to read the \"frequently asked questions\" section of our website to troubleshoot the problem. If trouble persists:\n\n1. Fill out our online Contact Us form or Live Chat with us\n2. Email <EMAIL>\n3. Return defective product to place of purchase\n\n{{d0fd745f606348db9a7535704b47a221}}\n{{cd4eed3867d444a795393ed7f1dda0bc}}\n\ngpelectric.com | [page 19]", "images_metadata": [{"image_id": "d0fd745f606348db9a7535704b47a221", "summary": "Logo of Go Power!, a company that appears to be the manufacturer of the product described in the warranty.", "actual_reference": "", "is_complete": true, "page_num": 18, "cross_page_context": null}, {"image_id": "cd4eed3867d444a795393ed7f1dda0bc", "summary": "Logo of Dometic, which appears to be associated with Go Power! or the product described.", "actual_reference": "", "is_complete": true, "page_num": 18, "cross_page_context": null}], "tables_metadata": [], "diagrams_metadata": [], "processing_time": 23.533806085586548}
-----image_18__end-----
-----enhancement_group_0_2__start-----
{"actual_references_found": {"images": [], "tables": [], "diagrams": []}, "headers_and_footers": {"headers": ["10AMP PWM SOLAR CONTROLLER", "User Manual", "GP-PWM-10-FM (FLUSH MOUNT - LITHIUM COMPATIBLE)", "CONTENTS"], "footers": ["gpelectric.com", "\u00a9 2021 Go Power!", "Worldwide Technical Support and Product Information gpelectric.com", "Go Power! | Dometic", "201-710 Redbrick Street Victoria, BC, V8T 5J3", "Tel: **************", "Manual_GP-PWM-10-FM", "page"]}, "enhanced_images": {"850bdf6b00434d80b02b4d60d34e6dbe": {"summary": "This image shows the front panel of the 10 AMP PWM Solar Controller (GP-PWM-10-FM) device. The controller has a black rectangular faceplate with a digital LCD display in the center. The display shows '0.0 V' and 'kAh', indicating it can show voltage and ampere-hour readings. There are icons for solar, battery, and load on the display, suggesting it monitors these aspects of a solar power system. Below the display are two buttons labeled A and B, likely for user interaction and settings adjustment. On the left side of the panel is a switch, possibly for power or mode selection. The Go Power! logo and website URL (gpelectric.com) are visible on the right side of the faceplate, branding the device.", "actual_reference": "", "is_complete": true, "cross_page_context": "This image appears on the first page of the user manual, introducing the physical appearance of the solar controller that the manual is about. It provides a visual reference for users to identify the device and its main interface elements."}, "7930945cf7d8410db0f18ea67199aa50": {"summary": "This image displays two logos associated with the solar controller product. The top logo is circular and contains the text 'Go Power!' inside, which is the primary brand of the solar controller. Below it is the Dometic logo, featuring the word 'DOMETIC' in a stylized font. This suggests that Go Power! is either a subsidiary or partner brand of Dometic, a well-known company in the mobile living market space.", "actual_reference": "", "is_complete": true, "cross_page_context": "These logos appear on the contents page of the manual, reinforcing the branding and corporate association of the product. The presence of both logos indicates a partnership or ownership relationship between Go Power! and Dometic in the production or distribution of this solar controller."}}, "enhanced_tables": {}, "enhanced_diagrams": {}}
-----enhancement_group_0_2__end-----
-----enhancement_group_3_5__start-----
{"actual_references_found": {"images": [], "tables": [], "diagrams": []}, "headers_and_footers": {"headers": ["INSTALLATION OVERVIEW", "WARNINGS"], "footers": ["gpelectric.com", "page"]}, "enhanced_images": {"b127aaf5568a42a2ab87f0378de9392d": {"summary": "This image shows two regulatory compliance symbols. The CE (Conformit\u00e9 Europ\u00e9enne) mark on the left indicates compliance with European Union standards. The RoHS (Restriction of Hazardous Substances) symbol on the right indicates compliance with the EU directive restricting the use of certain hazardous substances in electrical and electronic equipment. These symbols are relevant to the GP-PWM-10-FM Solar Controller discussed in the document, confirming its adherence to European safety and environmental standards.", "actual_reference": "", "is_complete": true, "cross_page_context": "These compliance symbols are part of the regulatory information section, following the technical specifications of the GP-PWM-10-FM Solar Controller."}, "a16da45ac9c74e0bb84581f03c56492a": {"summary": "The image shows the Dometic company logo, which is a stylized oval shape containing the text \"Go Power!\". This logo appears at the beginning of the warnings section, indicating that the safety information provided is associated with Dometic's Go Power! brand of solar controllers.", "actual_reference": "", "is_complete": true, "cross_page_context": "This logo is consistent with the branding seen on previous pages, reinforcing the product's association with Dometic and Go Power!."}, "d50830e7cfe24b7e9988e6c3e08ff933": {"summary": "This image depicts a warning symbol showing a lightning bolt inside a triangle. It indicates an electrical hazard and is used to emphasize the importance of disconnecting all power sources before installation or maintenance of the solar controller.", "actual_reference": "", "is_complete": true, "cross_page_context": "This symbol is part of a series of warning icons in the safety section, each highlighting different aspects of caution required when working with the GP-PWM-10-FM Solar Controller."}, "ea94cc0989a74433b0249ca4fd2883d2": {"summary": "The image shows a warning symbol featuring a battery with a lightning bolt. This symbol is used to highlight the safety precautions related to battery handling and the potential dangers associated with hydrogen gas production during battery charging.", "actual_reference": "", "is_complete": true, "cross_page_context": "This warning icon is part of the comprehensive safety instructions provided for the installation and use of the solar controller system."}, "7b4b52cefc0447fd90ed8bb5a21ef443": {"summary": "This warning symbol depicts a hand being struck by lightning, indicating the danger of electrical shock. It emphasizes the importance of proper wiring connections and the need to ensure all connections are tight and secure to prevent sparks and heat generation.", "actual_reference": "", "is_complete": true, "cross_page_context": "This symbol is part of the safety warnings section, highlighting specific risks associated with the electrical components of the solar controller system."}, "d74710c4dd3942e4b80129900b165ebb": {"summary": "The image shows a safety symbol of a person wearing safety goggles. This icon emphasizes the need for eye protection during installation and when working with the solar controller system, as part of the overall safety precautions.", "actual_reference": "", "is_complete": true, "cross_page_context": "This safety symbol is one of several in the warnings section, collectively emphasizing the importance of personal protective equipment and safe work practices."}, "d33fa9e0219d4511a6014400f3a393e3": {"summary": "This warning symbol shows an exclamation mark inside a triangle, indicating general caution or warning. In the context of the document, it's used to emphasize the importance of observing correct polarity at all times when connecting the solar controller.", "actual_reference": "", "is_complete": true, "cross_page_context": "This general warning symbol is used to highlight the critical nature of correct polarity in the installation process of the GP-PWM-10-FM Solar Controller."}, "91775d3ca90e450c965af56b7c032c05": {"summary": "The image depicts a warning symbol showing flames, indicating a fire hazard or high temperature warning. In the context of the document, this symbol is used to emphasize the importance of not exceeding the GP-PWM-10-FM's amp current and max voltage ratings to prevent potential fire hazards.", "actual_reference": "", "is_complete": true, "cross_page_context": "This warning symbol is the last in a series of safety icons, highlighting the potential risks of overloading the solar controller system."}}, "enhanced_tables": {"b467a98cb2af44169a8f265dacf977c3": {"summary": "This comprehensive table provides detailed specifications for the GP-PWM-10-FM Solar Controller. Key specifications include: Nominal System Voltage of 12 VDC, Maximum Solar Continuous DC Charge Current Input of 12.5 ADC, Maximum Solar DC Input Voltage of 35 VDC, and support for various battery types including Lead Acid and Lithium. The controller features a 4-stage charging system with specific voltage levels for Bulk/Absorption (14.1/14.4/14.4VDC), Float (13.7V, 14.0V for LFP), and Equalization (14.9V). It includes temperature compensation, USB charging capability, and various protection features. The table also lists physical specifications such as dimensions (149 x 98 x 32 mm) and weight (260 g), as well as environmental operating conditions and warranty information.", "actual_reference": "", "is_complete": true, "cross_page_context": "This specifications table follows the installation overview and precedes the warnings section, providing a comprehensive technical reference for the GP-PWM-10-FM Solar Controller discussed throughout the document."}}, "enhanced_diagrams": {}}
-----enhancement_group_3_5__end-----
-----enhancement_group_6_8__start-----
{"actual_references_found": {"images": [], "tables": [], "diagrams": []}, "headers_and_footers": {"headers": [], "footers": ["gpelectric.com", "page"]}, "enhanced_images": {"fcae861c77c74033badaf8d727a7efa5": {"summary": "A warning symbol (triangle with exclamation mark) followed by text that reads: \"WARNING: This unit is not provided with a GFDI device. This charge controller must be used with an external GFDI device as required by Article 690 of the National Electric Code for the installation location.\" This warning is important for the safe installation and operation of the GP-PWM-10-FM solar charge controller.", "actual_reference": "", "is_complete": true, "cross_page_context": "This warning appears in the context of installation instructions for the GP-PWM-10-FM solar charge controller, emphasizing safety requirements."}, "ceb5c3397e434e31aff9e7ff1c315fd9": {"summary": "A warning symbol (triangle with exclamation mark) followed by text that reads: \"WARNING: When the photovoltaic (solar) array is exposed to light, it supplies a dc voltage to this equipment\". This warning highlights the constant voltage supply from solar panels when exposed to light, which is crucial for safe handling during installation and maintenance.", "actual_reference": "", "is_complete": true, "cross_page_context": "This warning is part of the wiring diagrams section, providing important safety information for handling the solar array connection to the GP-PWM-10-FM controller."}}, "enhanced_tables": {"c014e294a04f4bae8622532e17641e63": {"summary": "The table shows suggested minimum wire gauge for different solar module wattages, specifically for the GP-PWM-10-FM controller installation. It recommends #10 Wire Gauge for all listed solar module wattages: 80W, 100W, 160W, 170W, and 190W. This uniform recommendation simplifies wiring choices for various solar panel configurations up to 190W.", "actual_reference": "Suggested Minimum Wire Gauge", "is_complete": true, "cross_page_context": "This table is part of the installation instructions, providing crucial information for proper wiring of the solar array to the GP-PWM-10-FM controller."}, "bed79aaed46845b2ac4102d077421453": {"summary": "A table titled \"Stranded Copper 90\u00b0C Wire\" specifies the rated torque for different wire sizes used with the GP-PWM-10-FM controller. It shows that for 14 AWG, 12 AWG, and 10 AWG wire sizes, the rated torque is consistently 20 in-lbs. This information is critical for proper installation and secure connections.", "actual_reference": "", "is_complete": true, "cross_page_context": "This table is provided in the context of installation instructions, specifically relating to the torque requirements for securing wires to the GP-PWM-10-FM controller terminals."}}, "enhanced_diagrams": {"46547e6ee2d245a8911eb45730caf1a5": {"summary": "A wiring diagram illustrating the connections for the GP-PWM-10-FM solar charge controller. It shows a battery bank connected to the controller through a fuse (rated no larger than 15 amps), and a solar array directly connected to the controller. The controller has four clearly labeled terminals: BATTERY +, BATTERY -, SOLAR +, and SOLAR -. This diagram emphasizes the importance of correct wiring and the use of a fuse for battery protection.", "actual_reference": "", "is_complete": true, "cross_page_context": "This diagram is part of the wiring instructions section, providing a visual guide for properly connecting the solar array and battery to the GP-PWM-10-FM controller."}, "3fc4224064ab40edb6df1edd445c3cd0": {"summary": "A diagram showing the interface of the GP-PWM-10-FM solar controller. It displays a screen with \"13.8v\" indicating the current voltage reading. Below the screen are two buttons labeled A and B, used for navigating the controller's settings. The device is identified as a \"10 AMP PWM SOLAR CONTROLLER\" at the top. A USB port icon is also visible, suggesting data connectivity or charging capabilities. The Go Power! logo is prominently displayed, branding the device.", "actual_reference": "", "is_complete": true, "cross_page_context": "This diagram is presented in the operating instructions section, illustrating the user interface of the GP-PWM-10-FM controller and how to interact with its settings, particularly for setting the battery charging profile."}}}
-----enhancement_group_6_8__end-----
-----enhancement_group_9_11__start-----
{"actual_references_found": {"images": [], "tables": [], "diagrams": []}, "headers_and_footers": {"headers": ["OPERATING INSTRUCTIONS"], "footers": ["gpelectric.com", "page"]}, "enhanced_images": {}, "enhanced_tables": {"485130aaa5b148c084b3ed5edfc1f3e4": {"summary": "This table presents battery charging profiles for different battery types: SEALED/GEL, AGM, FLOODED, and LFP (LiFePO4). It provides voltage settings for Float Charge, Bulk/Absorption Charge, and Equalization Charge at 25\u00b0C. The table helps users select the appropriate charging profile based on their battery manufacturer's recommendations. It's part of the GP-PWM-10-FM solar controller's operating instructions, specifically under section 7.3 BATTERY CHARGING PROFILE CHART.", "actual_reference": "", "is_complete": true, "cross_page_context": "This table is crucial for understanding the charging behavior of the GP-PWM-10-FM solar controller across different battery types. It's followed by information about the Auto Equalize feature and notes on charging limitations due to insufficient PV power or high load conditions."}}, "enhanced_diagrams": {"ac1a71d9ae4f4b05b2b7463a15a2e55f": {"summary": "This diagram shows the display of a 10 AMP PWM SOLAR CONTROLLER, specifically the GP-PWM-10-FM model. The display indicates a battery voltage of 13.8V. It illustrates how to use the B Button to toggle between different display modes, starting with the battery voltage. This is part of a series of diagrams demonstrating the controller's display information viewing process.", "actual_reference": "", "is_complete": true, "cross_page_context": "This is the first in a series of four diagrams showing how to cycle through different display modes of the solar controller using the B Button."}, "a88a71f80fae46e482d4df6fb392b242": {"summary": "This diagram depicts the 10 AMP PWM SOLAR CONTROLLER display showing the PV charging current of 7.6A. It's the second in the series demonstrating how to use the B Button to cycle through display modes. The diagram emphasizes the ampere symbol (A) and the battery state of charge icon.", "actual_reference": "", "is_complete": true, "cross_page_context": "This diagram follows the battery voltage display and precedes the battery state of charge display in the instruction sequence."}, "6de4033f6e0e41e0b2d4dd8000db1e6c": {"summary": "This diagram shows the 10 AMP PWM SOLAR CONTROLLER display indicating a 90% battery state of charge. It's the third in the series demonstrating the use of the B Button to cycle through display modes. The percent symbol (%) is highlighted, and the diagram notes that 100% will only be displayed after a Boost or Equalize charge completes.", "actual_reference": "", "is_complete": true, "cross_page_context": "This diagram is part of the sequence showing different display modes, positioned between the PV charging current and ampere hours charged displays."}, "87e2e5da62fe41f8b4660d20ff1ebca9": {"summary": "This diagram illustrates the 10 AMP PWM SOLAR CONTROLLER display showing 45.3 ampere hours (Ah) charged since the last reset. It's the fourth and final diagram in the series demonstrating how to use the B Button to cycle through display modes. The diagram emphasizes the Ah symbol and mentions that kAh (kiloamp hours) may also be displayed for larger values.", "actual_reference": "", "is_complete": true, "cross_page_context": "This is the last in the series of four diagrams showing the controller's display modes. It's followed by instructions on how to reset the ampere hours charged counter."}, "f8337a77a13e42869c64707ec35380da": {"summary": "This diagram shows the 10 AMP PWM SOLAR CONTROLLER display after resetting the ampere hours charged counter to 0.0 Ah. It illustrates the process of resetting the counter by pressing and holding the A Button for 6 seconds while in the ampere hours charged display mode.", "actual_reference": "", "is_complete": true, "cross_page_context": "This diagram follows the series of display mode illustrations and precedes the error display diagrams, demonstrating a specific function of the controller."}, "4a1c3cf77c74404599865eab10282296": {"summary": "This diagram depicts the 10 AMP PWM SOLAR CONTROLLER display during an over voltage error condition. All icons on the display are lit up, and the screen shows '8.8.8 kAh' to indicate that all segments are active. This visual representation corresponds to the controller's behavior when battery voltage exceeds 15.5V, at which point it stops operating until the error is cleared.", "actual_reference": "", "is_complete": true, "cross_page_context": "This diagram is part of the error display section, specifically illustrating the over voltage error condition. It's followed by a diagram showing the low voltage error display."}, "1b87d64ace1b4e9d88722d79588c7672": {"summary": "This diagram shows the 10 AMP PWM SOLAR CONTROLLER display during a low voltage condition. The screen displays a battery icon with the text 'LOW' beneath it, indicating that the battery voltage has reached 11 volts. This visual corresponds to the controller's low voltage warning, which occurs before the controller stops operating at voltages below 9 volts.", "actual_reference": "", "is_complete": true, "cross_page_context": "This is the final diagram in the operating instructions section, illustrating the low voltage warning display. It follows the over voltage error display and completes the set of error condition illustrations for the controller."}}}
-----enhancement_group_9_11__end-----
-----enhancement_group_12_14__start-----
{"actual_references_found": {"images": [], "tables": [], "diagrams": []}, "headers_and_footers": {"headers": ["DISPLAY SYMBOLS"], "footers": ["gpelectric.com", "page"]}, "enhanced_images": {"83754eeb90d1405c82112f56ec04360a": {"summary": "A warning symbol (yellow triangle with exclamation mark) indicating a cautionary message about USB charging connections. This symbol is used to emphasize the importance of proper USB device connection to prevent damage or malfunction.", "actual_reference": "", "is_complete": true, "cross_page_context": "This warning symbol is related to the USB charging feature discussed in the 'USB CHARGING' section on page 14."}, "b80e0a41adbd49a7989a56edcc22a69d": {"summary": "Logo of Go Power!, a company associated with solar power and battery systems. The logo appears at the bottom of page 15, suggesting it's the manufacturer of the GP-PWM-10-FM solar charge controller discussed in the document.", "actual_reference": "", "is_complete": true, "cross_page_context": "This logo is consistently placed at the bottom of the pages, indicating it's part of the document's branding."}, "4ad2362ce1434b5e89c25250f1aa7213": {"summary": "Logo of Dometic, another company likely associated with power systems or RV/marine equipment. The logo appears alongside the Go Power! logo, suggesting a partnership or association between the two companies in relation to the GP-PWM-10-FM product.", "actual_reference": "", "is_complete": true, "cross_page_context": "This logo is consistently placed at the bottom of the pages alongside the Go Power! logo, indicating a collaborative branding effort."}}, "enhanced_tables": {"1d1ec91ebbd241a289185bb8b897f927": {"summary": "A comprehensive table showing display symbols and their corresponding indicators for the GP-PWM-10-FM solar charge controller. It includes symbols for Day Time: PV Charge Current (sun icon), Night Time (moon icon), Battery Voltage (battery icon with 'V'), Battery State of Charge (battery icon with percentage), and battery types: Sealed/Gel, AGM/LFP, and Flooded. Each symbol is represented by an icon in the left column, with its meaning in the right column, providing users with a quick reference for interpreting the controller's display.", "actual_reference": "", "is_complete": true, "cross_page_context": "This table is part of the 'DISPLAY SYMBOLS' section, which continues across multiple pages to provide a comprehensive guide to the controller's interface."}, "82ddadb5d3e64d6aaf19ee94e743271c": {"summary": "A table showing additional symbols and their meanings for the GP-PWM-10-FM controller. It includes a USB symbol indicating 'USB charger ON (when charger is OFF, no symbol will show)', 'LOW' text indicating 'Battery voltage is lower than 11.0V', and 'Whole display will start to blink' indicating 'Battery voltage > 15.5V'. These symbols provide important status information about USB charging and battery voltage conditions.", "actual_reference": "", "is_complete": true, "cross_page_context": "This table complements the previous symbols table, providing additional information about the controller's display indicators, particularly related to USB charging and battery voltage warnings."}, "c1b95f5f0b2f45fdbcc436e05b5ab582": {"summary": "A detailed table titled 'BATTERY STATE OF CHARGE' for the GP-PWM-10-FM controller. It has two columns: 'SYMBOLS' and 'BATTERY VOLTAGE'. The SYMBOLS column shows 5 battery icons with varying levels of charge, from full to empty. The BATTERY VOLTAGE column provides corresponding voltage ranges or conditions for each symbol, such as '>13.0V' for a full battery and '<11.5V' for an empty battery. Additionally, it includes percentage indicators (100%, 90%, and 0%) with their respective voltage conditions. The table also provides an equation for calculating State of Charge (SOC): SOC(%) = (Battery Voltage - 11.0V) x 100 / (13.0V - 11.0V).", "actual_reference": "", "is_complete": true, "cross_page_context": "This table is part of the 'DISPLAY SYMBOLS' section and provides crucial information for interpreting the battery charge status on the controller's display. It relates to the battery symbols explained in the previous tables."}, "8628eb726bcf44748375f427899895cf": {"summary": "A table in the FAQ section detailing warning signals for the GP-PWM-10-FM controller. It has four columns: CONNECTION, WARNING, NOTES, and LCD. The table shows warning signals for battery and PV reverse polarity. For both cases, the WARNING is 'POL' displayed on the LCD and a constant audible alarm. For PV reverse polarity, there's a NOTE that the battery must be connected with correct polarity for the warning to function. The LCD column confirms 'POL' display for both cases. This table provides important troubleshooting information for users experiencing connection issues.", "actual_reference": "", "is_complete": true, "cross_page_context": "This table is part of the FAQ section on page 15, providing specific information about warning signals that users might encounter when using the GP-PWM-10-FM controller."}}, "enhanced_diagrams": {}}
-----enhancement_group_12_14__end-----
-----enhancement_group_15_17__start-----
{"actual_references_found": {"images": [], "tables": [], "diagrams": []}, "headers_and_footers": {"headers": ["TROUBLESHOOTING PROBLEMS"], "footers": []}, "enhanced_images": {"b64a669fed394589a18c63363bf43406": {"summary": "The image shows the Dometic company logo, which appears at the top of the page. This logo is consistently present across all three pages of the troubleshooting section, indicating it's likely a header element. The logo consists of the stylized word 'DOMETIC' in capital letters, with a curved line above it and the text 'Go Power!' inside an oval shape.", "actual_reference": "", "is_complete": true, "cross_page_context": "This logo appears on all three pages of the troubleshooting section, serving as a consistent branding element and header."}, "2a071aba0308438692b2546b6acd709f": {"summary": "The image displays the Dometic logo in the top right corner of the page. The logo design is consistent with the previous page, featuring the stylized word 'DOMETIC' with a curved line above it and the text 'Go Power!' inside an oval shape. This logo serves as a header element for the troubleshooting section.", "actual_reference": "", "is_complete": true, "cross_page_context": "This logo is consistently placed at the top of each page in the troubleshooting section, maintaining brand presence throughout the document."}, "65f26cc8663a44119b2b4b0af88112b0": {"summary": "The image shows the Dometic company logo, which is identical to the logos on the previous two pages. It consists of the stylized word 'DOMETIC' in capital letters, with a curved oval shape above containing the text 'Go Power!'. The logo is positioned at the top of the page, serving as a header element for the troubleshooting section.", "actual_reference": "", "is_complete": true, "cross_page_context": "This logo appears consistently across all three pages of the troubleshooting section, reinforcing the brand identity and providing visual continuity to the document."}}, "enhanced_tables": {}, "enhanced_diagrams": {}}
-----enhancement_group_15_17__end-----
-----enhancement_group_18_19__start-----
{"actual_references_found": {"images": [], "tables": [], "diagrams": []}, "headers_and_footers": {"headers": [], "footers": ["gpelectric.com", "\u00a9 2021 Go Power!", "Manual_GP-PWM-10-FM"]}, "enhanced_images": {"d0fd745f606348db9a7535704b47a221": {"summary": "The image shows the logo of Go Power!, the manufacturer of the GP-PWM-10 product described in the warranty section. The logo appears at the bottom of the warranty information page, indicating its significance as the company brand.", "actual_reference": "", "is_complete": true, "cross_page_context": "This logo is part of the company branding that appears consistently across the document, reinforcing the product's association with Go Power!."}, "cd4eed3867d444a795393ed7f1dda0bc": {"summary": "The image displays the logo of Dometic, which is associated with Go Power! as indicated by their joint appearance in the document footer. This suggests a partnership or ownership relationship between the two companies.", "actual_reference": "", "is_complete": true, "cross_page_context": "The Dometic logo appears alongside the Go Power! logo, indicating a close association between the two brands in relation to the GP-PWM-10 product."}, "d434935fb3514a59a7bed576a381350d": {"summary": "This image combines the logos of Go Power! and Dometic side by side. The Go Power! logo on the left features a stylized 'GP' in a circular design, while the Dometic logo on the right displays 'DOMETIC' in capital letters with a distinctive arrow-like symbol above it. This dual branding appears at the bottom of the document, serving as an official seal or endorsement.", "actual_reference": "", "is_complete": true, "cross_page_context": "The combined logo image reinforces the partnership between Go Power! and Dometic seen throughout the document, particularly in relation to the GP-PWM-10 product and its warranty information."}}, "enhanced_tables": {}, "enhanced_diagrams": {}}
-----enhancement_group_18_19__end-----
-----FINAL_MARKDOWN__start-----
# 10AMP PWM SOLAR CONTROLLER

## User Manual

GP-PWM-10-FM (FLUSH MOUNT - LITHIUM COMPATIBLE)

!Image[This image shows the front panel of the 10 AMP PWM Solar Controller (GP-PWM-10-FM) device. The controller has a black rectangular faceplate with a digital LCD display in the center. The display shows '0.0 V' and 'kAh', indicating it can show voltage and ampere-hour readings. There are icons for solar, battery, and load on the display, suggesting it monitors these aspects of a solar power system. Below the display are two buttons labeled A and B, likely for user interaction and settings adjustment. On the left side of the panel is a switch, possibly for power or mode selection. The Go Power! logo and website URL (gpelectric.com) are visible on the right side of the faceplate, branding the device.]

Worldwide Technical Support and Product Information



#

!Image[This image displays two logos associated with the solar controller product. The top logo is circular and contains the text 'Go Power!' inside, which is the primary brand of the solar controller. Below it is the Dometic logo, featuring the word 'DOMETIC' in a stylized font. This suggests that Go Power! is either a subsidiary or partner brand of Dometic, a well-known company in the mobile living market space.]

1. INSTALLATION OVERVIEW..................................................................................................................... 4

   1.1 INTRODUCTION ...............................................................................................................................4
   1.2 SYSTEM VOLTAGE AND CURRENT.....................................................................................................4
   1.3 BATTERY TYPE..................................................................................................................................4
   1.4 LOW VOLTAGE DISCONNECT FUNCTION (USB PORT) .......................................................................4
   1.5 REGULATORY INFORMATION ...........................................................................................................4
   1.6 SPECIFICATIONS...............................................................................................................................4

2. WARNINGS ............................................................................................................................................... 5

3. TOOLS AND MATERIALS NEEDED ........................................................................................................ 6

4. CHOOSING A LOCATION ........................................................................................................................ 6

5. INSTALLATION INSTRUCTIONS ............................................................................................................. 6

6. WIRING DIAGRAM .................................................................................................................................. 7

7. OPERATING INSTRUCTIONS .................................................................................................................. 8

   7.1 POWER UP......................................................................................................................................8
   7.2 SETTING THE BATTERY CHARGING PROFILE .....................................................................................8
   7.3 BATTERY CHARGING PROFILE CHART ...............................................................................................9
   7.4 VIEWING THE CONTROLLER DISPLAY INFORMATION........................................................................9
   7.5 RESETTING THE AMPERE HOURS CHARGED ...................................................................................11
   7.6 ERRORS .........................................................................................................................................11

8. DISPLAY SYMBOLS................................................................................................................................. 12

9. USB CHARGING...................................................................................................................................... 13

10. FREQUENTLY ASKED QUESTIONS (FAQS) .......................................................................................... 14

11. TROUBLESHOOTING PROBLEMS......................................................................................................... 14

    11.1 PROBLEMS WITH THE DISPLAY......................................................................................................15
    11.2 PROBLEMS WITH VOLTAGE ...........................................................................................................15
    11.3 PROBLEMS WITH CURRENT...........................................................................................................16

12. LIMITED WARRANTY ............................................................................................................................. 17

    12.1 REPAIR AND RETURN INFORMATION.............................................................................................17

13. INSTALLATION TEMPLATE .................................................................................................................... 17

 | [ 3]

# 1.

## 1.1 INTRODUCTION

A Solar Controller (or Charge Controller / Regulator) is an essential component of your photovoltaic solar system. The Controller maintains the life of the battery by protecting it from overcharging. When your battery has reached a 100% state of charge, the Controller prevents overcharging by limiting the current flowing into the batteries from your solar array.

The GP-PWM-10-FM uses Pulse Width Modulation (PWM) technology and a unique four stage charging system that includes an optional equalize setting to charge and protect your battery bank. The GP-PWM-10-FM features an LCD digital display that shows the charge current of the solar array, battery voltage and battery state of charge.

## 1.2 SYSTEM VOLTAGE AND CURRENT

The GP-PWM-10-FM is intended for use at 12 VDC nominal system voltage and is rated for a maximum continuous DC input current of 12.5A and input voltage of 35VDC.

Per the National Electric Code (NEC) article 690.7 and 690.8, PV module nameplate ratings at Standard Test Conditions (STC) must be multiplied by required values (typically 1.25 for both voltage and current) to obtain the true voltage and continuous current available from the module.

Applying the NEC factors, the maximum allowable nameplate PV Panel rated Isc is 10A (10A x 1.25 = 12.5A), and the maximum voltage, Voc is 28VDC (28VDC x 1.25 = 35VDC).

The voltage and current ratings of all equipment connected to PV panels must be capable of accepting the voltage and current levels available from PV panels installed in the field.

## 1.3 BATTERY TYPE

The GP-PWM-10-FM is suitable for use with lead acid and lithium batteries (vented, GEL, LiFePO4 (LFP) or AGM type).

## 1.4 LOW VOLTAGE DISCONNECT FUNCTION (USB PORT)

To protect the battery against over-discharge this function automatically switches off the USB output port when battery voltage is lower than 11.0 VDC. As soon as the battery reaches a voltage of 12.8 VDC the USB output port is switched on again.

## 1.5 REGULATORY INFORMATION

!Image[This image shows two regulatory compliance symbols. The CE (Conformité Européenne) mark on the left indicates compliance with European Union standards. The RoHS (Restriction of Hazardous Substances) symbol on the right indicates compliance with the EU directive restricting the use of certain hazardous substances in electrical and electronic equipment. These symbols are relevant to the GP-PWM-10-FM Solar Controller discussed in the document, confirming its adherence to European safety and environmental standards.]

[ 4] |

#

## 1.6 SPECIFICATIONS

!Table[This comprehensive table provides detailed specifications for the GP-PWM-10-FM Solar Controller. Key specifications include: Nominal System Voltage of 12 VDC, Maximum Solar Continuous DC Charge Current Input of 12.5 ADC, Maximum Solar DC Input Voltage of 35 VDC, and support for various battery types including Lead Acid and Lithium. The controller features a 4-stage charging system with specific voltage levels for Bulk/Absorption (14.1/14.4/14.4VDC), Float (13.7V, 14.0V for LFP), and Equalization (14.9V). It includes temperature compensation, USB charging capability, and various protection features. The table also lists physical specifications such as dimensions (149 x 98 x 32 mm) and weight (260 g), as well as environmental operating conditions and warranty information.]

 | [ 5]

# 2.

!Image[The image shows the Dometic company logo, which is a stylized oval shape containing the text "Go Power!". This logo appears at the beginning of the warnings section, indicating that the safety information provided is associated with Dometic's Go Power! brand of solar controllers.]

!Image[This image depicts a warning symbol showing a lightning bolt inside a triangle. It indicates an electrical hazard and is used to emphasize the importance of disconnecting all power sources before installation or maintenance of the solar controller.] **Disconnect all power sources**

Electricity can be very dangerous. Installation should be performed only by a licensed electrician or qualified personnel.

!Image[The image shows a warning symbol featuring a battery with a lightning bolt. This symbol is used to highlight the safety precautions related to battery handling and the potential dangers associated with hydrogen gas production during battery charging.] **Battery and wiring safety**

Observe all safety precautions of the battery manufacturer when handling or working around batteries. When charging, batteries produce hydrogen gas, which is highly explosive.

!Image[This warning symbol depicts a hand being struck by lightning, indicating the danger of electrical shock. It emphasizes the importance of proper wiring connections and the need to ensure all connections are tight and secure to prevent sparks and heat generation.] **Wiring connections**

Ensure all connections are tight and secure. Loose connections may generate sparks and heat. Be sure to check connections one week after installation to ensure they are still tight.

!Image[The image shows a safety symbol of a person wearing safety goggles. This icon emphasizes the need for eye protection during installation and when working with the solar controller system, as part of the overall safety precautions.] **Work safely**

Wear protective eye wear and appropriate clothing during installation. Use extreme caution when working with electricity and when handling and working around batteries. Use properly insulated tools only.

!Image[This warning symbol shows an exclamation mark inside a triangle, indicating general caution or warning. In the context of the document, it's used to emphasize the importance of observing correct polarity at all times when connecting the solar controller.] **Observe correct polarity at all times**

Reverse polarity of the battery terminals will cause the controller to give a warning tone. Reverse connection of the array will not cause an alarm but the controller will not function. Failure to correct this fault could damage the controller.

!Image[The image depicts a warning symbol showing flames, indicating a fire hazard or high temperature warning. In the context of the document, this symbol is used to emphasize the importance of not exceeding the GP-PWM-10-FM's amp current and max voltage ratings to prevent potential fire hazards.] **Do not exceed the GP-PWM-10-FM Amp current and max voltage ratings**

The maximum current of the solar system is the sum of parallel-connected PV module--rated short circuit Currents (Isc) multiplied by 1.25. The resulting system current is not to exceed 12.5A. If your solar system exceeds this value, contact your dealer for a suitable controller alternative.

**Do not exceed the GP-PWM-10-FM max voltage ratings**

The maximum voltage of the array is the sum of the PV module--rated open-circuit voltage of the series connected modules multiplied by 1.25 (or by a value from NEC 690.7 provided in Table 690.7 A). The resulting voltage is not to exceed 35V. If your solar system exceeds this value, contact your dealer for a suitable controller alternative.

[ 6] |

# 3. TOOL AND MATERIALS NEEDED

- Flathead Screwdriver (for wire terminals)
- Philips Screwdriver (for mounting screws)

> **Note**: If the GP-PWM-10-FM Controller was purchased with a Go Power! Solar Power Kit, then UV resistant wire is included. For instructions regarding the Go Power! Solar Power Kit installation, please refer to the Installation Guide provided with the Kit.

# 4. CHOOSING A LOCATION

The GP-PWM-10-FM is designed to be mounted flush against a wall, out of the way but easily visible.

The GP-PWM-10-FM should be:

- Mounted as close to the battery as possible
- Mounted on a vertical surface to optimize cooling of the unit
- Indoors, protected from the weather

In an RV, the most common controller location is above the refrigerator. The wire from the solar array most commonly enters the RV through the fridge vent on the roof or by using the Go Power! Cable Entry Plate (sold separately) that allows installers to run wires through any part of the roof. PV connections should connect directly to the controller. Positive and negative battery connections must connect directly from the controller to the batteries. Use of a positive or negative distribution bus is allowed between the controller and battery as long as it is properly sized, electrically safe and an adequate wire size is maintained.

# 5. INSTALLATION INSTRUCTIONS

1. Prepare for mounting. Use the template provided on  17 to mark the four mounting holes and the cutting line for flush mounting your controller.

2. Complete the installation of the solar modules. If this GP-PWM-10-FM was purchased as part of a Go Power! Solar Power Kit, follow the Installation Guide provided. Otherwise, follow manufacturer's instructions for solar module mounting and wiring.

3. Select wire type and gauge. If this GP-PWM-10-FM was purchased as part of a Go Power! Solar Power Kit, appropriate wire type, gauge, and length is provided. Please continue to Section 6, "Operating Instructions." If the GP-PWM-10-FM was purchased separately, follow the instructions included here.

Wire type is recommended to be a stranded copper UV-resistant wire. Wire fatigue and the likelihood of a loose connection are greatly reduced in stranded wire compared to solid wire. Wire gauge should be able to sustain rated current and minimize voltage drop.

Wire Strip Length
Strip wires to a length of approximately 3/8 in (9 mm, as per strip gauge).

Suggested Minimum Wire Gauge
(Cable length 25 ft. max. from solar array to battery bank)

!Table[The table shows suggested minimum wire gauge for different solar module wattages, specifically for the GP-PWM-10-FM controller installation. It recommends #10 Wire Gauge for all listed solar module wattages: 80W, 100W, 160W, 170W, and 190W. This uniform recommendation simplifies wiring choices for various solar panel configurations up to 190W.](Suggested Minimum Wire Gauge)

IMPORTANT: Identify the polarity (positive and negative) on the cable used for the battery and solar module. Use colored wires or mark the wire ends with tags. Although the GP-PWM-10-FM is protected, a reverse polarity contact may damage the unit.

Wiring the GP-PWM-10-FM. Wire the GP-PWM-10-FM according to the wiring schematic in Section 6. Run wires from the solar array and the batteries to the location of the GP-PWM-10-FM. Keep the solar array covered with an opaque material until all wiring is completed.

# 6. WIRING DIAGRAMS

**IMPORTANT**: All wiring must be in accordance to National Electrical Code, ANSI/NFPA 70. Always use appropriate circuit protection on any conductor attached to a battery.

4. Connect the battery wiring to the controller first and then connect the battery wiring to the battery.

5. Torque all terminal screws per the following:

!Table[A table titled "Stranded Copper 90°C Wire" specifies the rated torque for different wire sizes used with the GP-PWM-10-FM controller. It shows that for 14 AWG, 12 AWG, and 10 AWG wire sizes, the rated torque is consistently 20 in-lbs. This information is critical for proper installation and secure connections.]

With battery power attached, the controller should power up and display information. Connect the solar wiring to the controller and remove the opaque material from the solar array. The negative solar array and battery wiring must be connected directly to the controller for proper operation. Do not connect the negative solar array or negative battery controller wiring to the chassis of the vehicle.

6. Mounting the GP-PWM-10-FM. Mount the GP-PWM-10-FM to the wall using the included four mounting screws.

**IMPORTANT**: You must set the battery type on the GP-PWM-10-FM before you begin to use the controller (follow steps in Section 7). The default battery setting is for AGM/LiFePO4 batteries.

Congratulations, your GP-PWM-10-FM should now be operational. If the battery power is low and the solar array is producing power, your battery should begin to charge.

7. Re-torque: After 30 days of operation, re-torque all terminal screws to ensure the wires are properly secured to the controller.

!Image[A warning symbol (triangle with exclamation mark) followed by text that reads: "WARNING: This unit is not provided with a GFDI device. This charge controller must be used with an external GFDI device as required by Article 690 of the National Electric Code for the installation location." This warning is important for the safe installation and operation of the GP-PWM-10-FM solar charge controller.]

# 6. WIRING DIAGRAMS

**IMPORTANT**: This diagram is valid only for version 1.5 and newer. Version 1.4 and older have different terminal locations.

The GP-PWM-10-FM Maximum 12.5A rating is based on a 10 amp total maximum short circuit current rating (Isc) from the parallel solar module nameplate ratings. The National Electric Code specifies the PV equipment/system rating to be 125% of the maximum Isc from the PV module nameplate ratings (1.25 times 10 = 12.5A). Use the wiring diagram (below) to connect your battery to the battery terminals on the solar controller. First, connect the battery to the controller, and then connect the solar panel to the controller

**Note**: The fuse or breaker used should be no larger than 15 amps.

**Note**: The controller will not work unless there is a battery connected to the battery terminals with at least 9V.

!Image[A warning symbol (triangle with exclamation mark) followed by text that reads: "WARNING: When the photovoltaic (solar) array is exposed to light, it supplies a dc voltage to this equipment". This warning highlights the constant voltage supply from solar panels when exposed to light, which is crucial for safe handling during installation and maintenance.]

[ 8] |

# 7. OPERATING INSTRUCTIONS

!Wiring_Diagram[A wiring diagram illustrating the connections for the GP-PWM-10-FM solar charge controller. It shows a battery bank connected to the controller through a fuse (rated no larger than 15 amps), and a solar array directly connected to the controller. The controller has four clearly labeled terminals: BATTERY +, BATTERY -, SOLAR +, and SOLAR -. This diagram emphasizes the importance of correct wiring and the use of a fuse for battery protection.]

## 7.1 SYSTEM VOLTAGE AND CURRENT

When the GP-PWM-10-FM is connected to the battery, the controller will go into Power Up mode.

Icons Displayed: All segments of the numerical display; backlight blinks. Depending on the battery voltage when the GP-PWM-10-FM Power Up occurs, the controller may do a Boost Charge or quickly go into Float Charge. The Charging Profile selected will commence the following day after a Power Up (refer to the Charging Profile Chart on  11 for more details).

## 7.2 SETTING THE BATTERY CHARGING PROFILE

!Device_Interface[A diagram showing the interface of the GP-PWM-10-FM solar controller. It displays a screen with "13.8v" indicating the current voltage reading. Below the screen are two buttons labeled A and B, used for navigating the controller's settings. The device is identified as a "10 AMP PWM SOLAR CONTROLLER" at the top. A USB port icon is also visible, suggesting data connectivity or charging capabilities. The Go Power! logo is prominently displayed, branding the device.]

To select the battery charging profile, press and hold the B Button. This will cause the current battery type to flash.

Then, press the B Button to toggle through the profile options: Sealed/ Gel, AGM/LiFePO4 or Flooded.

To confirm the battery profile, press and hold the A Button for 3 seconds.

Non-volatile memory: Any settings made on the GP-PWM-10-FM will be saved even when the power has been disconnected from the controller.

Refer to the Battery Charge Profile Chart below for details on each profile.

## 7.2 SETTING THE BATTERY CHARGING PROFILE

 | [ 9]

#

## 7.3 BATTERY CHARGING PROFILE CHART

!Table[This table presents battery charging profiles for different battery types: SEALED/GEL, AGM, FLOODED, and LFP (LiFePO4). It provides voltage settings for Float Charge, Bulk/Absorption Charge, and Equalization Charge at 25°C. The table helps users select the appropriate charging profile based on their battery manufacturer's recommendations. It's part of the GP-PWM-10-FM solar controller's operating instructions, specifically under section 7.3 BATTERY CHARGING PROFILE CHART.]

If a charging cycle is unable to complete in a single day, it will continue the following day. The terms SEALED/GEL, AGM, FLOODED and LFP are generic battery designations. Choose the charging profile that works best with your battery manufacturer's recommendations.

> **Note**: If PV power is insufficient or too many loads are drawing power from the battery, the controller will not be able to charge the battery to the target charging voltage.

Auto Equalize: The GP-PWM-10-FM has an automatic equalize feature that will charge and recondition your batteries at least once a month at a higher voltage to ensure that any excess sulfation is removed.

> **Note**: This mode should not be entered unless you are using a flooded battery.

## 7.4 VIEWING THE CONTROLLER DISPLAY INFORMATION

To toggle between Battery Voltage, PV Charging Current, Battery State of Charge (SOC), and ampere hours charged since last reset, press the B Button.

!Diagram[This diagram shows the display of a 10 AMP PWM SOLAR CONTROLLER, specifically the GP-PWM-10-FM model. The display indicates a battery voltage of 13.8V. It illustrates how to use the B Button to toggle between different display modes, starting with the battery voltage. This is part of a series of diagrams demonstrating the controller's display information viewing process.]

[ 10] |

#

!Diagram[This diagram depicts the 10 AMP PWM SOLAR CONTROLLER display showing the PV charging current of 7.6A. It's the second in the series demonstrating how to use the B Button to cycle through display modes. The diagram emphasizes the ampere symbol (A) and the battery state of charge icon.]

Push the B Button to show the PV charging current.

Icons Displayed: Ampere Symbol (A), Battery SOC

!Diagram[This diagram shows the 10 AMP PWM SOLAR CONTROLLER display indicating a 90% battery state of charge. It's the third in the series demonstrating the use of the B Button to cycle through display modes. The percent symbol (%) is highlighted, and the diagram notes that 100% will only be displayed after a Boost or Equalize charge completes.]

Push the B Button to show the battery state of charge (shown as a percentage).

Icons Displayed: Battery SOC, Percent Symbol (%)

A value of 100% will only be displayed after a Boost or Equalize charge completes.

!Diagram[This diagram illustrates the 10 AMP PWM SOLAR CONTROLLER display showing 45.3 ampere hours (Ah) charged since the last reset. It's the fourth and final diagram in the series demonstrating how to use the B Button to cycle through display modes. The diagram emphasizes the Ah symbol and mentions that kAh (kiloamp hours) may also be displayed for larger values.]

Push the B Button to show the number of amp hours charged since the last reset.

Icons Displayed: Amp hours charged, Amp hour symbol (Ah) or kiloamp hour symbol (kAh)

 | [ 11]

#

## 7.5 RESETTING THE AMPERE HOURS CHARGED

!Diagram[This diagram shows the 10 AMP PWM SOLAR CONTROLLER display after resetting the ampere hours charged counter to 0.0 Ah. It illustrates the process of resetting the counter by pressing and holding the A Button for 6 seconds while in the ampere hours charged display mode.]

To reset the count of ampere hours charged, toggle to the ampere hours charged. Press and hold the A Button for 6 seconds to reset the counter to zero.

## 7.6 ERRORS

### OVER VOLTAGE

!Diagram[This diagram depicts the 10 AMP PWM SOLAR CONTROLLER display during an over voltage error condition. All icons on the display are lit up, and the screen shows '8.8.8 kAh' to indicate that all segments are active. This visual representation corresponds to the controller's behavior when battery voltage exceeds 15.5V, at which point it stops operating until the error is cleared.]

If the GP-PWM-10-FM experiences a battery over voltage (15.5V), the controller will stop operating, and the display will begin to flash with all icons. The controller will resume operating when the error is cleared.

Icons Displayed: All symbols

### LOW VOLTAGE

!Diagram[This diagram shows the 10 AMP PWM SOLAR CONTROLLER display during a low voltage condition. The screen displays a battery icon with the text 'LOW' beneath it, indicating that the battery voltage has reached 11 volts. This visual corresponds to the controller's low voltage warning, which occurs before the controller stops operating at voltages below 9 volts.]

If the battery voltage reaches 11 volts, the battery SOC symbol will show the text "LOW" beneath it. The controller will continue operating in this condition and will only stop operating if the voltage drops below 9 volts.

Icons Displayed: Battery SOC Symbol, LOW

[ 12] |

# 8.

## SYMBOLS

!Table[A comprehensive table showing display symbols and their corresponding indicators for the GP-PWM-10-FM solar charge controller. It includes symbols for Day Time: PV Charge Current (sun icon), Night Time (moon icon), Battery Voltage (battery icon with 'V'), Battery State of Charge (battery icon with percentage), and battery types: Sealed/Gel, AGM/LFP, and Flooded. Each symbol is represented by an icon in the left column, with its meaning in the right column, providing users with a quick reference for interpreting the controller's display.]

## OTHER SYMBOLS

!Table[A table showing additional symbols and their meanings for the GP-PWM-10-FM controller. It includes a USB symbol indicating 'USB charger ON (when charger is OFF, no symbol will show)', 'LOW' text indicating 'Battery voltage is lower than 11.0V', and 'Whole display will start to blink' indicating 'Battery voltage > 15.5V'. These symbols provide important status information about USB charging and battery voltage conditions.]

 | [ 13]

#

## BATTERY STATE OF CHARGE

!Table[A detailed table titled 'BATTERY STATE OF CHARGE' for the GP-PWM-10-FM controller. It has two columns: 'SYMBOLS' and 'BATTERY VOLTAGE'. The SYMBOLS column shows 5 battery icons with varying levels of charge, from full to empty. The BATTERY VOLTAGE column provides corresponding voltage ranges or conditions for each symbol, such as '>13.0V' for a full battery and '<11.5V' for an empty battery. Additionally, it includes percentage indicators (100%, 90%, and 0%) with their respective voltage conditions. The table also provides an equation for calculating State of Charge (SOC): SOC(%) = (Battery Voltage - 11.0V) x 100 / (13.0V - 11.0V).]

## USB CHARGING

The GP-PWM-10-FM offers a standard USB connector for delivering 5.0 VDC to small mobile appliances such as cell phones, tablets or small music players. This charging port is capable of supplying up to 1500 mA of current.

Remove the rubber cover of the USB terminal to access the terminal.

The USB charging port is always active when the USB symbol appears on the display.

The controller disables the USB charger automatically if the battery voltage drops below 11.0 VDC. If there is enough current from the PV panel/array available to charge the Battery to above 12.8 VDC, the USB terminal will be enabled again.

!Image[A warning symbol (yellow triangle with exclamation mark) indicating a cautionary message about USB charging connections. This symbol is used to emphasize the importance of proper USB device connection to prevent damage or malfunction.] WARNING: Do not connect the charging device anywhere else! USB-Negative contact is connected to battery negative.

[ 14] |

# 10. FREQUENTLY ASKED QUESTIONS

Before a problem is suspected with the system, read this section. There are numerous events that may appear as problems but are in fact perfectly normal. Please visit  for the most up-to-date FAQs.

**It seems like my flooded batteries are losing water over time.**

Flooded batteries may need to have distilled water added periodically to replace fluid loss during charging. Excessive water loss during a short period of time indicates the possibility of overcharging or aging batteries.

**When charging, my flooded batteries are emitting gas.**

During charging, hydrogen gas is generated within the battery. The gas bubbles stir the battery acid, allowing it to receive a fuller state of charge.
Important: Ensure batteries are in a well-ventilated space.

**My voltmeter shows a different reading than the GP-PWM-10-FM display.**

The meter value on the GP-PWM-10-FM display is an approximate reading intended for indication purposes only. There is an approximate 0.1 volt inherent error present that may be accentuated when compared with readings from another voltmeter.

There may be a slight difference between the battery voltage displayed on the GP-PWM-10-FM display and the battery voltage measured at the battery terminals. When troubleshooting using a voltmeter, check both the battery voltage at the GP-PWM-10-FM controller terminals and battery voltage at the battery terminals. If a difference of more than 0.5 volts is noted, this indicates a large voltage drop possibly caused by loose connections, long wire runs, small wire gauge, faulty wiring, a faulty voltmeter, or all the above. Consult the Suggested Minimum Wire Gauge chart in Section 5 for wiring suggestions and check all connections.

**What causes a warning signal and when are the warnings triggered?**

!Table[A table in the FAQ section detailing warning signals for the GP-PWM-10-FM controller. It has four columns: CONNECTION, WARNING, NOTES, and LCD. The table shows warning signals for battery and PV reverse polarity. For both cases, the WARNING is 'POL' displayed on the LCD and a constant audible alarm. For PV reverse polarity, there's a NOTE that the battery must be connected with correct polarity for the warning to function. The LCD column confirms 'POL' display for both cases. This table provides important troubleshooting information for users experiencing connection issues.]

**Why does the battery SOC% never reach 100%?**

A 100% value will only appear after a 2 hour Boost or Equalize charge has completed. The charge voltage must be maintained for an extended period of time to replenish the energy in the battery bank back to its rated capacity.

If the charge voltage cannot be maintained continuously, then the actual time it takes to complete Boost or Equalize charging may take much longer than 2 hours, even more than 1 day.

If loads are consuming more power than the solar panels can supply, then the battery bank cannot be charged to 100%.

!Image[Logo of Go Power!, a company associated with solar power and battery systems. The logo appears at the bottom of page 15, suggesting it's the manufacturer of the GP-PWM-10-FM solar charge controller discussed in the document.]
!Image[Logo of Dometic, another company likely associated with power systems or RV/marine equipment. The logo appears alongside the Go Power! logo, suggesting a partnership or association between the two companies in relation to the GP-PWM-10-FM product.]

 | [ 15]

# 11.

## How to Read this Section

 is split into three sub-sections, grouped by symptoms involving key components. Components considered irrelevant in a diagnosis are denoted 'Not Applicable' (N/A). A multimeter or voltmeter may be required for some procedures listed.

> It is imperative all electrical precautions stated in the Warning Section and outlined in the Installation Section are followed. Even if it appears the system is not functioning, it should be treated as a fully functioning system generating live power.

## 11.1 ERRORS

### Display Reading: Blank
Time of Day: Daytime/Nighttime

**Possible Causes:**
Battery or fuse connection and/or solar array connection
(Daytime only) or battery or fuse connection (Nighttime only).

**How to tell:**
1. Check the voltage at the controller battery terminals with a voltmeter and compare with a voltage reading at the battery terminals.
2. If there is no voltage reading at the controller battery terminals, the problem is in the wiring between the battery and the controller. If the battery voltage is lower than 6 volts the controller will not function.
3. For the solar array, repeat steps 1 and 2 substituting all battery terminals with solar array terminals.

**Remedy:**
Check all connections from the controller to the battery including checking for correct wire polarity. Check that all connections are clean, tight, and secure. Ensure the battery voltage is above 6 volts.

### Display Reading: Nighttime
Time of Day: Daytime

**Possible Causes:**
Panel is covered by something; PV panel is too dirty to supply a high enough voltage to charge the battery; PV panel is not connected.

**Remedy:**
Check the panel and to ensure it is not obscured. Clean the panel if it is dirty. Check that PV cables are connected to the controller.

## 11.2 PROBLEMS WITH VOLTAGE

### Voltage Reading: Inaccurate
Time of Day: Daytime/Nighttime

**Possible Causes:**
Excessive voltage drop from batteries to controller due to loose connections, small wire gauge or both.

**How to tell:**
1. Check the voltage at the controller battery terminals with a voltmeter and compare with the voltage reading at the battery terminals.
2. If there is a voltage discrepancy of more than 0.5 V, there is an excessive voltage drop.

**Remedy:**
Check all connections from the controller to the battery including checking for correct wire polarity. Check that all connections are clean, tight, and secure. Shorten the distance from the controller to battery or obtain larger gauge wire. It is also possible to double up the existing gauge wire (i.e. two wire runs) to simulate a larger gauge wire.

!Image[The image shows the Dometic company logo, which appears at the top of the page. This logo is consistently present across all three pages of the troubleshooting section, indicating it's likely a header element. The logo consists of the stylized word 'DOMETIC' in capital letters, with a curved line above it and the text 'Go Power!' inside an oval shape.]

#

## 11.3 PROBLEMS WITH CURRENT

### Current Reading: 0 A
Time of Day: Daytime, clear sunny skies

**Possible Cause:**
Current is being limited below 1 Amp as per normal operation or poor connection between solar array and controller.

**How to tell:**
1. The State of Charge (SOC) screen is close to 100% and the Sun and Battery icon are present with an arrow between.
2. With the solar array in sunlight, check the voltage at the controller solar array terminals with a voltmeter.
3. If there is no reading at the controller solar array terminals, the problem is somewhere in the wiring from the solar array to the controller.

**Remedy:**
Check all connections from the controller to the array including checking for correct wire polarity. Check that all connections are clean, tight, and secure. Continue with the solutions below for additional help on low current readings.

### Current Reading: Less than expected
Time of Day: Daytime, clear sunny skies

**Possible Causes:**
1. Current is being limited below 1 Amp as per normal operation.
2. Incorrect series/parallel configuration and/or wiring connections and/or wire gauge.
3. Dirty or shaded module or lack of sun.
4. Blown diode in solar module when two or more modules are connected in parallel.

**How to tell:**
1. Battery State of Charge screen is close to 100% and the Sun and Battery icon are present with an arrow in between.
2. Check that the modules and batteries are configured correctly. Check all wiring connections.
3. Modules look dirty, overhead object is shading modules or it is an overcast day in which a shadow cannot be cast.

> **Note:** Avoid any shading no matter how small. An object as small as a broomstick held across the solar module may cause the power output to be reduced. Overcast days may also cut the power output of the module

4. Disconnect one or both array wires from the controller. Take a voltage reading between the positive and negative array wire. A single 12 volt module should have an open circuit voltage between 17 and 22 volts. If you have more than one solar module, you will need to conduct this test between the positive and negative terminals of each module junction box with either the positive or the negative wires disconnected from the terminal.

**Remedy:**
1. Reconnect in correct configuration. Tighten all connections. Check wire gauge and length of wire run. Refer to Suggested Minimum Wire Gauge in Section 5.

2. Clean modules, clear obstruction or wait for conditions to clear.

3. If the open circuit voltage of a non-connected 12 volt module is lower than the manufacturer's specifications, the module may be faulty. Check for blown diodes in the solar module junction box, which may be shorting the power output of module.

!Image[The image displays the Dometic logo in the top right corner of the page. The logo design is consistent with the previous page, featuring the stylized word 'DOMETIC' with a curved line above it and the text 'Go Power!' inside an oval shape. This logo serves as a header element for the troubleshooting section.]

#

## 11.4 CONTROLLER FLASHING

**Possible Cause:**
This behavior is usually the controller dealing with a very high C or voltage rate (Above 15.5 volts). Even though the controller can handle up to 30A, if the battery capacity is too small for the panel input current. The voltage shoots up too high, too quickly, tripping the high voltage flashing. Solution increase battery capacity.

Can also be caused by an unregulated converter or alternator, in the system that is putting current to the batteries at the same time.

**Remedy:**
The solution here is to unplug shore power and reset the controller, which can be done in two ways:

1. **Soft Reset** - This is done by holding down all 4 buttons on the front of the controller for 15 seconds. If this does not work or you do not have a 4-button controller, a hard reset is required.
1. **Hard Reset** - Remove all 4 wires from the back of the controller for 15-20 minutes, then reconnect the wires. Determine if this clears the error state.

If the problem was "fixed," then it was because the user started using loads which divert some of the input current because the panels became dusty or shaded, or because there was less sunlight.

!Image[The image shows the Dometic company logo, which is identical to the logos on the previous two pages. It consists of the stylized word 'DOMETIC' in capital letters, with a curved oval shape above containing the text 'Go Power!'. The logo is positioned at the top of the page, serving as a header element for the troubleshooting section.]

# 12. LIMITED WARRANTY

Go Power! warrants the GP-PWM-10 for a period of five (5) years from the date of shipment from its factory. This warranty is valid against defects in materials and workmanship for the five(5) year warranty period. It is not valid against defects resulting from, but not limited to:

- Misuse and/or abuse, neglect or accident
- Exceeding the unit's design limits
- Improper installation, including, but not limited to, improper environmental protection and improper hook-up
- Acts of God, including lightning, floods, earthquakes, fire, and high winds
- Damage in handling, including damage encountered during shipment

This warranty shall be considered void if the warranted product is in any way opened or altered. The warranty will be void if any eyelet, rivets, or other fasteners used to seal the unit are removed or altered, or if the unit's serial number is in any way removed, altered, replaced, defaced, or rendered illegible.

## 12.1 REPAIR AND RETURN INFORMATION

Visit www. to read the "frequently asked questions" section of our website to troubleshoot the problem. If trouble persists:

1. Fill out our online Contact Us form or Live Chat with us
2. Email techsupport@
3. Return defective product to place of purchase

!Image[The image shows the logo of Go Power!, the manufacturer of the GP-PWM-10 product described in the warranty section. The logo appears at the bottom of the warranty information page, indicating its significance as the company brand.]
!Image[The image displays the logo of Dometic, which is associated with Go Power! as indicated by their joint appearance in the document footer. This suggests a partnership or ownership relationship between the two companies.]

 | [page 19]

!Image[This image combines the logos of Go Power! and Dometic side by side. The Go Power! logo on the left features a stylized 'GP' in a circular design, while the Dometic logo on the right displays 'DOMETIC' in capital letters with a distinctive arrow-like symbol above it. This dual branding appears at the bottom of the document, serving as an official seal or endorsement.]

© 2021 Go Power!

Worldwide Technical Support and Product Information gpelectric.com
Go Power! | Dometic
201-710 Redbrick Street Victoria, BC, V8T 5J3
Tel: **************

Manual_GP-PWM-10-FM
-----FINAL_MARKDOWN__end-----
