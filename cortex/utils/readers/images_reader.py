"""
Universal image-to-markdown converter with parallel processing and resumable capabilities.
"""

import base64
import hashlib
import json
import logging
import re
import threading
import time
import uuid
import unicodedata
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import asdict, dataclass
from typing import Any, Callable, Dict, List, Optional

from llama_index.core.base.llms.types import Image<PERSON>lock, TextBlock
from llama_index.core.prompts import ChatMessage, MessageRole

from services.claude import get_claude_llm
from utils.constants import CLAUDE_V3_5_SONNET_MODEL_ID
from utils.storage import StorageBackend, get_storage_backend

logger = logging.getLogger(__name__)


@dataclass
class ImageMetadata:
    """Metadata for an image being processed."""

    image_id: str
    summary: str
    actual_reference: Optional[str] = None
    is_complete: bool = True
    page_num: Optional[int] = None
    cross_page_context: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ImageMetadata":
        return cls(**data)


@dataclass
class TableMetadata:
    """Metadata for a table being processed."""

    table_id: str
    summary: str
    actual_reference: Optional[str] = None
    is_complete: bool = True
    page_num: Optional[int] = None
    cross_page_context: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "TableMetadata":
        return cls(**data)


@dataclass
class DiagramMetadata:
    """Metadata for diagrams, flowcharts, charts, etc."""

    diagram_id: str
    diagram_type: str  # flowchart, chart, diagram, graph, etc.
    summary: str
    actual_reference: Optional[str] = None
    is_complete: bool = True
    page_num: Optional[int] = None
    cross_page_context: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "DiagramMetadata":
        return cls(**data)


@dataclass
class ProcessingResult:
    """Result of processing an image."""

    image_index: int
    markdown_content: str
    images_metadata: List[ImageMetadata]
    tables_metadata: List[TableMetadata]
    diagrams_metadata: List[DiagramMetadata]
    processing_time: float

    def to_dict(self) -> Dict[str, Any]:
        return {
            "image_index": self.image_index,
            "markdown_content": self.markdown_content,
            "images_metadata": [img.to_dict() for img in self.images_metadata],
            "tables_metadata": [tbl.to_dict() for tbl in self.tables_metadata],
            "diagrams_metadata": [diag.to_dict() for diag in self.diagrams_metadata],
            "processing_time": self.processing_time,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ProcessingResult":
        return cls(
            image_index=data["image_index"],
            markdown_content=data["markdown_content"],
            images_metadata=[ImageMetadata.from_dict(img) for img in data["images_metadata"]],
            tables_metadata=[TableMetadata.from_dict(tbl) for tbl in data["tables_metadata"]],
            diagrams_metadata=[DiagramMetadata.from_dict(diag) for diag in data.get("diagrams_metadata", [])],
            processing_time=data["processing_time"],
        )


class ImagesReader:
    """Converter that processes any images to markdown with parallel processing and cache storage."""

    def __init__(
        self,
        images: List[bytes],
        cache_filename: str,
        cache_filepath: str,
        source_type: str = "generic",
        context: Optional[Dict[str, Any]] = None,
        max_workers: int = 4,
        storage_backend: Optional[StorageBackend] = None,
        progress_callback: Optional[Callable[[int, int], None]] = None,
        force_reprocess: bool = False,
    ):
        """
        Initialize the converter with images and configuration.

        Args:
            images: List of image bytes to process
            cache_filename: Name of the cache file to store results
            cache_file_path: Full path to the cache file for storage
            source_type: Type of source (e.g., "pdf", "website", "document")
            context: Optional context for processing
            max_workers: Number of parallel workers for processing
            storage_backend: Optional storage backend for caching
            progress_callback: Optional callback for progress updates (processed, total)
            force_reprocess: If True, ignore cached results and reprocess all images
        """

        self.images = images
        self.source_type = source_type
        self.context = context or {}
        self.max_workers = max_workers

        self.cache_filename = cache_filename
        self.storage_backend = storage_backend or get_storage_backend(cache_filepath)

        self.progress_callback = progress_callback
        self.force_reprocess = force_reprocess
        self.llm = get_claude_llm(model=CLAUDE_V3_5_SONNET_MODEL_ID)
        self.use_caching = len(images) > 5

        # Thread lock for cache operations to prevent race conditions
        self._cache_lock = threading.Lock()

    def _preprocess_llm_response(self, response_text: str) -> str:
        """Pre-process LLM response to fix common JSON formatting issues."""
        cleaned_text = response_text.strip()
        
        if cleaned_text.startswith("```json"):
            cleaned_text = cleaned_text.split("```json")[1].split("```")[0].strip()
        elif cleaned_text.startswith("```"):
            cleaned_text = cleaned_text.split("```")[1].split("```")[0].strip()
        
        # Find JSON object boundaries
        start_idx = cleaned_text.find('{')
        end_idx = cleaned_text.rfind('}')
        
        if start_idx == -1 or end_idx == -1:
            raise ValueError("No valid JSON object found in response")
        
        json_text = cleaned_text[start_idx:end_idx + 1]
        return json_text.strip()
        

    def _extract_and_parse_json(self, response_text: str, context: str = "") -> dict:
        """Extract and parse JSON from LLM response with robust error handling."""

        cleaned_text = response_text.strip()

        # Extract JSON from markdown code blocks
        if cleaned_text.startswith("```json"):
            cleaned_text = cleaned_text.split("```json")[1].split("```")[0].strip()
        elif cleaned_text.startswith("```"):
            cleaned_text = cleaned_text.split("```")[1].split("```")[0].strip()

        try:
            preprocessed_text = self._preprocess_llm_response(response_text)
            return json.loads(preprocessed_text)
        except json.JSONDecodeError as e:
            error_msg = f"Failed to parse JSON response for {context}. Raw response: {response_text[:500]}..."
            logger.error(error_msg)
            raise RuntimeError(f"LLM JSON parsing failed for {context}: {str(e)}") from e
        except Exception as e:
            error_msg = f"Unexpected error parsing JSON for {context}: {str(e)}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    def _retrieve_from_cache(self, cache_key: str) -> Optional[str]:
        """Retrieve data from cache using regex pattern with thread safety."""
        if not self.use_caching or self.force_reprocess:
            return None

        with self._cache_lock:
            try:
                cached_bytes = self.storage_backend.get(self.cache_filename)
                if cached_bytes:
                    cache_content = cached_bytes.decode('utf-8')
                    pattern = re.compile(
                        re.escape(f"-----{cache_key}__start-----")
                        + r"(.+?)"
                        + re.escape(f"-----{cache_key}__end-----"),
                        re.DOTALL,
                    )
                    match = pattern.search(cache_content)
                    if match:
                        logger.info(f"Cache hit for {cache_key}")
                        return match.group(1).strip()
                return None
            except Exception as exp:
                logger.warning(f"Error retrieving from cache {cache_key}: {exp}")
                return None

    def _store_to_cache(self, cache_key: str, data: str):
        """Store data to cache with regex markers with thread safety."""
        if not self.use_caching:
            return

        with self._cache_lock:
            try:
                cached_bytes = self.storage_backend.get(self.cache_filename)
                if cached_bytes:
                    cache_content = cached_bytes.decode('utf-8')
                else:
                    cache_content = ""

                existing_pattern = re.compile(
                    re.escape(f"-----{cache_key}__start-----") + r".+?" + re.escape(f"-----{cache_key}__end-----\n?"),
                    re.DOTALL,
                )
                cache_content = existing_pattern.sub("", cache_content)

                new_cache_entry = f"-----{cache_key}__start-----\n{data}\n-----{cache_key}__end-----\n"
                updated_content = cache_content + new_cache_entry
                self.storage_backend.set(self.cache_filename, updated_content)
                logger.debug(f"Cached data for {cache_key}")

            except Exception as exp:
                logger.error(f"Error storing to cache {cache_key}: {exp}")

    def _convert_to_processing_result(self, structured_content: dict, image_index: int) -> ProcessingResult:
        """Convert structured content to ProcessingResult."""
        markdown_text = structured_content.get("markdown_text", "")
        images_metadata = []
        tables_metadata = []
        diagrams_metadata = []

        for img in structured_content.get("images", []):
            image_reference = img.get("reference")
            if image_reference:
                unique_image_reference = uuid.uuid4().hex
                markdown_text = markdown_text.replace(f"{{{{{image_reference}}}}}", f"{{{{{unique_image_reference}}}}}")

                images_metadata.append(
                    ImageMetadata(
                        image_id=unique_image_reference,
                        summary=img.get("summary", ""),
                        actual_reference=img.get("actual_reference_in_document", ""),
                        is_complete=img.get("is_complete", True),
                        page_num=image_index,
                    )
                )

        for tbl in structured_content.get("tables", []):
            table_reference = tbl.get("reference")
            if table_reference:
                unique_table_reference = uuid.uuid4().hex
                markdown_text = markdown_text.replace(f"{{{{{table_reference}}}}}", f"{{{{{unique_table_reference}}}}}")

                tables_metadata.append(
                    TableMetadata(
                        table_id=unique_table_reference,
                        summary=tbl.get("summary", ""),
                        actual_reference=tbl.get("actual_reference_in_document", ""),
                        is_complete=tbl.get("is_complete", True),
                        page_num=image_index,
                    )
                )

        for diag in structured_content.get("diagrams", []):
            diagram_reference = diag.get("reference")
            if diagram_reference:
                unique_diagram_reference = uuid.uuid4().hex
                markdown_text = markdown_text.replace(
                    f"{{{{{diagram_reference}}}}}", f"{{{{{unique_diagram_reference}}}}}"
                )

                diagrams_metadata.append(
                    DiagramMetadata(
                        diagram_id=unique_diagram_reference,
                        diagram_type=diag.get("type", "diagram"),
                        summary=diag.get("summary", ""),
                        actual_reference=diag.get("actual_reference_in_document", ""),
                        is_complete=diag.get("is_complete", True),
                        page_num=image_index,
                    )
                )

        return ProcessingResult(
            image_index=image_index,
            markdown_content=markdown_text,
            images_metadata=images_metadata,
            tables_metadata=tables_metadata,
            diagrams_metadata=diagrams_metadata,
            processing_time=0.0,
        )

    def _get_source_instructions(self, source_type: str) -> str:
        """Get source-specific processing instructions."""
        instructions = {
            "pdf": """
            This is a PDF page. Pay attention to:
            - Academic/document formatting
            - Headers and footers
            - Multi-column layouts
            - Citations and references
            - Page numbers
            """,
            "website": """
            This is a website screenshot. Pay attention to:
            - Navigation elements
            - Headers and footers (website layout)
            - Sidebar content
            - Main content area
            - Interactive elements
            """,
            "document": """
            This is a document image. Pay attention to:
            - Document structure and formatting
            - Headings and sections
            - Lists and bullet points
            - Any embedded media
            """,
            "generic": """
            This is a general image. Extract all visible text and identify any structured content.
            """,
        }

        return instructions.get(source_type, instructions["generic"])

    def _extract_layout_info_with_llm(self, image_bytes: bytes, image_index: int) -> str:
        """Extract layout information using LLM to understand image."""

        cached_layout = self._retrieve_from_cache(f"layout_info_image_{image_index}")
        if cached_layout:
            logger.info(f"Using cached layout info for image {image_index}")
            return cached_layout

        source_instructions = self._get_source_instructions(self.source_type)

        prompt = f"""
        You are analyzing an image from a {self.source_type}. Extract the structure and layout information.

        {source_instructions}

        Please provide detailed analysis:
        1. Heading hierarchy (identify main headings, subheadings based on font size/style)
        2. Current section/chapter context if visible
        3. Content role in document flow (introduction, main content, conclusion, etc.)
        4. Layout structure (single/multi-column/grid, number of columns, etc.)
        5. Elements present in the image (text blocks, images, tables, charts, diagrams, flowcharts, graphs, etc.)
        6. Visual elements categorization:
           - Charts: bar charts, line graphs, pie charts, scatter plots, etc.
           - Diagrams: flowcharts, organizational charts, network diagrams, system diagrams, etc.
           - Tables: data tables, comparison tables, etc.
           - Images: photographs, illustrations, screenshots, etc.
        7. Text density and formatting (paragraphs, lists, code blocks, etc.)
        8. Visual hierarchy and organization
        9. Document context summary in 2-3 lines

        Return your analysis in this format:
        LAYOUT ANALYSIS:
        - Headings: [list main headings found with their hierarchy level]
        - Section Context: [current section/chapter if identifiable]
        - Content Role: [content's role in document flow]
        - Layout Structure: [detailed column/grid structure description]
        - Elements: [comprehensive list of all elements: text, images, tables, charts, diagrams, flowcharts, etc.]
        - Visual Elements:
          * Charts: [list any charts found with types]
          * Diagrams: [list any diagrams/flowcharts found with types]
          * Tables: [list any tables found]
          * Images: [list any images/photos found]
        - Text Formatting: [describe text density, formatting, lists, etc.]
        - Visual Hierarchy: [describe the visual organization and emphasis]
        - Context Summary: [2-3 line summary of page content and its role]
        """

        encoded_image = base64.b64encode(image_bytes).decode("utf-8")

        messages = [
            ChatMessage(
                role=MessageRole.USER,
                blocks=[TextBlock(text=prompt.strip()), ImageBlock(image=encoded_image, image_mimetype="image/png")],
            )
        ]

        response = self.llm.chat(messages=messages)
        result = response.message.content.strip()

        self._store_to_cache(f"layout_info_image_{image_index}", result)
        logger.info(f"Extracted and cached layout info for image {image_index}")
        return result

    def _process_single_image(self, image_bytes: bytes, image_index: int) -> ProcessingResult:
        """Process a single image to extract markdown content."""

        layout_information = self._extract_layout_info_with_llm(image_bytes, image_index)
        source_instructions = self._get_source_instructions(self.source_type)

        prompt = f"""
        You are analyzing an image from a {self.source_type}. Extract and structure the content as markdown.

        {source_instructions}

        LAYOUT ANALYSIS FROM PREVIOUS STEP:
        {layout_information}

        Using the layout analysis above to understand the structure, extract the content from this image.

        TASK: Analyze the image and return a JSON object with this exact structure:
        {{
            "markdown_text": "Complete text content with placeholder references like {{{{image_1}}}} for images, {{{{table_1}}}} for tables, and {{{{diagram_1}}}} for diagrams",
            "images": [
                {{
                    "reference": "image_1",
                    "summary": "Detailed description of what is shown in the image - describe visual content, data, etc.",
                    "actual_reference_in_document": "Fig. 1, Figure 1, etc. if visible in the image (use the exact reference text if available)",
                    "is_complete": true
                }}
            ],
            "tables": [
                {{
                    "reference": "table_1", 
                    "summary": "Detailed description of the table content and data - describe headers, rows, values, structure",
                    "actual_reference_in_document": "Tab. 1, Table 1, etc. if visible in the image (use the exact reference text if available)",
                    "is_complete": true
                }}
            ],
            "diagrams": [
                {{
                    "reference": "diagram_1",
                    "type": "flowchart|chart|graph|diagram|organizational_chart|process_flow|mind_map|timeline|network_diagram",
                    "summary": "Detailed description of the diagram content - describe structure, components, relationships, data flow, etc.",
                    "actual_reference_in_document": "Fig. 1, Figure 1, Chart A, etc. if visible in the image (use the exact reference text if available)",
                    "is_complete": true
                }}
            ]
        }}

        CRITICAL JSON FORMATTING RULES:
        1. Use the layout analysis to understand the document structure and hierarchy
        2. Extract ALL text content from the image in proper markdown format
        3. Identify images, figures, photos and replace with {{{{image_X}}}} placeholders
        4. Identify tables and replace with {{{{table_X}}}} placeholders
        5. Identify diagrams, charts, flowcharts, graphs, organizational charts, process flows, mind maps, timelines, network diagrams and replace with {{{{diagram_X}}}} placeholders
        6. For each image/table/diagram: Provide detailed textual summary of the content
        7. For diagrams: Specify the type and describe the structure, components, relationships, and data flow
        8. Set is_complete to true if the element is fully visible on this image
        9. Maintain natural text flow and proper markdown formatting while inserting placeholders
        10. Use the layout hierarchy information to create proper markdown headers

        CRITICAL: VALID JSON OUTPUT REQUIREMENTS:
        - ALL string values MUST be properly escaped for JSON
        - Convert ALL newlines in text to \\n (backslash-n)
        - Convert ALL quotes to \\" (backslash-quote) 
        - Convert ALL backslashes to \\\\ (double backslash)
        - Convert ALL tabs to \\t (backslash-t)
        - Do NOT put actual line breaks inside JSON string values
        - The markdown_text should contain proper markdown but escaped for JSON
        - Example: "markdown_text": "# Header\\n\\nParagraph text with \\"quotes\\" and more\\n\\n## Subheader"

        EXAMPLE OF CORRECT JSON FORMAT:
        {{
            "markdown_text": "# Document Title\\n\\nThis is a paragraph with \\"quoted text\\" and proper formatting.\\n\\n## Section 1\\n\\nContent goes here with {{{{image_1}}}}.\\n\\n### Subsection\\n\\n- List item 1\\n- List item 2",
            "images": [
                {{
                    "reference": "image_1",
                    "summary": "A chart showing data trends with bars and labels",
                    "actual_reference": "Figure 1",
                    "is_complete": true
                }}
            ],
            "tables": [],
            "diagrams": []
        }}

        Return ONLY the valid JSON object with properly escaped strings. No markdown code blocks, no additional text.
        """

        encoded_image = base64.b64encode(image_bytes).decode("utf-8")

        messages = [
            ChatMessage(
                role=MessageRole.USER,
                blocks=[
                    TextBlock(text=prompt.strip()),
                    ImageBlock(image=encoded_image, image_mimetype="image/png"),
                ],
            )
        ]

        response = self.llm.chat(messages=messages)
        response_text = response.message.content.strip()

        structured_content = self._extract_and_parse_json(response_text, f"image {image_index}")

        return self._convert_to_processing_result(structured_content, image_index)

    def _process_single_image_with_cache(
        self,
        image_bytes: bytes,
        image_index: int,
    ) -> ProcessingResult:
        """Process single image with individual caching."""
        image_key = f"image_{image_index}"
        cached_result = self._retrieve_from_cache(image_key)
        if cached_result:
            logger.info(f"Using cached result for image {image_index}")
            try:
                return ProcessingResult.from_dict(json.loads(cached_result))
            except (json.JSONDecodeError, Exception) as e:
                logger.warning(f"Failed to parse cached result for image {image_index}: {e}")

        start_time = time.time()
        result = self._process_single_image(image_bytes, image_index)
        result.processing_time = time.time() - start_time
        self._store_to_cache(image_key, json.dumps(result.to_dict()))
        return result

    def _process_images_parallel(self) -> List[ProcessingResult]:
        """Process images in parallel with individual caching."""
        results = [None] * len(self.images)
        processed_count = 0
        failed_images = []

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_index = {
                executor.submit(self._process_single_image_with_cache, image_bytes, index): index
                for index, image_bytes in enumerate(self.images)
            }

            for future in as_completed(future_to_index):
                index = future_to_index[future]

                try:
                    result = future.result()
                    results[index] = result
                    processed_count += 1

                    if self.progress_callback:
                        self.progress_callback(processed_count, len(self.images))

                    logger.debug(f"Completed processing image {index}")
                    
                except Exception as exp:
                    error_msg = f"Thread failed to process image {index}: {str(exp)}"
                    logger.error(error_msg)
                    failed_images.append((index, str(exp)))
                    raise RuntimeError(f"Image processing failed for image {index}: {str(exp)}") from exp
                
        if failed_images:
            error_details = "\n".join([f"Image {idx}: {err}" for idx, err in failed_images])
            logger.error(f"Failed to process {len(failed_images)} images:\n{error_details}")
            raise RuntimeError(f"Failed to process {len(failed_images)} images:\n{error_details}")

        successful_results = [r for r in results if r is not None]
        if len(successful_results) != len(self.images):
            missing_count = len(self.images) - len(successful_results)
            raise RuntimeError(f"Failed to process {missing_count} out of {len(self.images)} images")

        return successful_results

    def _enhance_with_context(self, results: List[ProcessingResult]) -> List[ProcessingResult]:
        """Enhance results with cross-image context, similar to PDF processing."""
        if len(results) <= 1:
            return results

        logger.info(f"Enhancing {len(results)} results with cross-image context...")

        # Group images for context enhancement (process in groups of 3)
        group_size = 3
        enhanced_results = []

        for start_idx in range(0, len(results), group_size):
            end_idx = min(start_idx + group_size, len(results))
            group_results = results[start_idx:end_idx]
            enhanced_group = self._enhance_group_with_context(group_results, start_idx, end_idx - 1)
            enhanced_results.extend(enhanced_group)

        return enhanced_results

    def _enhance_group_with_context(
        self, group_results: List[ProcessingResult], start_idx: int, end_idx: int
    ) -> List[ProcessingResult]:
        """Enhance a group of results with cross-image context."""

        if len(group_results) <= 1:
            return group_results

        context_text = ""
        all_images = {}
        all_tables = {}
        all_diagrams = {}

        for i, result in enumerate(group_results):
            context_text += f"Image {start_idx + i + 1}:\n{result.markdown_content}\n\n"

            for img_meta in result.images_metadata:
                all_images[img_meta.image_id] = img_meta
            for tbl_meta in result.tables_metadata:
                all_tables[tbl_meta.table_id] = tbl_meta
            for diag_meta in result.diagrams_metadata:
                all_diagrams[diag_meta.diagram_id] = diag_meta

        cache_key = f"enhancement_group_{start_idx}_{end_idx}"
        cached_enhancement = self._retrieve_from_cache(cache_key)

        if cached_enhancement:
            try:
                enhancement_data = json.loads(cached_enhancement)
                self._apply_enhancement_to_group(group_results, enhancement_data, all_images, all_tables, all_diagrams)
                return group_results
            except (json.JSONDecodeError, Exception) as e:
                logger.warning(f"Failed to parse cached enhancement for group {start_idx}-{end_idx}: {e}")

        enhancement_data = self._generate_context_enhancement(
            context_text, all_images, all_tables, all_diagrams, start_idx, end_idx
        )
        self._store_to_cache(cache_key, json.dumps(enhancement_data))
        self._apply_enhancement_to_group(group_results, enhancement_data, all_images, all_tables, all_diagrams)

        return group_results

    def _generate_context_enhancement(
        self, context_text: str, all_images: Dict, all_tables: Dict, all_diagrams: Dict, start_idx: int, end_idx: int
    ) -> Dict[str, Any]:
        """Generate context enhancement using LLM analysis."""

        prompt = f"""
        You are analyzing a section of a {self.source_type} document (images {start_idx + 1} to {end_idx + 1}). 
        Using the multi-image context, provide enhanced summaries for images, tables, and diagrams, identify actual references, and detect headers/footers.

        DOCUMENT SECTION CONTENT:
        {context_text}

        CURRENT IMAGE SUMMARIES:
        {json.dumps({k: v.to_dict() for k, v in all_images.items()}, indent=2)}

        CURRENT TABLE SUMMARIES:
        {json.dumps({k: v.to_dict() for k, v in all_tables.items()}, indent=2)}

        CURRENT DIAGRAM SUMMARIES:
        {json.dumps({k: v.to_dict() for k, v in all_diagrams.items()}, indent=2)}

        TASK: Enhance the summaries with better context, identify actual references, and detect headers/footers. Return a JSON object:
        {{
            "actual_references_found": {{
                "images": ["Fig. 1", "Figure 2", "Image A"],
                "tables": ["Table 1", "Table 2", "Tbl. 3"],  
                "diagrams": ["Chart 1", "Diagram A", "Flowchart B"]
            }},
            "headers_and_footers": {{
                "headers": ["Header text that appears at top of pages", "Company Name", "Document Title"],
                "footers": ["Footer text that appears at bottom", "Page numbers", "Copyright text", "www.company.com"]
            }},
            "enhanced_images": {{
                "image_reference_id": {{
                    "summary": "Enhanced detailed description with cross-image context",
                    "actual_reference": "Updated reference if found (Fig. 1, Figure 1, etc.)",
                    "is_complete": true,
                    "cross_page_context": "Additional context from surrounding images"
                }}
            }},
            "enhanced_tables": {{
                "table_reference_id": {{
                    "summary": "Enhanced detailed description with cross-image context", 
                    "actual_reference": "Updated reference if found (Table 1, etc.)",
                    "is_complete": true,
                    "cross_page_context": "Additional context, note if table spans multiple images"
                }}
            }},
            "enhanced_diagrams": {{
                "diagram_reference_id": {{
                    "summary": "Enhanced detailed description with cross-image context",
                    "actual_reference": "Updated reference if found (Chart 1, etc.)",
                    "is_complete": true,
                    "cross_page_context": "Additional context from surrounding images"
                }}
            }}
        }}

        INSTRUCTIONS:
        1. Scan the document content and identify ALL actual references to images/tables/diagrams
        2. List them in the actual_references_found section (e.g., "Fig. 1", "Figure 2", "Table 1", "Chart A")
        3. Include variations like: Fig., Figure, Chart, Diagram, Table, Tbl., Graph, etc.
        4. CRITICAL: Identify common headers and footers that appear across multiple images:
           - Headers: Text that consistently appears at the top (company names, document titles, navigation, page headers, etc.)
           - Footers: Text that consistently appears at the bottom (page numbers, copyright, URLs, website footers, etc.)
           - Look for repetitive text patterns that appear in multiple images at similar positions
           - List exact text strings for headers and footers so they can be removed programmatically
           - Include partial matches (e.g., if "Page 1", "Page 2" appear, include "Page" as a pattern)
        5. Provide enhanced CONTENT summaries using the full section context:
           - For images: Describe what is visually shown, data represented, text content within images
           - For tables: Describe the actual data, column headers, key values, trends, totals
           - For diagrams: Describe the structure, flow, relationships, components, data representation
        6. Identify if elements span multiple images (set is_complete accordingly)
        7. Add cross-image context that helps understand the content better
        8. For split elements, describe the complete structure and all visible data

        HEADER/FOOTER DETECTION EXAMPLES:
        - Headers: "Company ABC", "Quarterly Report 2024", "Chapter 1: Introduction", "Navigation Menu"
        - Footers: "Page", "Copyright 2024", "www.company.com", "Confidential", "All Rights Reserved"
        - Look for text that appears at consistent positions across multiple images

        Return ONLY the JSON object with properly escaped strings.
        """

        messages = [ChatMessage(role=MessageRole.USER, blocks=[TextBlock(text=prompt.strip())])]

        response = self.llm.chat(messages=messages)
        response_text = response.message.content.strip()

        # Use robust JSON parsing
        enhancement_data = self._extract_and_parse_json(response_text, f"context enhancement {start_idx + 1}-{end_idx + 1}")
        
        # Log LLM-detected headers and footers
        headers_footers = enhancement_data.get("headers_and_footers", {})
        detected_headers = headers_footers.get("headers", [])
        detected_footers = headers_footers.get("footers", [])
        
        if detected_headers:
            logger.info(f"LLM detected {len(detected_headers)} headers for images {start_idx + 1}-{end_idx + 1}: {detected_headers}")
        if detected_footers:
            logger.info(f"LLM detected {len(detected_footers)} footers for images {start_idx + 1}-{end_idx + 1}: {detected_footers}")
        
        logger.info(f"Generated context enhancement for images {start_idx + 1}-{end_idx + 1}")
        return enhancement_data

    def _apply_enhancement_to_group(
        self,
        group_results: List[ProcessingResult],
        enhancement_data: Dict[str, Any],
        all_images: Dict,
        all_tables: Dict,
        all_diagrams: Dict,
    ):
        """Apply enhancement data to the group results."""

        # Update images with enhanced summaries
        for ref_id, enhanced_data in enhancement_data.get("enhanced_images", {}).items():
            if ref_id in all_images:
                all_images[ref_id].summary = enhanced_data.get("summary", all_images[ref_id].summary)
                all_images[ref_id].actual_reference = enhanced_data.get(
                    "actual_reference", all_images[ref_id].actual_reference
                )
                all_images[ref_id].is_complete = enhanced_data.get("is_complete", all_images[ref_id].is_complete)
                all_images[ref_id].cross_page_context = enhanced_data.get("cross_page_context", "")

        # Update tables with enhanced summaries
        for ref_id, enhanced_data in enhancement_data.get("enhanced_tables", {}).items():
            if ref_id in all_tables:
                all_tables[ref_id].summary = enhanced_data.get("summary", all_tables[ref_id].summary)
                all_tables[ref_id].actual_reference = enhanced_data.get(
                    "actual_reference", all_tables[ref_id].actual_reference
                )
                all_tables[ref_id].is_complete = enhanced_data.get("is_complete", all_tables[ref_id].is_complete)
                all_tables[ref_id].cross_page_context = enhanced_data.get("cross_page_context", "")

        # Update diagrams with enhanced summaries
        for ref_id, enhanced_data in enhancement_data.get("enhanced_diagrams", {}).items():
            if ref_id in all_diagrams:
                all_diagrams[ref_id].summary = enhanced_data.get("summary", all_diagrams[ref_id].summary)
                all_diagrams[ref_id].actual_reference = enhanced_data.get(
                    "actual_reference", all_diagrams[ref_id].actual_reference
                )
                all_diagrams[ref_id].is_complete = enhanced_data.get("is_complete", all_diagrams[ref_id].is_complete)
                all_diagrams[ref_id].cross_page_context = enhanced_data.get("cross_page_context", "")

        actual_refs = enhancement_data.get("actual_references_found", {})
        headers_footers = enhancement_data.get("headers_and_footers", {})
        
        for i, result in enumerate(group_results):
            absolute_image_index = result.image_index
            total_images = len(self.images)
            
            cleaned_content = self._remove_headers_and_footers_from_content(
                result.markdown_content, 
                headers_footers,
                absolute_image_index,
                total_images
            )
            result.markdown_content = self._replace_actual_references_with_uuids(
                cleaned_content, actual_refs, all_images, all_tables, all_diagrams
            )

    def _remove_headers_and_footers_from_content(
        self, 
        text: str, 
        headers_footers: Dict[str, List[str]], 
        image_index: int, 
        total_images: int
    ) -> str:
        """Remove common headers and footers from text, but preserve them on first/last images."""
        cleaned_text = text
        headers_removed = 0
        footers_removed = 0
        
        # Remove headers only if it's NOT the first image
        if image_index != 0:
            for header in headers_footers.get("headers", []):
                if header.strip():
                    pattern = re.escape(header.strip()).replace(r'\ ', r'\s+')
                    before_removal = cleaned_text
                    cleaned_text = re.sub(pattern, "", cleaned_text, flags=re.IGNORECASE)
                    if cleaned_text != before_removal:
                        headers_removed += 1
                        logger.debug(f"Removed LLM-detected header from image {image_index}: '{header.strip()}'")
        
        # Remove footers only if it's NOT the last image
        if image_index != (total_images - 1):  # Not the last image
            for footer in headers_footers.get("footers", []):
                if footer.strip():
                    pattern = re.escape(footer.strip()).replace(r'\ ', r'\s+')
                    before_removal = cleaned_text
                    cleaned_text = re.sub(pattern, "", cleaned_text, flags=re.IGNORECASE)
                    if cleaned_text != before_removal:
                        footers_removed += 1
                        logger.debug(f"Removed LLM-detected footer from image {image_index}: '{footer.strip()}'")
        
        if headers_removed > 0 or footers_removed > 0:
            logger.info(f"LLM-detected header/footer removal for image {image_index}: {headers_removed} headers, {footers_removed} footers")
        
        cleaned_text = re.sub(r'\n\s*\n\s*\n+', '\n\n', cleaned_text)
        lines = cleaned_text.split('\n')
        cleaned_lines = [line.rstrip() for line in lines]
        cleaned_text = '\n'.join(cleaned_lines)
        
        return cleaned_text.strip()

    def _replace_actual_references_with_uuids(
        self, 
        text: str, 
        actual_refs: Dict[str, List[str]], 
        all_images: Dict, 
        all_tables: Dict, 
        all_diagrams: Dict
    ) -> str:
        """Replace actual references in text with UUID placeholders."""
        updated_text = text
        
        # Replace image references
        for img_ref in actual_refs.get("images", []):
            for img_id, img_meta in all_images.items():
                if img_meta.actual_reference == img_ref:
                    updated_text = updated_text.replace(img_ref, f"{{{{{img_id}}}}}")
                    break
        
        # Replace table references
        for tbl_ref in actual_refs.get("tables", []):
            for tbl_id, tbl_meta in all_tables.items():
                if tbl_meta.actual_reference == tbl_ref:
                    updated_text = updated_text.replace(tbl_ref, f"{{{{{tbl_id}}}}}")
                    break
        
        # Replace diagram references
        for diag_ref in actual_refs.get("diagrams", []):
            for diag_id, diag_meta in all_diagrams.items():
                if diag_meta.actual_reference == diag_ref:
                    updated_text = updated_text.replace(diag_ref, f"{{{{{diag_id}}}}}")
                    break
        
        return updated_text

    def _combine_to_markdown(self, results: List[ProcessingResult]) -> str:
        """Combine results into final markdown with proper image/table/diagram references, matching PDF reader format."""
        if not results:
            return ""

        sorted_results = sorted(results, key=lambda r: r.image_index)

        combined_content = []
        all_images = {}
        all_tables = {}
        all_diagrams = {}

        for result in sorted_results:
            for img_meta in result.images_metadata:
                all_images[img_meta.image_id] = img_meta
            for tbl_meta in result.tables_metadata:
                all_tables[tbl_meta.table_id] = tbl_meta
            for diag_meta in result.diagrams_metadata:
                all_diagrams[diag_meta.diagram_id] = diag_meta

            combined_content.append(result.markdown_content)

        final_markdown = "\n\n".join(combined_content)
        for image_id, image_meta in all_images.items():
            replace_text = f"!Image[{image_meta.summary}]"
            if image_meta.actual_reference:
                replace_text += f"({image_meta.actual_reference})"
            final_markdown = final_markdown.replace(f"{{{{{image_id}}}}}", replace_text)

        for table_id, table_meta in all_tables.items():
            replace_text = f"!Table[{table_meta.summary}]"
            if table_meta.actual_reference:
                replace_text += f"({table_meta.actual_reference})"
            final_markdown = final_markdown.replace(f"{{{{{table_id}}}}}", replace_text)

        for diagram_id, diagram_meta in all_diagrams.items():
            replace_text = f"!{diagram_meta.diagram_type.title()}[{diagram_meta.summary}]"
            if diagram_meta.actual_reference:
                replace_text += f"({diagram_meta.actual_reference})"
            final_markdown = final_markdown.replace(f"{{{{{diagram_id}}}}}", replace_text)

        return final_markdown

    def convert_to_markdown(self) -> str:
        """
        Convert images to markdown with automatic parallelization and caching.

        Returns:
            Combined markdown content
        """
        if not self.images:
            return ""

        cached_markdown = self._retrieve_from_cache("FINAL_MARKDOWN")
        if cached_markdown:
            logger.info(f"Using cached markdown for FINAL_MARKDOWN")
            return cached_markdown

        logger.info(f"Processing {len(self.images)} images")

        # Process images in parallel
        results = self._process_images_parallel()

        # Enhance with context
        enhanced_results = self._enhance_with_context(results)

        # Combine to final markdown
        final_markdown = self._combine_to_markdown(enhanced_results)

        # Cache the final result
        self._store_to_cache("FINAL_MARKDOWN", final_markdown)

        logger.info(f"Completed processing {len(self.images)} images")
        return final_markdown
