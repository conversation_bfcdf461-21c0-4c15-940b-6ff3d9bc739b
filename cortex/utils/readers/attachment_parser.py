"""
Attachment parser using PDF reader components from pdf-reader branch.
This module provides parsing capabilities for various file types without modifying existing implementations.
"""
import logging
import mimetypes
import os
from typing import Optional, Dict, Any

from utils.readers.pdf_reader import PDFReader
from utils.readers.images_reader import ImagesReader

logger = logging.getLogger(__name__)


class AttachmentParser:
    """
    Parser for various attachment types using the PDF reader components.
    Can be used independently or integrated with existing readers.
    """
    
    def __init__(self, cache_dir: str = "./knowledge_base_indexes/cache"):
        """
        Initialize the attachment parser.
        
        Args:
            cache_dir: Directory for caching processed results
        """
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
        
    def parse_attachment(self, content_bytes: bytes, filename: str, attachment_metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Parse attachment content based on file type.
        
        Args:
            content_bytes: Raw file content as bytes
            filename: Name of the file
            attachment_metadata: Optional metadata about the attachment
            
        Returns:
            Dictionary containing parsed content and metadata
        """
        if not content_bytes:
            return {
                "success": False,
                "error": "No content provided",
                "parsed_content": "",
                "metadata": attachment_metadata or {}
            }
            
        # Determine file type
        file_extension = os.path.splitext(filename)[1].lower()
        mime_type, _ = mimetypes.guess_type(filename)
        
        try:
            if file_extension == '.pdf' or mime_type == 'application/pdf':
                return self._parse_pdf(content_bytes, filename, attachment_metadata)
            elif mime_type and mime_type.startswith('image/'):
                return self._parse_image(content_bytes, filename, attachment_metadata)
            else:
                return {
                    "success": True,
                    "parsed_content": f"Attachment: {filename}\nFile Type: {mime_type or 'unknown'}\nSize: {len(content_bytes)} bytes\n\nContent parsing not supported for this file type.",
                    "metadata": {
                        **(attachment_metadata or {}),
                        "file_extension": file_extension,
                        "mime_type": mime_type,
                        "file_size": len(content_bytes),
                        "parsing_supported": False
                    }
                }
                
        except Exception as e:
            logger.error(f"Error parsing attachment {filename}: {e}")
            return {
                "success": False,
                "error": str(e),
                "parsed_content": f"Attachment: {filename}\nFile Type: {mime_type or 'unknown'}\nSize: {len(content_bytes)} bytes\n\nParsing failed: {str(e)}",
                "metadata": {
                    **(attachment_metadata or {}),
                    "file_extension": file_extension,
                    "mime_type": mime_type,
                    "file_size": len(content_bytes),
                    "parsing_error": str(e)
                }
            }
    
    def _parse_pdf(self, content_bytes: bytes, filename: str, attachment_metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Parse PDF content using PDFReader."""
        logger.info(f"Parsing PDF: {filename}")
        
        cache_filepath = os.path.join(self.cache_dir, "pdf_cache")
        os.makedirs(cache_filepath, exist_ok=True)
        
        with PDFReader(
            content=content_bytes,
            filename=filename,
            cache_filepath=cache_filepath
        ) as pdf_reader:
            parsed_content = pdf_reader.execute()
            
        return {
            "success": True,
            "parsed_content": parsed_content,
            "metadata": {
                **(attachment_metadata or {}),
                "file_extension": ".pdf",
                "mime_type": "application/pdf",
                "file_size": len(content_bytes),
                "parsing_method": "PDFReader",
                "parsing_supported": True
            }
        }
    
    def _parse_image(self, content_bytes: bytes, filename: str, attachment_metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Parse image content using ImagesReader."""
        logger.info(f"Parsing image: {filename}")
        
        cache_filepath = os.path.join(self.cache_dir, "image_cache")
        os.makedirs(cache_filepath, exist_ok=True)
        
        images_reader = ImagesReader(
            images=[content_bytes],
            cache_filename=f"{filename.replace('.', '_')}_cache.txt",
            cache_filepath=cache_filepath,
            source_type="attachment",
            context={
                "filename": filename,
                **(attachment_metadata or {})
            }
        )
        parsed_content = images_reader.convert_to_markdown()
        
        return {
            "success": True,
            "parsed_content": parsed_content,
            "metadata": {
                **(attachment_metadata or {}),
                "file_extension": os.path.splitext(filename)[1].lower(),
                "mime_type": mimetypes.guess_type(filename)[0],
                "file_size": len(content_bytes),
                "parsing_method": "ImagesReader",
                "parsing_supported": True
            }
        }


# Convenience function for easy integration
def parse_attachment_content(content_bytes: bytes, filename: str, attachment_metadata: Optional[Dict[str, Any]] = None, cache_dir: str = "./knowledge_base_indexes/cache") -> Dict[str, Any]:
    """
    Convenience function to parse attachment content.
    
    Args:
        content_bytes: Raw file content as bytes
        filename: Name of the file
        attachment_metadata: Optional metadata about the attachment
        cache_dir: Directory for caching processed results
        
    Returns:
        Dictionary containing parsed content and metadata
    """
    parser = AttachmentParser(cache_dir=cache_dir)
    return parser.parse_attachment(content_bytes, filename, attachment_metadata)
