"""
Simplified PDF reader using the universal ImageToMarkdownConverter.
"""
import logging
from typing import Dict, Any, Optional, Callable
from io import BytesIO

import pymupdf
from utils.readers.images_reader import ImagesReader

logger = logging.getLogger(__name__)


class PDFReader:
    """
    Simple PDF reader that extracts images and uses ImagesReader.
    """

    def __init__(
        self, 
        content: bytes, 
        filename: str,
        cache_filepath: str,
        max_workers: int = 4,
        progress_callback: Optional[Callable[[int, int], None]] = None
    ) -> None:
        """
        Initialize the PDF reader.
        
        Args:
            content: PDF content as bytes
            filename: Name of the PDF file
            cache_filepath: Path to cache file for storing results
            max_workers: Maximum number of parallel workers
            progress_callback: Optional callback for progress updates
        """
        self.filename = filename
        self.cache_filepath = cache_filepath
        self.max_workers = max_workers
        self.progress_callback = progress_callback
        
        # Initialize PDF document
        pdf_stream = BytesIO(content)
        self.pdf_document = pymupdf.open(stream=pdf_stream, filetype="pdf")

    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit - cleanup resources."""
        if hasattr(self, 'pdf_document') and self.pdf_document:
            self.pdf_document.close()

    
    def _extract_text_from_pdf(self) -> str:
        """Extract raw text from the PDF document."""
        text_content = ""
        for page in self.pdf_document:
            text_content += page.get_text()
        
        return text_content.strip()
    
    def _extract_page_images_and_contents(self) -> tuple[list[bytes], list[str]]:
        """Extract all pages as images with error handling."""
        page_images = []
        text_contents = []
    
        for page_num in range(self.pdf_document.page_count):
            page = self.pdf_document[page_num]
            matrix = pymupdf.Matrix(2, 2)
            pix = page.get_pixmap(matrix=matrix, alpha=False)
            img_bytes = pix.pil_tobytes(format="PNG")
            
            if not img_bytes:
                raise RuntimeError(f"Failed to extract image from page {page_num + 1}")
            
            page_images.append(img_bytes)
            text_content = page.get_text()
            text_contents.append(text_content.strip())
                
        if not page_images:
            raise RuntimeError(f"No pages successfully extracted from PDF: {self.filename}")
            
        logger.info(f"Successfully extracted {len(page_images)} pages from {self.filename}")
        return page_images, text_contents
    
    def execute(self, force_reprocess: bool = False) -> Dict[str, Any]:
        """
        Execute the PDF processing pipeline with fail-fast error handling.
        
        Args:
            force_reprocess: If True, ignore cached results and reprocess
            
        Returns:
            Dictionary containing processed content and metadata
            
        Raises:
            RuntimeError: If any step of the processing fails
        """
        logger.info(f"Processing PDF: {self.filename} ({self.pdf_document.page_count} pages)")
        
        page_images, page_contents = self._extract_page_images_and_contents()

        converter = ImagesReader(
            images=page_images,
            cache_filename=f"{self.filename.replace('.pdf', '').replace(' ', '-')}_cache.txt",
            cache_filepath=self.cache_filepath,
            source_type="pdf",
            context={
                "filename": self.filename,
                "total_pages": len(page_images),
                "text_contents": page_contents
            },
            max_workers=self.max_workers,
            progress_callback=self.progress_callback,
            force_reprocess=force_reprocess
        )
        
        markdown_content = converter.convert_to_markdown()
        logger.info(f"PDF processing completed successfully for {self.filename}")
        return markdown_content