"""
Example of how to integrate PDF reader with Happy<PERSON>ox reader without modifying existing code.
This shows how to use the attachment parser with your existing HappyFox implementation.
"""
import logging
from typing import List, Optional, Dict, Any
from llama_index.core.schema import Document

from utils.readers.happyfox.happyfox_reader import HappyFoxKnowledgeBaseReader
from utils.readers.attachment_parser import parse_attachment_content

logger = logging.getLogger(__name__)


class HappyFoxReaderWithPDFParsing(HappyFoxKnowledgeBaseReader):
    """
    Extended HappyFox reader that adds PDF parsing capabilities without modifying the original.
    This is an example of how to integrate the PDF reader components.
    """
    
    def __init__(self, *args, **kwargs):
        """Initialize with PDF parsing enabled."""
        # Extract PDF parsing specific arguments
        self.enable_pdf_parsing = kwargs.pop('enable_pdf_parsing', True)
        self.pdf_cache_dir = kwargs.pop('pdf_cache_dir', "./knowledge_base_indexes/cache")
        
        # Initialize the parent class
        super().__init__(*args, **kwargs)
        
    async def process_single_attachment(self, article: dict, attachment: dict) -> Optional[Document]:
        """
        Override the attachment processing to add PDF parsing capabilities.
        Falls back to original behavior if PDF parsing is disabled or fails.
        """
        if not self.enable_pdf_parsing:
            # Use original implementation
            return await super().process_single_attachment(article, attachment)
            
        attachment_id = attachment.get("id")
        attachment_name = attachment.get("name", f"attachment_{attachment_id}")
        attachment_url = attachment.get("url", "")
        is_embedded = attachment.get("embedded", False)

        logger.info(f"Processing {'embedded image' if is_embedded else 'attachment'} {attachment_id}: {attachment_name} with PDF parsing")

        if not attachment_url:
            logger.warning(f"No URL found for attachment {attachment_id}")
            return None

        try:
            # Download attachment content to memory
            content_bytes = await self.client.download_attachment_to_memory(attachment_url)
            if not content_bytes:
                logger.warning(f"Failed to download attachment {attachment_id}")
                # Fall back to original implementation
                return await super().process_single_attachment(article, attachment)

            # Parse the attachment using PDF reader components
            attachment_metadata = {
                "parent_article_id": article.get("id"),
                "parent_article_title": article.get("title"),
                "attachment_id": attachment_id,
                "attachment_url": attachment_url,
                "section_id": article.get("section", {}).get("id"),
                "is_embedded_image": is_embedded
            }
            
            parse_result = parse_attachment_content(
                content_bytes=content_bytes,
                filename=attachment_name,
                attachment_metadata=attachment_metadata,
                cache_dir=self.pdf_cache_dir
            )
            
            if parse_result["success"]:
                # Create document with parsed content
                return Document(
                    text=parse_result["parsed_content"],
                    metadata={
                        "source": "HappyFox",
                        "content_type": "embedded_image" if is_embedded else "attachment",
                        "attachment_name": attachment_name,
                        **parse_result["metadata"],
                        "parsed_with_pdf_reader": True
                    }
                )
            else:
                # Parsing failed, fall back to original implementation
                logger.warning(f"PDF parsing failed for {attachment_name}: {parse_result.get('error', 'Unknown error')}")
                return await super().process_single_attachment(article, attachment)
                
        except Exception as e:
            logger.error(f"Error in PDF parsing for attachment {attachment_id}: {e}")
            # Fall back to original implementation
            return await super().process_single_attachment(article, attachment)


# Example usage function
async def load_happyfox_data_with_pdf_parsing(
    sections: Optional[List[int]] = None,
    api_key: Optional[str] = None,
    auth_code: Optional[str] = None,
    domain: Optional[str] = None,
    staff_credentials: Optional[Dict[str, str]] = None,
    oauth_credentials: Optional[Dict[str, str]] = None,
    oauth_token: Optional[str] = None,
    enable_pdf_parsing: bool = True,
    pdf_cache_dir: str = "./knowledge_base_indexes/cache"
) -> List[Document]:
    """
    Load HappyFox data with optional PDF parsing capabilities.
    
    Args:
        sections: List of section IDs to filter
        api_key: HappyFox API key
        auth_code: HappyFox auth code
        domain: HappyFox domain
        staff_credentials: Staff login credentials
        oauth_credentials: OAuth credentials
        oauth_token: OAuth access token
        enable_pdf_parsing: Whether to enable PDF parsing for attachments
        pdf_cache_dir: Directory for PDF parsing cache
        
    Returns:
        List of documents including parsed attachments
    """
    reader = HappyFoxReaderWithPDFParsing(
        sections=sections,
        api_key=api_key,
        auth_code=auth_code,
        domain=domain,
        staff_credentials=staff_credentials,
        oauth_credentials=oauth_credentials,
        oauth_token=oauth_token,
        enable_pdf_parsing=enable_pdf_parsing,
        pdf_cache_dir=pdf_cache_dir
    )
    
    return await reader.load_data()


# Simple function to parse individual attachments
async def parse_happyfox_attachment(attachment_url: str, filename: str, happyfox_client) -> Dict[str, Any]:
    """
    Parse a single HappyFox attachment using PDF reader components.
    
    Args:
        attachment_url: URL of the attachment
        filename: Name of the attachment file
        happyfox_client: HappyFox client instance for downloading
        
    Returns:
        Dictionary with parsing results
    """
    try:
        content_bytes = await happyfox_client.download_attachment_to_memory(attachment_url)
        if not content_bytes:
            return {"success": False, "error": "Failed to download attachment"}
            
        return parse_attachment_content(content_bytes, filename)
        
    except Exception as e:
        return {"success": False, "error": str(e)}
