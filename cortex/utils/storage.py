"""
Storage utilities for resumable processing with support for local and S3 storage.
"""

import json
import logging
import os
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Optional

import boto3
from django.conf import settings

logger = logging.getLogger(__name__)


class StorageBackend(ABC):
    """Abstract base class for storage backends."""

    @abstractmethod
    def exists(self, filename: str) -> bool:
        """Check if a filename exists in storage."""
        pass

    @abstractmethod
    def get(self, filename: str) -> Optional[bytes]:
        """Get raw bytes from storage."""
        pass

    @abstractmethod
    def set(self, filename: str, data: Any) -> None:
        """Store data as bytes. Consumer is responsible for serialization."""
        pass

    @abstractmethod
    def delete(self, filename: str) -> None:
        """Delete data from storage."""
        pass


class LocalStorageBackend(StorageBackend):
    """Local file system storage backend."""

    def __init__(self, prefix_path: str = ""):
        self.base_path = Path(os.path.join("./storage", prefix_path)).resolve()
        self.base_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"Using local storage at: {self.base_path}")

    def _get_file_path(self, filename: str) -> Path:
        """Get the full file path for a filename."""
        return self.base_path / filename

    def exists(self, filename: str) -> bool:
        """Check if a filename exists in local storage."""
        return self._get_file_path(filename).exists()

    def get(self, filename: str) -> Optional[bytes]:
        """Get raw bytes from local storage."""
        file_path = self._get_file_path(filename)
        if not file_path.exists():
            return None

        try:
            with open(file_path, "rb") as f:
                return f.read()
        except IOError as e:
            logger.error(f"Error reading from local storage file {filename}: {e}")
            return None

    def set(self, filename: str, data: Any) -> None:
        """Store raw data in local storage."""
        file_path = self._get_file_path(filename)

        try:
            if isinstance(data, bytes):
                content = data
            elif isinstance(data, str):
                content = data.encode("utf-8")
            elif isinstance(data, (dict, list)):
                content = json.dumps(data, indent=2, ensure_ascii=False).encode("utf-8")
            else:
                content = str(data).encode("utf-8")

            with open(file_path, "wb") as f:
                f.write(content)
        except IOError as e:
            logger.error(f"Error writing to local storage file {filename}: {e}")
            raise

    def delete(self, filename: str) -> None:
        """Delete data from local storage."""
        file_path = self._get_file_path(filename)
        if file_path.exists():
            file_path.unlink()


class S3StorageBackend(StorageBackend):
    """AWS S3 storage backend."""

    def __init__(self, prefix_path: str = ""):
        self.prefix_path = prefix_path

        kwargs = {
            "service_name": "s3",
            "aws_access_key_id": settings.AWS_ACCESS_KEY_ID,
            "aws_secret_access_key": settings.AWS_SECRET_ACCESS_KEY,
            "endpoint_url": settings.AWS_BUCKET_URL,
            "region_name": settings.AWS_REGION,
        }
        self.s3_client = boto3.client(**kwargs)

    def _get_key(self, filename: str) -> str:
        """Get the full S3 key."""
        return os.path.join(self.prefix_path, filename)

    def exists(self, filename: str) -> bool:
        """Check if a filename exists in S3."""
        try:
            self.s3_client.head_object(Bucket=settings.AWS_BUCKET_NAME, Key=self._get_key(filename))
            return True
        except Exception as e:
            logger.error(f"Error checking S3 file {filename}: {e}")
            return False

    def get(self, filename: str) -> Optional[bytes]:
        """Get raw bytes from S3."""
        try:
            response = self.s3_client.get_object(Bucket=settings.AWS_BUCKET_NAME, Key=self._get_key(filename))
            return response["Body"].read()
        except Exception as e:
            logger.error(f"Error reading from S3 file {filename}: {e}")
            return None

    def set(self, filename: str, data: Any) -> None:
        """Store raw data in S3."""
        try:
            if isinstance(data, bytes):
                content = data
            elif isinstance(data, str):
                content = data.encode("utf-8")
            elif isinstance(data, (dict, list)):
                content = json.dumps(data, indent=2, ensure_ascii=False).encode("utf-8")
            else:
                content = str(data).encode("utf-8")

            self.s3_client.put_object(
                Bucket=settings.AWS_BUCKET_NAME,
                Key=self._get_key(filename),
                Body=content,
                ContentType="application/octet-stream",  # Generic binary type
            )
        except Exception as e:
            logger.error(f"Error writing to S3 file {filename}: {e}")
            raise

    def delete(self, filename: str) -> None:
        """Delete data from S3."""
        try:
            self.s3_client.delete_object(Bucket=settings.AWS_BUCKET_NAME, Key=self._get_key(filename))
        except Exception as e:
            logger.error(f"Error deleting S3 file {filename}: {e}")


def get_storage_backend(prefix_path: str = "") -> StorageBackend:
    """Get the appropriate storage backend based on environment."""
    if hasattr(settings, "ENVIRONMENT") and settings.ENVIRONMENT != "development":
        return S3StorageBackend(prefix_path)

    return LocalStorageBackend(prefix_path)
