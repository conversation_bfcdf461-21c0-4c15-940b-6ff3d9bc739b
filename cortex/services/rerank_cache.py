import hashlib
import json
import logging
from datetime import timedelta
from typing import List, Optional, Union

from django.conf import settings
from django.core.cache import cache
from django.utils import timezone

logger = logging.getLogger(__name__)

# Rerank cache configuration with environment-based overrides
RERANK_CACHE_TIMEOUT = getattr(
    settings, "RER<PERSON>K_CACHE_TIMEOUT", 2 * 60 * 60  # Default 2 hours
)

RERANK_CACHE_PREFIX = getattr(settings, "RERANK_CACHE_PREFIX", "rerank")

# Whether to enable caching (can be disabled for debugging)
RERANK_CACHE_ENABLED = getattr(settings, "RERANK_CACHE_ENABLED", False)

# Maximum size of query or node content to include in cache key generation
# This prevents extremely large cache keys (Note: now using node IDs instead of content)
MAX_CACHE_KEY_CONTENT_LENGTH = getattr(
    settings,
    "RERANK_CACHE_MAX_CONTENT_LENGTH",
    10000,  # 10KB (legacy, kept for query truncation)
)

# Whether to include node metadata in cache key generation
# Setting this to False will create more generic cache keys but may cause incorrect hits
INCLUDE_METADATA_IN_CACHE_KEY = getattr(
    settings, "RERANK_CACHE_INCLUDE_METADATA", True
)

# Metadata fields to include in cache key (only used if INCLUDE_METADATA_IN_CACHE_KEY is True)
CACHE_KEY_METADATA_FIELDS = getattr(
    settings,
    "RERANK_CACHE_METADATA_FIELDS",
    ["data_source_id", "document_id", "chunk_index"],
)

# Whether to compress cached data (useful for large result sets)
COMPRESS_CACHED_DATA = getattr(settings, "RERANK_CACHE_COMPRESS", False)

# Log level for cache operations (DEBUG, INFO, WARNING, ERROR)
CACHE_LOG_LEVEL = getattr(settings, "RERANK_CACHE_LOG_LEVEL", "INFO")

# Set the cache logger level based on configuration
cache_logger = logging.getLogger(__name__)
cache_logger.setLevel(getattr(logging, CACHE_LOG_LEVEL.upper(), logging.INFO))


def _generate_cache_key(
    query: str,
    nodes: List,
    reranker_type: str,
    top_n: int,
    kb_uuid: str,
) -> str:
    """
    Generate a deterministic cache key based on query, nodes content, reranking parameters, and knowledge base UUID.

    Args:
        query: The search query
        nodes: List of nodes/documents to rerank
        reranker_type: Type of reranker (e.g., 'cohere', 'llm')
        top_n: Number of top results to return
        kb_uuid: Knowledge base UUID to scope the cache

    Returns:
        A unique cache key string
    """
    # Create a hash of the node IDs and metadata
    node_data = []
    for node in nodes:
        node_data.append(node.node_id)

    # Create cache key components
    cache_data = {
        "query": query.strip().lower(),
        "nodes": node_data,
        "reranker_type": reranker_type,
        "top_n": top_n,
    }

    # Create deterministic hash
    cache_str = json.dumps(cache_data, sort_keys=True, default=str)
    cache_hash = hashlib.sha256(cache_str.encode("utf-8")).hexdigest()

    return f"{RERANK_CACHE_PREFIX}:{kb_uuid}:{cache_hash}"


def get_cached_rerank_result(
    query: str,
    nodes: List,
    reranker_type: str,
    top_n: int,
    kb_uuid: str,
) -> Optional[List]:
    """
    Retrieve cached reranking results if available.

    Args:
        query: The search query
        nodes: List of nodes/documents to rerank
        reranker_type: Type of reranker (e.g., 'cohere', 'llm')
        top_n: Number of top results to return
        kb_uuid: Knowledge base UUID to scope the cache

    Returns:
        Cached reranked nodes if available, None otherwise
    """
    if not nodes or not RERANK_CACHE_ENABLED:
        return None

    try:
        cache_key = _generate_cache_key(
            query,
            nodes,
            reranker_type,
            top_n,
            kb_uuid,
        )
        cached_result = cache.get(cache_key)

        if cached_result is not None:
            cache_logger.info(
                "Cache HIT for rerank query '%s' with %d nodes using %s reranker in KB %s",
                query[:50],
                len(nodes),
                reranker_type,
                kb_uuid,
            )
            # Check if cached result has expiry metadata
            if isinstance(cached_result, dict) and "data" in cached_result:
                cached_at = cached_result.get("cached_at")
                if cached_at:
                    cache_age = (timezone.now() - cached_at).total_seconds()
                    cache_logger.debug("Cache age: %.2f seconds", cache_age)
                return cached_result["data"]
            else:
                # Legacy cache format (backwards compatibility)
                return cached_result
        else:
            cache_logger.debug(
                "Cache MISS for rerank query '%s' with %d nodes using %s reranker in KB %s",
                query[:50],
                len(nodes),
                reranker_type,
                kb_uuid,
            )
            return None

    except Exception as e:
        logger.warning("Error retrieving from rerank cache: %s", str(e))
        return None


def cache_rerank_result(
    query: str,
    nodes: List,
    reranked_nodes: List,
    reranker_type: str,
    top_n: int,
    kb_uuid: str,
) -> bool:
    """
    Cache reranking results with metadata.

    Args:
        query: The search query
        nodes: Original list of nodes/documents that were reranked
        reranked_nodes: The reranked results to cache
        reranker_type: Type of reranker (e.g., 'cohere', 'llm')
        top_n: Number of top results returned
        kb_uuid: Knowledge base UUID to scope the cache

    Returns:
        True if caching was successful, False otherwise
    """
    if not nodes or not reranked_nodes or not RERANK_CACHE_ENABLED:
        return False

    try:
        cache_key = _generate_cache_key(
            query,
            nodes,
            reranker_type,
            top_n,
            kb_uuid,
        )

        # Store with metadata for better cache management
        cache_value = {
            "data": reranked_nodes,
            "cached_at": timezone.now(),
            "reranker_type": reranker_type,
            "top_n": top_n,
            "kb_uuid": kb_uuid,
            "original_count": len(nodes),
            "reranked_count": len(reranked_nodes),
        }

        cache.set(cache_key, cache_value, timeout=RERANK_CACHE_TIMEOUT)

        cache_logger.info(
            "Cached rerank result for query '%s' with %d->%d nodes using %s reranker in KB %s (key: %s)",
            query[:50],
            len(nodes),
            len(reranked_nodes),
            reranker_type,
            kb_uuid,
            cache_key[:20],
        )
        return True

    except Exception as e:
        logger.warning("Error caching rerank result: %s", str(e))
        return False


def invalidate_rerank_cache(
    kb_uuid: Optional[str] = None, pattern: Optional[str] = None
) -> int:
    """
    Invalidate reranking cache entries.

    Args:
        kb_uuid: Optional knowledge base UUID to invalidate cache for specific KB
        pattern: Optional pattern to match cache keys. If None, invalidates all rerank cache.

    Returns:
        Number of cache entries invalidated
    """
    try:
        # Note: Django's cache doesn't have a native way to delete by pattern
        # This is a simplified implementation. For production with Redis,
        # you might want to use redis client directly for pattern-based deletion
        if kb_uuid or pattern:
            logger.warning(
                "Pattern-based cache invalidation not fully implemented. "
                "Consider using Redis client directly for complex patterns."
            )
            if kb_uuid:
                logger.info(
                    "Rerank cache invalidation requested for KB %s (pattern: %s)",
                    kb_uuid,
                    pattern,
                )
            return 0
        else:
            # For now, we'll just log the invalidation request
            # In a production environment, you might implement this using Redis SCAN
            logger.info(
                "Rerank cache invalidation requested (pattern: %s)", pattern
            )
            return 0

    except Exception as e:
        logger.error("Error invalidating rerank cache: %s", str(e))
        return 0


def get_cache_stats() -> dict:
    """
    Get cache statistics for monitoring and debugging.

    Returns:
        Dictionary with cache statistics
    """
    try:
        # This is a basic implementation. For detailed stats,
        # you might want to implement custom cache key tracking
        return {
            "cache_prefix": RERANK_CACHE_PREFIX,
            "timeout_seconds": RERANK_CACHE_TIMEOUT,
            "timeout_hours": RERANK_CACHE_TIMEOUT / 3600,
            "enabled": RERANK_CACHE_ENABLED,
            "max_content_length": MAX_CACHE_KEY_CONTENT_LENGTH,
            "include_metadata": INCLUDE_METADATA_IN_CACHE_KEY,
            "metadata_fields": CACHE_KEY_METADATA_FIELDS,
            "log_level": CACHE_LOG_LEVEL,
        }
    except Exception as e:
        logger.error("Error getting cache stats: %s", str(e))
        return {}
