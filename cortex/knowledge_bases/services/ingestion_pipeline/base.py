import logging
from typing import Sequence

from django.conf import settings
from llama_index.core.node_parser import (
    HierarchicalNodePars<PERSON>,
    <PERSON>de<PERSON><PERSON><PERSON>,
    SemanticSplitterNodeParser,
    SentenceSplitter,
)
from llama_index.core.schema import BaseNode, Document

from knowledge_bases.models import DataSource
from knowledge_bases.models import Document as DataSourceDocument
from knowledge_bases.models import DocumentChunk
from services import get_cohere_embedding_model
from utils.enum import ChunkingStrategy, DataSourceStatus, DocumentStatus

logger = logging.getLogger(__name__)


class BaseIngestionPipeline:
    """
    Base class for ingestion pipelines.
    """

    def __init__(self, data_source: DataSource) -> None:
        self.data_source = data_source
        self.knowledge_base = self.data_source.knowledge_base
        self.account = self.knowledge_base.account
        self.embed_model = get_cohere_embedding_model()

    def _update_data_source_content(
        self, documents: list[Document], doc_to_nodes: dict
    ) -> None:
        """
        Update the data source content with the topics assigned to the documents and
        the scraped content.
        """
        logger.info(
            "Updating data source content for %s documents", len(documents)
        )
        logger.info("Doc to nodes: %s", len(doc_to_nodes))
        for doc in documents:
            documents = self.data_source.documents.get(id=doc.metadata["id"])
            logger.info(
                "Updating data source content for document: %s", documents
            )
            documents.raw_content = doc.text
            # update the title of the content
            documents.title = doc.metadata.get("title", "Untitled Document")
            documents.status = DocumentStatus.PROCESSED
            # get all the topics assigned to the document
            nodes = doc_to_nodes.get(doc.node_id, [])
            if not nodes:
                logger.info(
                    "No topics assigned to document: %s", documents.title
                )
            topic_ids = []
            current_chunk_number = 1
            for node in nodes:
                document_chunk = DocumentChunk(
                    document=documents,
                    content=node.text,
                    chunk_number=current_chunk_number,
                    external_id=node.id_,
                    metadata=node.metadata,
                )
                document_chunk.save()
                current_chunk_number += 1
                topic_ids.extend(
                    node.metadata[f"{self.account.uuid}__topic_ids"]
                )
            documents.metadata[f"{self.account.uuid}__topic_ids"] = list(
                set(topic_ids)
            )
            documents.save(
                update_fields=["metadata", "title", "raw_content", "status"]
            )
            logger.info(
                "Updated data source content for document: %s", documents
            )

        # Update the status of the documents to be processed
        self.data_source.status = DataSourceStatus.COMPLETED
        self.data_source.save(update_fields=["status"])

    def _parse_document(self, doc: DataSourceDocument):
        """
        Parse a document into a list of nodes.
        """
        raise NotImplementedError

    def _get_documents_to_ingest(self) -> list[Document]:
        """
        Get the documents to ingest for the data source.
        """
        raise NotImplementedError

    def _get_node_parser(self) -> NodeParser:
        """
        Get the appropriate node parser based on the knowledge base's chunking strategy.

        Returns:
            NodeParser: The configured node parser
        """
        if self.data_source.chunking_strategy != ChunkingStrategy.DEFAULT:
            strategy = self.data_source.chunking_strategy
            config = self.data_source.chunking_config or {}
        elif self.knowledge_base.chunking_strategy != ChunkingStrategy.DEFAULT:
            strategy = self.knowledge_base.chunking_strategy
            config = self.knowledge_base.chunking_config or {}
        elif self.account.chunking_strategy != ChunkingStrategy.DEFAULT:
            strategy = self.account.chunking_strategy
            config = self.account.chunking_config or {}
        else:
            strategy = ChunkingStrategy.DEFAULT
            config = {}

        # Default chunk size and overlap if not specified in config
        chunk_size = config.get("chunk_size", 1024)
        chunk_overlap = config.get("chunk_overlap", 20)

        if strategy == ChunkingStrategy.DEFAULT:
            strategy = settings.DEFAULT_CHUNKING_STRATEGY

        if strategy == ChunkingStrategy.FIXED_SIZE:
            logger.info(
                "Using SentenceSplitter with chunk size as %s and overlap as %s",
                chunk_size,
                chunk_overlap,
            )
            return SentenceSplitter(
                chunk_size=chunk_size,
                chunk_overlap=chunk_overlap,
                include_metadata=True,
                include_prev_next_rel=True,
            )

        elif strategy == ChunkingStrategy.HIERARCHICAL:
            # Get hierarchical chunk sizes or create default ones
            chunk_sizes = config.get(
                "chunk_sizes", [chunk_size, chunk_size // 2, chunk_size // 4]
            )

            # Create a parser for each level
            parsers = {}
            for i, size in enumerate(chunk_sizes):
                parser_id = f"level_{i}"
                parsers[parser_id] = SentenceSplitter(
                    chunk_size=size,
                    chunk_overlap=min(chunk_overlap, size // 10),
                )

            return HierarchicalNodeParser(
                chunk_sizes=chunk_sizes, node_parser_map=parsers
            )

        elif strategy == ChunkingStrategy.SEMANTIC:
            # Get semantic-specific config options
            buffer_size = config.get("buffer_size", 1)
            breakpoint_percentile_threshold = config.get(
                "breakpoint_percentile_threshold", 95
            )
            logger.info(
                "Using semantic splitter with buffer size: %s, breakpoint percentile threshold: %s",
                buffer_size,
                breakpoint_percentile_threshold,
            )
            return SemanticSplitterNodeParser(
                buffer_size=buffer_size,
                breakpoint_percentile_threshold=breakpoint_percentile_threshold,
                embed_model=self.embed_model,
            )

        elif strategy == ChunkingStrategy.NONE:
            # Return a pass-through parser for the NONE strategy
            class PassThroughParser(NodeParser):
                def _parse_nodes(
                    self,
                    nodes: Sequence[BaseNode],
                    show_progress: bool = True,
                    **kwargs: any,
                ) -> list[BaseNode]:
                    logger.info("Total nodes to parse: %s", len(nodes))
                    return nodes

                def get_nodes_from_documents(
                    self,
                    documents: Sequence[Document],
                    show_progress: bool = True,
                    **kwargs: any,
                ) -> list[BaseNode]:
                    logger.info("Total documents to parse: %s", len(documents))
                    # Documents already inherit from BaseNode, but we need to return
                    # them as a proper list[BaseNode] to match the interface
                    return list(documents)

            return PassThroughParser()

        else:
            # Default to semantic splitter if strategy is not recognized
            return SemanticSplitterNodeParser(embed_model=self.embed_model)

    def run(self):
        """
        Execute the ingestion pipeline.
        This method should be implemented by subclasses.

        Raises:
            NotImplementedError: When not implemented by a subclass
        """
        raise NotImplementedError
