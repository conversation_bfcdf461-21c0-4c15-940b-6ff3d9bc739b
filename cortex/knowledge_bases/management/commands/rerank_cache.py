from django.core.cache import cache
from django.core.management.base import BaseCommand

from services.rerank_cache import (
    RERANK_CACHE_PREFIX,
)


class Command(BaseCommand):
    help = "Clear rerank cache entries"

    def add_arguments(self, parser):
        parser.add_argument(
            "--clear",
            action="store_true",
            help="Clear all rerank cache entries",
        )

    def handle(self, *args, **options):
        if options["clear"]:
            self.clear_cache()
        else:
            self.stdout.write(
                self.style.WARNING(
                    "Use --clear to clear all rerank cache entries"
                )
            )

    def clear_cache(self):
        """Clear all rerank cache entries"""
        self.stdout.write("Clearing rerank cache...")

        try:
            from django.core.cache.backends.redis import RedisCache

            if isinstance(cache, RedisCache):
                # Get Redis client and delete by pattern
                client = cache._cache.get_client(write=True)
                keys = client.keys(f"{RERANK_CACHE_PREFIX}:*")
                if keys:
                    deleted_count = client.delete(*keys)
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"Successfully deleted {deleted_count} cache entries"
                        )
                    )
                else:
                    self.stdout.write("No rerank cache entries found")
            else:
                # Fallback for other cache backends
                self.stdout.write(
                    self.style.WARNING(
                        "Cache clearing not fully supported for this cache backend"
                    )
                )
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error clearing cache: {e}"))
