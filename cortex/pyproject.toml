[tool.poetry]
name = "cortex"
version = "0.1.0"
description = "AI Services"
authors = ["<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11" # CHANGED: Downgraded to Python 3.11 for compatibility
django = "^5.1.6"
django-environ = "^0.12.0"
adrf = "^0.1.9"
django-json-widget = "^2.0.1"
uvicorn = "^0.34.0"
gunicorn = "^23.0.0"
python-socketio = "^5.12.1"
whitenoise = "^6.9.0"
celery = "^5.4.0"
psycopg2-binary = "^2.9.10"
asyncpg = "^0.30.0"
sentry-sdk = "^2.22.0"
structlog = "^25.1.0"
opensearch-py = "^2.8.0"
django-redis = "^5.4.0"
langfuse = "^2.59.7"
html2text = "^2024.2.26"
docx2txt = "^0.8"
markdown2 = "^2.5.3"
llama-index = "^0.12.22"
llama-index-llms-bedrock-converse = "^0.4.8"
llama-index-postprocessor-bedrock-rerank = "^0.3.2"
llama-index-embeddings-bedrock = "^0.5.0"
llama-index-vector-stores-opensearch = "^0.5.2"
llama-index-storage-docstore-postgres = "^0.3.0"
llama-index-readers-web = "^0.3.7"
llama-index-readers-notion = "^0.3.0"
llama-index-readers-google = "^0.6.0"
# pymupdf = "^1.24.0"  # Temporarily disabled to avoid installation issues
google-generativeai = "^0.7.0"
streamlit = "^1.45.1"
cohere = "^5.2.5"
# llama-index-embeddings-cohere = "^0.1.0"  # Temporarily commented out due to compatibility issues
pymupdf = "^1.26.1"

[tool.poetry.group.dev.dependencies]
black = "^25.1.0"
isort = "^6.0.1"
ruff = "^0.9.9"
flake8 = "^7.1.2"
pytest-django = "^4.10.0"
pre-commit = "^4.1.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
# Allow lines to be as long as 120.
line-length = 120
target-version = "py311" # CHANGED: Align with Python 3.11
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    ".venv",
    "libs"
]

[tool.isort]
profile = "black"
sections = ["FUTURE","STDLIB","THIRDPARTY","FIRSTPARTY","LOCALFOLDER"]
line_length = 120

[tool.black]
line-length = 120
exclude = '''
/(
    \.git
  | \.mypy_cache
  | \.pytest_cache
  | \.tox
  | \.venv
  | __pycache__
  | build
  | dist
  | libs
)/
'''