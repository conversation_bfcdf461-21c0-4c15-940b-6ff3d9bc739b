"""
Django settings for cortex project.

Generated by 'django-admin startproject' using Django 5.1.1.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""

import os
from pathlib import Path

import environ

from cortex.logging import BASE_LOGGING_CONFIG, DEV_LOGGING_CONFIG
from utils.enum import ChunkingStrategy

# django-environ is a package that allows you to utilize 12factor inspired environment variables
# in your settings, securely store configuration variables and switch configurations easily.
# More about django-environ here: https://django-environ.readthedocs.io/en/latest/
ENV = environ.Env()

# Load all the environment variables
# environ.Env.read_env()


# Get the environment setting (development, staging, production, etc.)
ENVIRONMENT = ENV.str("ENVIRONMENT", default="development")
IS_PROD = ENVIRONMENT == "production"

# Use different color codes for different environments (just for visual differentiation)
ENVIRONMENT_COLOR = "#808080"
if IS_PROD:
    ENVIRONMENT_COLOR = "#FF0000"
elif ENVIRONMENT == "staging":
    ENVIRONMENT_COLOR = "#F4C430"

# BASE_DIR is a constant in Django that gets the root directory of the project
# Here it's one level up the directory where settings.py resides
BASE_DIR = Path(__file__).resolve().parent.parent
# Load .env file from the project root (one level up from BASE_DIR)
PROJECT_ROOT = BASE_DIR.parent
environ.Env.read_env(os.path.join(PROJECT_ROOT, '.env'))
# Debug mode: development environment by default, can be changed by setting DEBUG environment variable
DEBUG = ENV.bool("DEBUG", default=True)

# SECRET_KEY is a secret unique key for your particular Django installation.
# This is used to provide cryptographic signing, and should be set to a unique, unpredictable value.
SECRET_KEY = ENV.str("SECRET_KEY")

# A list of strings representing the host/domain names that this Django site can serve.
ALLOWED_HOSTS = ENV.list("ALLOWED_HOSTS", default=["localhost", "0d2e-125-23-34-124.ngrok-free.app"])

# The URL on which the admin section of the site will be served.
ADMIN_ENDPOINT = ENV.str("ADMIN_ENDPOINT", default="admin/")

# SESSION_COOKIE_SECURE is a setting that determines whether to use a secure cookie for the session cookie.
# If set to True, the cookie will be marked as "secure", which means it will only be sent over HTTPS connections.
# It's recommended to set this to True in production to ensure the confidentiality of session data.
SESSION_COOKIE_SECURE = ENV.bool("SESSION_COOKIE_SECURE", default=True)

REDIS_URL = ENV.str("REDIS_URL", default=None)
DATABASE_NAME = ENV.str("DATABASE_NAME", default=None)
DATABASE_USER = ENV.str("DATABASE_USER", default=None)
DATABASE_PASSWORD = ENV.str("DATABASE_PASSWORD", default=None)
DATABASE_HOST = ENV.str("DATABASE_HOST", default=None)
DATABASE_PORT = ENV.int("DATABASE_PORT", default=5432)

# Application definition
REST_FRAMEWORK_APPS = [
    "adrf",  # Async Django REST Framework
]

THIRD_PARTY_APPS = [
    "django_json_widget",  # Django JSON Widget
]

PROJECT_APPS = [
    "accounts",  # Accounts
    "knowledge_bases",  # Knowledge Bases
]

INSTALLED_APPS = (
    [
        "cortex.admin.AdminConfig",  # Admin Config (Custom Admin)
        "django.contrib.auth",  # Django Authentication
        "django.contrib.contenttypes",  # Django Content Types
        "django.contrib.sessions",  # Django Sessions
        "django.contrib.messages",  # Django Messages
        "django.contrib.staticfiles",  # Django Static Files
    ]
    + REST_FRAMEWORK_APPS
    + THIRD_PARTY_APPS
    + PROJECT_APPS
)

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

# HEALTH CHECKS
# ------------------------------------------------------------------------------
# Kubernetes Health Checks in Django
# https://www.ianlewis.org/en/kubernetes-health-checks-django
# Add this to the beginning of your MIDDLEWARE_CLASSES to add health checks to your app.
# Putting it at the beginning of MIDDLEWARE_CLASSES ensures it gets run before
# other Middleware classes that might access the database.
MIDDLEWARE.insert(0, "cortex.middlewares.HealthCheckMiddleware")  # noqa F405

ROOT_URLCONF = "cortex.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [
            os.path.join(BASE_DIR, "knowledge_bases", "templates"),
        ],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "cortex.wsgi.application"


# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": DATABASE_NAME,
        "USER": DATABASE_USER,
        "PASSWORD": DATABASE_PASSWORD,
        "HOST": DATABASE_HOST,
        "PORT": DATABASE_PORT,
    }
}


# Cache configuration
# https://docs.djangoproject.com/en/5.1/topics/cache/

CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.redis.RedisCache",
        "LOCATION": REDIS_URL,
    }
}

# OpenSearch settings
OPENSEARCH_URL = ENV.str("OPENSEARCH_URL", default="http://localhost:9200")

# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/
STATIC_ROOT = os.path.join(BASE_DIR, "staticfiles")
STATIC_URL = "/static/"
STORAGES = {
    "default": {
        "BACKEND": "whitenoise.storage.CompressedStaticFilesStorage",
    },
    "staticfiles": {
        "BACKEND": "whitenoise.storage.CompressedStaticFilesStorage",
    },
}

# Extra places for collectstatic to find static files.
STATICFILES_DIRS = (os.path.join(BASE_DIR, "static"),)

if DEBUG:
    MEDIA_ROOT = os.path.join(BASE_DIR, "media")
    MEDIA_URL = "/media/"

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# Logging settings
LOGGING = BASE_LOGGING_CONFIG
if DEBUG:
    LOGGING = DEV_LOGGING_CONFIG

# This section configures Redis URL and settings for Celery.
# Celery is a Distributed Task Queue, which can be used to offload long-running tasks and schedule tasks.
CELERY_BROKER_URL = ENV.str("CELERY_BROKER_URL", default=REDIS_URL)
CELERY_BACKEND = ENV.str("CELERY_BACKEND", default=REDIS_URL)
CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP = True
# Where your tasks are located
CELERY_IMPORTS = [
    "knowledge_bases.tasks",
]

# Additional Celery configuration for production use
CELERY_TASK_ACKS_LATE = True
CELERY_WORKER_PREFETCH_MULTIPLIER = 1
CELERY_TASK_REJECT_ON_WORKER_LOST = True
CELERY_TASK_TIME_LIMIT = 60 * 60  # 1 hour
CELERY_TASK_SOFT_TIME_LIMIT = 30 * 60  # 30 minutes

# langfuse settings
ENABLE_LANGFUSE = ENV.bool("ENABLE_LANGFUSE", default=False)
LANGFUSE_SECRET_KEY = ENV.str("LANGFUSE_SECRET_KEY", default=None)
LANGFUSE_PUBLIC_KEY = ENV.str("LANGFUSE_PUBLIC_KEY", default=None)
LANGFUSE_HOST = ENV.str("LANGFUSE_HOST", default=None)

REST_FRAMEWORK = {
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.LimitOffsetPagination",
    "PAGE_SIZE": 4,
}

# opensearch settings
OPENSEARCH_HOST = ENV.str("OPENSEARCH_HOST", default="opensearch")
OPENSEARCH_PORT = ENV.int("OPENSEARCH_PORT", default=9200)
OPENSEARCH_REGION = ENV.str("OPENSEARCH_REGION", default="")
OPENSEARCH_AUTH = ENV.str("OPENSEARCH_AUTH", default=None)

# AWS Bedrock settings
BEDROCK_REGION = ENV.str("BEDROCK_REGION", default="us-west-2")
BEDROCK_ACCESS_KEY_ID = ENV.str("BEDROCK_ACCESS_KEY_ID")
BEDROCK_SECRET_ACCESS_KEY = ENV.str("BEDROCK_SECRET_ACCESS_KEY")
BEDROCK_COMPLETION_API_TOKEN_LIMIT = ENV.int("BEDROCK_COMPLETION_API_TOKEN_LIMIT", default=3000)

# Chunking strategy
DEFAULT_CHUNKING_STRATEGY = ENV.str("DEFAULT_CHUNKING_STRATEGY", default=ChunkingStrategy.SEMANTIC)

# Default LLM configuration
DEFAULT_LLM = ENV.str("DEFAULT_LLM", default="openai")
