from __future__ import absolute_import, unicode_literals

from cortex.celery import app as celery_app

__all__ = ("celery_app",)

# Configure default LLM based on settings
def configure_default_llm():
    """Configure the default LLM for LlamaIndex based on Django settings"""
    try:
        from django.conf import settings
        from llama_index.core import Settings

        if hasattr(settings, 'DEFAULT_LLM') and settings.DEFAULT_LLM == 'claude':
            from services.claude import get_claude_llm
            Settings.llm = get_claude_llm()
    except Exception:
        # Ignore errors during initialization
        pass

# Configure LLM on import
configure_default_llm()
